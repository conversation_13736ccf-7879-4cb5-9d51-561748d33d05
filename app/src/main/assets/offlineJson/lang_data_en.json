[{"key": "beginner_level_name", "value": "<PERSON><PERSON><PERSON>"}, {"key": "intermediate_level_name", "value": "Intermediate"}, {"key": "advanced_level_name", "value": "Advanced"}, {"key": "abs_category_name", "value": "Abs"}, {"key": "bear_crawls_exercise_name", "value": "Bear Crawls"}, {"key": "bear_crawls_body", "value": "Abs, Chest, Arms , Legs"}, {"key": "bicycles_ex_name", "value": "Bicycles"}, {"key": "bicycles_body", "value": "Abs"}, {"key": "bicycles_eq", "value": "Yoga Mat"}, {"key": "crunches_name", "value": "Crunches"}, {"key": "crunches_body", "value": "Abs"}, {"key": "crunches_eq", "value": "Yoga Mat"}, {"key": "downward_facing_dogs_name", "value": "Downward-Facing Dogs"}, {"key": "downward_facing_dogs_body", "value": "Abs, Shoulders, Arms"}, {"key": "downward_facing_dogs_eq", "value": "Yoga Mat"}, {"key": "heel_push_crunches_name", "value": "Hell-Push Crunches"}, {"key": "heel_push_crunches_body", "value": "Abs"}, {"key": "heel_push_crunches_eq", "value": "Yoga Mat"}, {"key": "high_knees_name", "value": "High Knees"}, {"key": "high_knees_body", "value": "<PERSON><PERSON>, Legs"}, {"key": "knee_up_crunches_name", "value": "Knee-UP Crunches"}, {"key": "knee_up_crunches_body", "value": "Abs"}, {"key": "knee_up_crunches_eq", "value": "Yoga Mat"}, {"key": "leg_raises_name", "value": "Leg Raises"}, {"key": "leg_raises_body", "value": "Abs"}, {"key": "leg_raises_eq", "value": "Yoga Mat"}, {"key": "leg_cycle__russian_twists_name", "value": "Leg Cycle -Russian Twists"}, {"key": "leg_cycle__russian_twists_body", "value": "Abs"}, {"key": "leg_cycle__russian_twists_eq", "value": "Yoga Mat"}, {"key": "long_arm_crunches_name", "value": "Long-Arm Crunches"}, {"key": "long_arm_crunches_body", "value": "Abs,  Shoulders"}, {"key": "long_arm_crunches_eq", "value": "Yoga Mat"}, {"key": "mountain_climbers_name", "value": "Mountain Climbers"}, {"key": "mountain_climbers_body", "value": "Abs"}, {"key": "mountain_climbers_eq", "value": "Yoga Mat"}, {"key": "opposite_arm_and_leg_crunches_name", "value": "Opposite Arm & Leg Crunches"}, {"key": "opposite_arm_and_leg_crunches_body", "value": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>"}, {"key": "opposite_arm_and_leg_crunches_eq", "value": "Yoga Mat"}, {"key": "plank_name", "value": "Plank"}, {"key": "plank_body", "value": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>"}, {"key": "plank_eq", "value": "Yoga Mat"}, {"key": "plank_on_hands_name", "value": "Plank On Hands"}, {"key": "plank_on_hands_body", "value": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>"}, {"key": "plank_on_hands_eq", "value": "Yoga Mat"}, {"key": "plank_push_ups_name", "value": "Plank Push-Ups"}, {"key": "plank_push_ups_body", "value": "Abs, Arms, Shoulders, Chest"}, {"key": "plank_push_ups_eq", "value": "Yoga Mat"}, {"key": "side_crunches_name", "value": "Side Crunches"}, {"key": "side_crunches_body", "value": "Abs"}, {"key": "side_crunches_eq", "value": "Yoga Mat"}, {"key": "side_plank_name", "value": "Side Plank"}, {"key": "side_plank_body", "value": "<PERSON><PERSON>, <PERSON>t"}, {"key": "side_plank_eq", "value": "Yoga Mat"}, {"key": "sit_ups_name", "value": "Sit-Ups"}, {"key": "sit_ups_body", "value": "Abs"}, {"key": "sit_ups_eq", "value": "Yoga Mat"}, {"key": "spider_crawls_name", "value": "Spider Crawls"}, {"key": "spider_crawls_body", "value": "A<PERSON>, Butt, Back, Arms, Chest, Shoulders"}, {"key": "spider_crawls_eq", "value": "Yoga Mat"}, {"key": "vertical_leg_crunches_name", "value": "Vertical-<PERSON><PERSON>"}, {"key": "vertical_leg_crunches_body", "value": "Abs"}, {"key": "vertical_leg_crunches_eq", "value": "Yoga Mat"}, {"key": "back_extensions_name", "value": "Back Extensions"}, {"key": "back_extensions_body", "value": "Arms, Chest, Shoulders, Back"}, {"key": "back_extensions_eq", "value": "Yoga Mat"}, {"key": "box_push_ups_name", "value": "Box Push-Ups"}, {"key": "box_push_ups_body", "value": "Arms, Chest, Shoulders"}, {"key": "box_push_ups_eq", "value": "Yoga Mat"}, {"key": "close_arm_push_ups_name", "value": "Close-Arm Push-Ups"}, {"key": "close_arm_push_ups_body", "value": "Arms, Chest, Shoulders"}, {"key": "close_arm_push_ups_eq", "value": "Yoga Mat"}, {"key": "cobras_name", "value": "Cobras"}, {"key": "cobras_body", "value": "Arms, Chest, Shoulders, Back"}, {"key": "cobras_eq", "value": "Yoga Mat"}, {"key": "decline_push_ups_name", "value": "Decline Push-Ups"}, {"key": "decline_push_ups_body", "value": "Arms, Chest, Shoulders"}, {"key": "decline_push_ups_eq", "value": "Chair or Bench"}, {"key": "dip_and_kick_outs_name", "value": "Dip & Kick- Outs"}, {"key": "dip_and_kick_outs_body", "value": "Arms, Abs"}, {"key": "dip_and_kick_outs_eq", "value": "Chair or Bench"}, {"key": "floor_tricep_dips_name", "value": "Floor Tricep Dips"}, {"key": "floor_tricep_dips_body", "value": "Arms"}, {"key": "floor_tricep_dips_eq", "value": "Chair or Bench"}, {"key": "hindu_push_ups_name", "value": "Hindu Push-Ups"}, {"key": "hindu_push_ups_body", "value": "Arms, Chest, Shoulders, Back"}, {"key": "hindu_push_ups_eq", "value": "Yoga Mat"}, {"key": "incline_push_ups_name", "value": "Incline Push-Ups"}, {"key": "incline_push_ups_body", "value": "Arms, Chest, Shoulders"}, {"key": "incline_push_ups_eq", "value": "Chair or Bench"}, {"key": "knee_diamond_push_ups_name", "value": "<PERSON><PERSON> Push-Ups"}, {"key": "knee_diamond_push_ups_body", "value": "Arms, Shoulders, Chest"}, {"key": "knee_diamond_push_ups_eq", "value": "Yoga Mat"}, {"key": "pike_push_ups_name", "value": "<PERSON> Push-Ups"}, {"key": "pike_push_ups_body", "value": "Arms, Shoulders"}, {"key": "pike_push_ups_eq", "value": "Yoga Mat"}, {"key": "power_punches_name", "value": "Power Punches"}, {"key": "power_punches_body", "value": "Arms, Chest, Shoulders"}, {"key": "push_ups_name", "value": "Push-Ups"}, {"key": "push_ups_body", "value": "Arms, Shoulders, Chest"}, {"key": "push_ups_eq", "value": "Yoga Mat"}, {"key": "side_rotation_push_ups_name", "value": "Side-Rotation Push-Ups"}, {"key": "side_rotation_push_ups_body", "value": "Arms, Abs, Back"}, {"key": "staggered_push_ups_name", "value": "Staggered Push-Ups"}, {"key": "staggered_push_ups_body", "value": "Arms, Chest, Shoulders"}, {"key": "staggered_push_ups_eq", "value": "Yoga Mat"}, {"key": "v_push_ups_name", "value": "V Push-Ups"}, {"key": "v_push_ups_body", "value": "Arms, Shoulders"}, {"key": "v_push_ups_eq", "value": "Yoga Mat"}, {"key": "wall_push_ups_name", "value": "Wall Push-Ups"}, {"key": "wall_push_ups_body", "value": "Arms, Chest, Shoulders"}, {"key": "wide_arm_push_ups_name", "value": "Wide-Arm Push-Ups"}, {"key": "wide_arm_push_ups_body", "value": "Arms, Shoulders, Chest"}, {"key": "wide_arm_push_ups_eq", "value": "Yoga Mat"}, {"key": "ballet_twist_name", "value": "Ballet Twist"}, {"key": "ballet_twist_body", "value": "But<PERSON>, <PERSON>, <PERSON>bs"}, {"key": "donkey_kicks_name", "value": "Donkey Kicks"}, {"key": "donkey_kicks_body", "value": "Butt, Legs"}, {"key": "donkey_kicks_eq", "value": "Yoga Mat"}, {"key": "glute_bridges_name", "value": "Glut<PERSON>"}, {"key": "glute_bridges_body", "value": "<PERSON><PERSON>, <PERSON><PERSON>, Back"}, {"key": "glute_bridges_eq", "value": "Yoga Mat"}, {"key": "ice_skaters_name", "value": "Ice Skaters"}, {"key": "ice_skaters_body", "value": "Butt, <PERSON>gs, Abs"}, {"key": "plyo_jacks_name", "value": "Plyo Jacks"}, {"key": "plyo_jacks_body", "value": "Butt, Legs, Shoulders"}, {"key": "posterior_plank_name", "value": "Posterior Plank"}, {"key": "posterior_plank_body", "value": "Butt, <PERSON>gs, Abs"}, {"key": "posterior_plank_eq", "value": "Yoga Mat"}, {"key": "reverse_hypers_name", "value": "Reverse Hypers"}, {"key": "reverse_hypers_body", "value": "Butt, Legs"}, {"key": "reverse_hypers_eq", "value": "Chair or Bench"}, {"key": "butt_kicks_name", "value": "Butt Kicks"}, {"key": "butt_kicks_body", "value": "Legs"}, {"key": "forward_lunges_name", "value": "Forward Lunges"}, {"key": "forward_lunges_body", "value": "Legs, Butt"}, {"key": "goblet_squats_name", "value": "Goblet Squats"}, {"key": "goblet_squats_body", "value": "Legs, Butt, Chest, Arms"}, {"key": "goblet_squats_eq", "value": "<PERSON><PERSON><PERSON> or <PERSON><PERSON> of Water"}, {"key": "jump_squats_name", "value": "Jump Squats"}, {"key": "jump_squats_body", "value": "Legs, Butt"}, {"key": "lateral_squats_name", "value": "Lateral Squats"}, {"key": "lateral_squats_body", "value": "Legs"}, {"key": "little_pulse_jumps_name", "value": "Little-Pulse Jumps"}, {"key": "little_pulse_jumps_body", "value": "Legs, Butt"}, {"key": "prisoner_squats_name", "value": "Prisoner <PERSON><PERSON><PERSON>"}, {"key": "prisoner_squats_body", "value": "Legs, Butt"}, {"key": "regular_squats_name", "value": "Regular Squats"}, {"key": "regular_squats_body", "value": "Legs, Butt"}, {"key": "reverse_lunge_and_kicks_name", "value": "Reverse Lunge & Kicks"}, {"key": "reverse_lunge_and_kicks_body", "value": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>"}, {"key": "rolling_squats_name", "value": "Rolling Squats"}, {"key": "rolling_squats_body", "value": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>"}, {"key": "rolling_squats_eq", "value": "Yoga Mat"}, {"key": "running_in_place_name", "value": "Running in Place"}, {"key": "running_in_place_body", "value": "Legs"}, {"key": "side_lunges_name", "value": "Side Lunges"}, {"key": "side_lunges_body", "value": "Legs, Butt"}, {"key": "side_to_side_squats_name", "value": "Side-To-Side Squats"}, {"key": "side_to_side_squats_body", "value": "Legs"}, {"key": "single_leg_deadlifts_name", "value": "Single-Leg Deadlifts"}, {"key": "single_leg_deadlifts_body", "value": "<PERSON><PERSON>, Back, Butt"}, {"key": "split_jump_squats_name", "value": "Split-Jump Squats"}, {"key": "split_jump_squats_body", "value": "Legs, Butt"}, {"key": "split_lunge_jumps_name", "value": "Split-Lunge Jumps"}, {"key": "split_lunge_jumps_body", "value": "Legs, Butt"}, {"key": "squat_holds_name", "value": "Squat Holds"}, {"key": "squat_holds_body", "value": "Legs, Butt"}, {"key": "squat_tuck_jumps_name", "value": "Squat Tuck Jumps"}, {"key": "squat_tuck_jumps_body", "value": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>"}, {"key": "sumo_squats_name", "value": "<PERSON><PERSON>"}, {"key": "sumo_squats_body", "value": "Legs, Butt"}, {"key": "sumo_squats_jumps_name", "value": "Sumo-Squats Jumps"}, {"key": "sumo_squats_jumps_body", "value": "Legs, Butt"}, {"key": "tuck_jumps_name", "value": "Tuck Jumps"}, {"key": "tuck_jumps_body", "value": "Legs, Butt"}, {"key": "wide_squats_name", "value": "Wide Squats"}, {"key": "wide_squats_body", "value": "Legs, Butt"}, {"key": "arm_circles_name", "value": "Arm Circles"}, {"key": "arm_circles_body", "value": "Shoulders, <PERSON>"}, {"key": "side_push_ups_name", "value": "Side Push-Ups"}, {"key": "side_push_ups_body", "value": "Shoulders, Arms"}, {"key": "body_strength_superset_name", "value": "Beach Body"}, {"key": "grilled_lemon_dijon_chicken_name", "value": "Grilled Lemon Dijon Chicken Thighs with Arugula Salad"}, {"key": "grilled_lemon_dijon_chicken_ingr", "value": "2 (2 oz.) skinless, boneless chicken thighs \r\n½ tsp salt \r\n¼ tsp ground black pepper \r\n1 tbsp olive oil \r\n2 tsp lemon juice \r\n1 tsp minced garlic \r\ncooking spray \r\n1 tbsp balsamic vinegar \r\n1 tsp honey \r\n1 cup grape tomatoes, halved \r\n1 (5 oz.) package baby arugula"}, {"key": "grilled_lemon_dijon_chicken_steps", "value": "Sprinkle chicken with ¼ teaspoon salt and 1/8 teaspoon pepper. Combine chicken with 1–1.5 teaspoons oil, lemon juice, mustard, and garlic in a bowl, toss to coat. Let stand 4 minutes. Heat a grill pan over medium heat. Coat pan with cooking spray. Add chicken to pan, grill for 5 minutes on each side or until done. Cut chicken into ¼–inch–thick slices. Enjoy!"}, {"key": "orange_honey_sri<PERSON>ha_name", "value": "Orange Honey Sriracha Chicken"}, {"key": "orange_honey_sriracha_ingr", "value": "0.1 kg / 0.25 lb. chicken breast \r\n1/8 tbsp sesame oil \r\n12.5 g / 0.4 oz Sriracha sauce \r\nsalt \r\npepper"}, {"key": "orange_honey_sriracha_steps", "value": "Set your grill or oven to 375°F 190°C. In a sealable plastic bag, add the contents for the marinade and mix together well. Taste the marinade and season it to your liking.  Add the chicken to the marinade and place in the refrigerator, for at least 30 minutes. Then, take the chicken from the refrigerator and place it on the grill. Grill for about 15 to 20 minutes until the juices from the chicken run clear. Enjoy!"}, {"key": "chili_fajitas_name", "value": "<PERSON><PERSON>"}, {"key": "chili_fajitas_ingr", "value": "1 lb. skinless chicken breast \r\n½ green, red, and yellow bell pepper, thinly sliced \r\n⅓ cup water \r\n2 tsp soy sauce \r\n1 dash salt \r\n1 dash black pepper \r\n2 tbsp olive oil \r\n1 large onion, thinly sliced"}, {"key": "chili_fajitas_steps", "value": "Combine all marinade ingredients with the chicken in a container and refrigerate at least 2 hours. Remove the meat from the marinade and grill over medium flame for 4–5 minutes on each side. Cut meat into thin strips. For the sauce, combine 2 tbsp water, 1 tsp soy sauce, ¼ tsp lime juice, dash salt, dash black pepper, and 1 tbsp olive oil.  Enjoy!"}, {"key": "bbq_chicken–wrapped_name", "value": "BBQ Chicken With Asparagus"}, {"key": "bbq_chicken–wrapped_ingr", "value": "2 chicken breasts thinly cut, 3 oz each piece \r\n12 spears asparagus \r\n2 tbsp bbq sauce 1 pinch smoked paprika \r\n1 pinch garlic powder \r\n1 pinch pepper"}, {"key": "lavender_mint_steps", "value": "Place lavender petals and mint leaves into a saucepan, pour water over and bring to a boil. Turn heat to low and simmer tea until flavor is your desired strength, 15 to 20 minutes. Enjoy!"}, {"key": "meal_ingr", "value": "Ingredients"}, {"key": "bbq_chicken–wrapped_steps", "value": "Set oven to 400°F / 200°C. Flatten the chicken with a mallet. Season chicken cutlets with 1 tbsp barbecue sauce (for each piece) and your choice of seasonings. Place the chicken on a grill with the open facing down. Bake in the oven or smoke on a grill for about 8 to 10 minutes. Enjoy!"}, {"key": "mushroom_and_spinach_name", "value": "Mushroom and Spinach Omelet"}, {"key": "mushroom_and_spinach_ingr", "value": "1 tbsp olive oil \r\n½ cup chopped shallots \r\n3 garlic cloves, minced \r\n1 (8 oz) package sliced white mushrooms \r\n1 thyme sprig \r\n6 oz fresh baby spinach \r\nsalt \r\nground black pepper \r\n3 large eggs"}, {"key": "mushroom_and_spinach_steps", "value": "Heat a skillet over medium–high heat. Add oil to the pan. Add shallots, garlic, mushrooms, and thyme. Add spinach, sauté until liquid almost evaporates. Combine a pinch of salt and pepper, and 4 eggs in a small bowl, stirring with a whisk. Return pan to medium heat.  Add egg mixture and cook for 1 minute. Lift edges of omelet with a spatula, tilting pan to roll uncooked egg onto the bottom of the pan. Enjoy!"}, {"key": "te<PERSON><PERSON>_chicken_name", "value": "Teriyaki Chicken and Broccoli"}, {"key": "teriya<PERSON>_chicken_ingr", "value": "1 cups broccoli florets \r\n1 large chicken breast, diced into cubes \r\n1 tablespoon oil \r\nsalt and pepper \r\n1 bell pepper, sliced \r\n1 cups cooked brown rice \r\n¼ cup light soy sauce \r\n2  tbsp rice wine vinegar \r\n1 garlic clove, minced \r\n½ tsp sesame oil \r\n¼ teaspoon ground ginger"}, {"key": "te<PERSON><PERSON>_chicken_steps", "value": "Heat a large pan over medium–high heat. Add chicken, season with salt and pepper and cook until no longer pink. Whisk all the ingredients for the teriyaki sauce in a small bowl. Add the broccoli florets and bell pepper in the pan. Divide the chicken and broccoli into 4 meal prep containers. Sprinkle with sesame seeds and cover. Enjoy!"}, {"key": "flaxseed_oatmeal_name", "value": "Flaxseed Oatmeal with Blueberries"}, {"key": "flaxseed_oatmeal_ingr", "value": "2 cups water \r\n1 cups rolled oats \r\n1 tbsp ground flaxseed \r\n1/8 tsp salt \r\n¼ cup whole milk \r\n1 cup blueberries \r\n1 tbsp blueberry preserves \r\n½ tsp grated lemon rind \r\n1 tbsp lemon juice\r\n1 ½ tablespoons butter"}, {"key": "flaxseed_oatmeal_steps", "value": "Bring 3 cups of water to a boil. Stir in oats, add the flaxseed, and salt. Reduce heat to medium–low, cook until tender, stirring frequently. Stir in milk and butter. Meanwhile, combine blueberries and next 4 ingredients in a microwave bowl, microwave at high for 1.5 minutes, stirring every 30 seconds. Divide oatmeal among 4 bowls. Top with blueberries and pecans. Enjoy!"}, {"key": "spicy_and_light_name", "value": "Spicy & Light Kung Pao Chicken Meal Prep"}, {"key": "spicy_and_light_ingr", "value": "150 g / 0.3 lb. chicken breast \r\n1/6 clove garlic  \r\n33 g / 1.17 oz snow peas \r\n150 g / 5.17 oz bell pepper \r\n2/3 tbsp water \r\n⅓ tbsp tomato sauce \r\n1/6 tbsp fresh ginger \r\n1/6 tbsp red chili pepper sauce \r\n1/6 tbsp rice vinegar \r\nchopped green onion"}, {"key": "spicy_and_light_steps", "value": "In a bowl, add ingredients for the sauce and whisk. Set a skillet over medium heat and spray with olive oil. When the skillet is hot, toss in garlic and sear for about 2 minutes. Then add chopped chicken breasts. Cook the chicken until the outside of it is seared, about 6 to 8 minutes. Toss in chopped bell peppers and sear for about 3 minutes. Slowly pour in the sauce and stir frequently. Enjoy!"}, {"key": "healthy_fruity_name", "value": "Healthy Fruity Oatmeal"}, {"key": "healthy_fruity_ingr", "value": "½ cup oats \r\n1 cup water \r\n1 tbsp honey \r\n1 tbsp vanilla extract \r\n½ tsp ground cinnamon \r\n¼ cup blueberries \r\n4 sliced strawberries \r\n½ banana, cut into slices"}, {"key": "healthy_fruity_steps", "value": "Combine water and oats in a microwave bowl. Microwave on high until oats have absorbed the water. Stir in honey, vanilla extract, and ground cinnamon. Add the blueberries and top with banana and strawberry slices. Enjoy!"}, {"key": "smoothie_with_kiwi_name", "value": "Smoothie with <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>"}, {"key": "smoothie_with_kiwi_ingr", "value": "a large handful spinach \r\n2 kiwi pieces \r\none mango \r\njuice one orange"}, {"key": "smoothie_with_kiwi_steps", "value": "Wash the spinach, cut off the roots. Wash the mango and the kiwis, then cut into small pieces. Whisk all ingredients in a blender until smooth. Enjoy!"}, {"key": "smoked_salmon_name", "value": "Salmon & Avocado Tartare"}, {"key": "smoked_salmon_ingr", "value": "1 avocado \r\n57 g / 16 oz salmon \r\n17 g / 0.6 oz red onion, chopped \r\n2 tbsp cilantro, chopped \r\npepper"}, {"key": "smoked_salmon_steps", "value": "Dice the avocado and salmon into cubes. Mix them with the remaining ingredients in a bowl, adding seasonings to taste. Enjoy!"}, {"key": "skinny_fried_egg_name", "value": "Skinny Fried Egg and Avocado Toast"}, {"key": "skinny_fried_egg_ingr", "value": "2 eggs \r\n2 toasted bread slices \r\n1 small avocado \r\n1 tsp lime juice \r\nsalt \r\nblack pepper \r\nparsley"}, {"key": "skinny_fried_egg_steps", "value": "Prepare toast and fried eggs to personal preference. Peel and mash avocado with the lime juice, salt, and pepper. Spread avocado evenly on each slice of toast then top each with a fried egg and additional seasonings at your preference. Enjoy!"}, {"key": "sweet_red_name", "value": "Sweet Red Pre–Workout Smoothie"}, {"key": "sweet_red_ingr", "value": "1 red apple \r\n½ beet \r\n1 stick celery \r\n3 strawberries \r\n¼ a banana \r\ngreen tea (optional) \r\n41g / 1.5 oz  raspberries \r\n1 tsp ginger (optional)"}, {"key": "sweet_red_steps", "value": "Add all ingredients into a blender. Simply blend for about 1 minute to ensure you remove any chunks, then blend until smooth. Simply drink as a snack. Enjoy!"}, {"key": "garlic_roasted_name", "value": "Garlic Roasted Asparagus With Parmesan"}, {"key": "garlic_roasted_ingr", "value": "1 lb. fresh asparagus \r\n2 tbsp olive oil \r\n3 tbsp grated parmesan \r\n2 garlic cloves, minced \r\nsalt and pepper"}, {"key": "garlic_roasted_steps", "value": "Trim the asparagus bottoms. Lay the asparagus in the pan and season with salt and pepper and toss in the olive oil. Lay the asparagus on the grill in a row. Grill for 5–10 minutes over medium heat until they have char marks and are tender. Enjoy!"}, {"key": "homemade_pesto_name", "value": "Homemade Pesto with Shrimp & Zucchini Pasta"}, {"key": "homemade_pesto_ingr", "value": "12 g / 3/8 oz fresh basil \r\n7.5 g / ¼ oz raw spinach \r\n7.5 g / 0.3 oz walnuts \r\n20 ml / 2.8 fl oz olive oil \r\n½ tbsp parmesan \r\n¼ lemon juice \r\n¼ clove garlic \r\nsalt \r\npepper\r\n42 g /1.5 oz cooked shrimp \r\n¼ zucchini \r\n2 cherry tomatoes sliced"}, {"key": "homemade_pesto_steps", "value": "Blend in a food processor Pesto ingredients until smooth. In a bowl, julienne raw zucchini linguine with a spiralizer. Add pesto sauce to zucchini linguine and lightly toss using tongs.  Add your choice of protein such as grilled shrimp or chicken, and then add chopped cherry tomatoes. Enjoy!"}, {"key": "spiced_berry_name", "value": "Spiced Berry Cobbler Oatmeal"}, {"key": "spiced_berry_ingr", "value": "150 g rolled oats, 5.3 oz (cooked)  \r\n49 g blueberries, 1.8 oz  \r\n50 g 1.8 oz blackberries \r\n3 tbsp chopped walnuts \r\n½ tbsp coconut oil \r\n¼ tsp turmeric \r\n¼ tsp allspice \r\n1 packet stevia \"In The Raw\" brand \r\n1 pinch cinnamon \r\n1 tbsp granola"}, {"key": "healthy_shrimp_name", "value": "Healthy Shrimp & Veggie Pasta"}, {"key": "spiced_berry_steps", "value": "Cook rolled oats as directed. Set aside. Set a nonstick skillet over medium heat, add coconut oil. Allow the pan to get hot. Toss in the berries. Season the berries with a pinch of cinnamon, and stir. Remove the berries from the heat and allow the sauce to thicken. Mix very well and add a bit more cinnamon if desired. Pour the warm berry syrup on top of the oatmeal. Garnish with fresh granola to add some crunchiness. Enjoy!"}, {"key": "peach_banana_name", "value": "<PERSON><PERSON>"}, {"key": "peach_banana_ingr", "value": "1 banana \r\n2 peaches\r\n½ cup vanilla almond milk\r\n½ cup ice cubes \r\n¼ tsp cinnamon \r\nhoney (optional)"}, {"key": "peach_banana_steps", "value": "Peel and slice fresh peaches and banana. Pour all ingredients into a blender. Add honey and blend to make the smoothie sweeter if desired. Enjoy!"}, {"key": "chicken_and_name", "value": "Chicken & Pineapple Stir–Fry"}, {"key": "chicken_and_ingr", "value": "110 g chicken breasts, 4 oz \r\n40 g pineapple, 1.4 oz \r\n120 g brown rice, 4.4 oz \r\n⅓ tbsp olive oil \r\n½ tsp brown mustard \r\n1/6 tbsp smoked paprika \r\nsea salt \r\npepper"}, {"key": "chicken_and_steps", "value": "Chop the raw chicken breasts into small pieces. Season the chicken pieces with smoked paprika and brown mustard. Add olive oil to a skillet then toss in the chicken breast pieces. Cook for about 4 minutes until the chicken is about 80% finished, then add chopped bell pepper. Cook for another 2 or 3 minutes before adding the diced pineapple. Enjoy !"}, {"key": "herbal_tea_name", "value": "Herbal Tea"}, {"key": "herbal_tea_ingr", "value": "1 cup water \r\n1 teaspoon grated fresh ginger \r\n1 teaspoon grated lime zest \r\n1 teaspoon lime juice \r\n3 leaves fresh mint"}, {"key": "herbal_tea_steps", "value": "Bring water to a boil in a pot stir in rest of the ingredients. Cook and stir until tea is infused. Enjoy!"}, {"key": "mushroom_and_tomato_name", "value": "Mushroom and Tomato Omelet"}, {"key": "mushroom_and_tomato_ingr", "value": "4 eggs whites \r\n1 egg \r\n½ cup low–fat cottage cheese \r\n½ cup sliced mushrooms \r\n½ chopped tomato"}, {"key": "mushroom_and_tomato_steps", "value": "Combine eggs and beat lightly. Using a cooking spray, cook eggs in a non–stick pan. Add cottage cheese, mushrooms and tomatoes in the center of omelet. Cook until eggs are set and fold over. Enjoy!"}, {"key": "spinach_smoothie_name", "value": "<PERSON><PERSON> Smoothie with <PERSON><PERSON> and <PERSON>"}, {"key": "spinach_smoothie_ingr", "value": "3 cups spinach\r\n2 tbsp lemon juice\r\n2 tsp ginger \r\n2 pears\r\nmint"}, {"key": "spinach_smoothie_steps", "value": "Mix all ingredients in a blender and blend until it reaches a smooth consistency. Enjoy!"}, {"key": "grilled_mexican_name", "value": "Grilled Mexican Corn"}, {"key": "grilled_mexican_ingr", "value": "4 ears corn \r\n1.5 cups sour cream \r\n1 cup grated parmesan \r\n1 lime, juice \r\nred chili powder \r\n2 limes cut into wedges, for garnish"}, {"key": "grilled_mexican_steps", "value": "Grill the corn turning once on a hot grill until it starts to get slightly charred. In a small bowl mix the mayonnaise, sour cream and cilantro. Squeeze over corn, lime juice and season with parmesan. Sprinkle with chili powder. Enjoy!"}, {"key": "pineapple_kiwi_name", "value": "Pineapple Kiwi"}, {"key": "pineapple_kiwi_ingr", "value": "4 tbsp water \r\n½ cup milk \r\n½ cup yogurt \r\n1.5 tbsp honey \r\n1 kiwi fruit, peeled \r\n1 cup pineapple chunks \r\n½ a mango \r\n2 ice cubes"}, {"key": "pineapple_kiwi_steps", "value": "Blend all ingredients at low speed until thick and smooth. Enjoy!"}, {"key": "citrus_honey_name", "value": "Citrus Honey Green Tea"}, {"key": "citrus_honey_ingr", "value": "1 lemon slice\r\n2 teaspoons green tea powder \r\n3/4 cup hot water \r\n½ cup grapefruit juice \r\n3 tbsp lemon juice \r\n1 tsp honey"}, {"key": "citrus_honey_steps", "value": "Stir in the green tea powder and hot water. Add the grapefruit juice, lemon juice, and honey. Mix well and serve with a lemon slice. Enjoy!"}, {"key": "grilled_shrimp_name", "value": "Grilled Shrimp & Avocado Kabobs"}, {"key": "grilled_shrimp_ingr", "value": "450 g / 1 lb jumbo shrimp\r\n1.5 avocados\r\n1 red bell pepper\r\n1 orange bell pepper\r\n1 yellow bell pepper\r\n1 red onion\r\n1 tsp garlic paste \r\n½ tbsp extra virgin olive oil \r\n1 lime \r\nsalt, pepper"}, {"key": "grilled_shrimp_steps", "value": "Set your grill to about 400°F/200°C. Chop veggies and avocado into large pieces for the kabob. Peel shrimp and season.  Choose fibrous vegetables as they are more filling and will likely hold well on a kabob stick under intense heat. Place the kabobs on the grill and cook for about 8-10 minutes then flip over and cook for another 8 to 10 minutes. Enjoy!"}, {"key": "spicy_grilled_chicken_name", "value": "Spicy Grilled Chicken & Egg Sandwich"}, {"key": "spicy_grilled_chicken_ingr", "value": "140 g chicken breasts (5 oz), raw\r\n1 egg \r\n30 g / 1 oz arugula (rocket) \r\n½ roma tomato \r\n1 tbsp goat cheese \r\n1 whole wheat bun \r\ncoconut oil spray \r\n1 tsp smoked paprika\r\n½ tsp cayenne pepper\r\nsalt \r\npepper"}, {"key": "spicy_grilled_chicken_steps", "value": "Season the chicken breast with paprika, cayenne, cumin, sea salt, and pepper. Place whole wheat grain bun in a toaster and cook to desired readiness. Spray a skillet with coconut oil and set over medium - high heat. Add the chicken breast to the skillet and cook on both sides until the chicken breast is completely cooked. Add arugula, tomato, goat cheese, chicken and then the egg. Enjoy!"}, {"key": "golden_glow_name", "value": "Golden Glow Elixir"}, {"key": "golden_glow_ingr", "value": "2 apples \r\n4 medium oranges, peel removed \r\n1 lemon, peeled \r\n2–3 inch knob fresh ginger, peeled \r\n2–inch knob fresh turmeric, peeled \r\n2 cups cold water \r\n1 handful ice"}, {"key": "golden_glow_steps", "value": "In a high–speed blender, blend all ingredients until smooth, adding extra water if needed. A high powered decent blender will turn this into a smooth juice. If you would rather juice the ingredients to make it even smoother, go right ahead to keep all the fiber and nutrients in but juicing the ingredients is just as good. Enjoy!"}, {"key": "beef_omelette_name", "value": "<PERSON><PERSON>"}, {"key": "beef_omelette_ingr", "value": "1 egg \r\n3 egg whites \r\n110 g / 4 oz  lean ground beef\r\n30 g raw spinach, 1 oz \r\n58 g / 2.1 oz bell pepper \r\n14 g / 0.5 oz mozzarella cheese, grated\r\ngarlic powder \r\nground cumin \r\nsalt \r\npepper"}, {"key": "beef_omelette_steps", "value": "Season and cook lean beef in a nonstick skillet. In a bowl, whip eggs together with a pinch of sea salt and pepper. Set a separate nonstick skillet over medium-high heat. Pour in egg mixture and cook like a pancake. Cover the skillet to allow the egg to be cooked in its entirety. Enjoy!"}, {"key": "papaya_name", "value": "<PERSON><PERSON>"}, {"key": "papaya_ingr", "value": "2 medium papayas, pitted & chopped \r\n1 cup raspberries \r\n¼ small pineapple \r\nginger \r\n1 cup milk \r\n2 tbsp greek yogurt \r\n1.5 cubes ice \r\nberries to top"}, {"key": "papaya_steps", "value": "Throw ingredients into a blender. Blend until smooth. Enjoy!"}, {"key": "lavender_mint_name", "value": "Lavender Mint Tea"}, {"key": "lavender_mint_ingr", "value": "¼ cup fresh lavender petals\r\n1 cup fresh mint leaves\r\n4 cups water"}, {"key": "healthy_shrimp_ingr", "value": "170 g/ lb. shrimp \r\n1 zucchini \r\n2 carrots \r\n½ clove garlic chopped \r\n¼ red onion chopped \r\n1 tbsp low–sodium soy sauce \r\n¼ orange juice \r\n½ tbsp sesame oil\r\n1 tablespoon apple cider vinegar"}, {"key": "healthy_shrimp_steps", "value": "You’ll need a <PERSON><PERSON> peeler to slice the zucchini and carrots to make the veggie pasta. Next, add 1 tablespoons of soy sauce. Then, add cider vinegar and squeeze the juice from ¼ an orange. Lastly, add ½ tbsp of sesame oil. Set a skillet a medium heat and spray with coconut oil. Toss in garlic and red onion.  Add the shrimp to the skillet and stir quickly with a spoon to make sure that all of the shrimp absorbs the flavor of the garlic and onions. Enjoy!"}, {"key": "lean_body_name", "value": "Lean Body"}, {"key": "staggered_push_ups_descry", "value": "Begin with your arms in a traditional push-up position, but then stagger your hands by moving one upwards about 6 inches, and the other downwards about 6 inches, keeping them shoulder-width apart. Your knees, legs, and feet should stay together with your heels up and your body weight forward on your hands, all of which create a more challenging core workout.\r\nOnce you rise up from one push-up, execute the next repetition after switching your hands forwards and backwards. Continue to switch your hand positions in between each repetition. The optimal goal is to have your nose and/or chest touch the floor with every push-up."}, {"key": "v_push_ups_descr", "value": "Form a reverse V with your body, keeping your legs stretched out and arms in line with your torso.Clench your hands into a fist to provide a stable support point without placing undue stress on your wrists.\r\nShift your body weight forward while maintaining the V position, and fold your arms by extending the elbows slightly outwards.\r\nLift with your arms and, contracting your pectoral muscles, return to the start position."}, {"key": "wall_push_ups_descr", "value": "Face a wall, standing a little farther than arm’s length away, feet shoulder-width apart.\r\nLean your body forward and put your palms flat against the wall at shoulder height and shoulder-width apart.\r\nSlowly breathe in as you bend your elbows and lower your upper body toward the wall in a slow, controlled motion. Keep your feet flat on the floor.\r\nBreathe out and slowly push yourself back until your arms are straight."}, {"key": "wide_arm_push_ups_descr", "value": "Place your hands on the floor just wider than shoulder width and at chest height then step your feet back behind you and lift your knees. \r\nEngage your glutes, tense your legs and brace your core to keep your body rigid. \r\nBend at the elbows to lower your body towards the floor. Your head and shoulders should move forwards of your hands as you get closer to the floor. \r\nPush your hands into the floor, keeping you body tensed, to move it up away from the floor back to the start position."}, {"key": "side_push_ups_descr", "value": "Lay on your left side with your right arm bent and its palm flat on the ground in front of your left shoulder.\r\nWrap your left arm around your rib cage and bend your knees slightly.\r\nPush with your right arm and allow your body to raise off the ground as you push.\r\nOnce your right arm is almost fully extended, hold for a second before slowly lowering yourself back down to the floor.\r\nRepeat for the designated number of reps before performing the same movement with your left arm."}, {"key": "side_push_ups_eq", "value": "Yoga Matt"}, {"key": "ballet_twist_descr", "value": "Start out in a side plank, balancing on the side of your foot and on one hand with the other extended to the ceiling.\r\nLook at your palm and reach it under your body while raising your butt in the air.\r\nReturn your trunk and hand to a lateral planking position."}, {"key": "donkey_kicks_descr", "value": "Assume the starting position on all fours: knees hip-width apart, hands under your shoulders, neck and spine neutral.\r\nBracing your core, begin to lift your right leg, knee staying bent, foot staying flat, and hinging at the hip.\r\nUse your glute to press your foot directly toward the ceiling and squeeze at the top. Ensure your pelvis and working hip stay pointed toward the ground.\r\nReturn to the starting position."}, {"key": "glute_bridges_descr", "value": "Lie face up on the floor, with your knees bent and feet flat on the ground. Keep your arms at your side with your palms down.\r\nLift your hips off the ground until your knees, hips and shoulders form a straight line. Squeeze those glutes hard and keep your abs drawn in so you don’t overextend your back during the exercise.\r\nHold your bridged position for a couple of seconds before easing back down."}, {"key": "ice_skaters_descr", "value": "Begin in a standing position with your feet shoulder-width apart. You should be looking directly forward, with your chest up, knees and hips unlocked, and your back straight. This will be your starting position.\r\nBegin the exercise by jumping to the right with a slight bend in your knees.\r\nNext, in the same motion, reach down and toward the outside of your right foot with your left hand. Perform this same movement on the opposite side."}, {"key": "plyo_jacks_descr", "value": "Stand with your feet together, arms fully extended with your hands by your sides. \r\nBend your knees slightly then straighten and push through the balls of your feet while straightening your your knees to jump up spreading your legs to wider than hip width apart.\r\nAs you do so, raise both arms out and up in a smooth arc until your hands meet above your head.\r\nAs you return to the ground, bring your feet together and your hands back to your sides with your arms fully extended.\r\nContinue without pause for the desired amount of time or repetitions."}, {"key": "posterior_plank_descr", "value": "Sit on the floor with your legs extended in front of you.\r\nPlace your palms, with fingers spread wide, on the floor slightly behind and outside your hips.\r\nPress into your palms and lift your hips and torso toward the ceiling.\r\nKeep your entire body strong and form a straight line from your head to your heels.\r\nSqueeze your core and glutes try to pull your belly button back toward your spine. Maintain the position for the duration of the set."}, {"key": "reverse_hypers_descr", "value": "Lie on a flat bench on your stomach.\r\nMove where your hips are at the end of the bench and grab the frame or the board.\r\nAt the starting position, straighten your legs, or you can bend them a little bit.\r\nLift your legs up as high as you can. Perform the motion slowly and focus on your spine and lower back. At the top position, you can hold your lower body for a moment.\r\nThen, slowly lower your legs."}, {"key": "butt_kicks_descr", "value": "Standing tall with a tight core and flat back, you will begin the dynamic stretching exercise as if you were running in place. Keep the knees slightly bent at all times.\r\nStart slowly and work up to a faster speed.\r\nBring your left foot back and all the way up to the glutes. Return your left foot to the ground and repeat on the other side.\r\nContinue this back and forth motion, keeping your arms swinging in motion."}, {"key": "forward_lunges_descr", "value": "Stand tall with feet hip-width apart. Engage core.\r\nTake a big step forward with right leg and start to shift weight forward so heel hits the floor first.\r\nLower body until right thigh is parallel to floor and right shin is vertical (it's okay if knee shifts forward a little as long as it doesn't go past right toe). If mobility allows, lightly tap left knee to ground while keeping weight in right heel.\r\nPress into right heel to drive back up to starting position.\r\nRepeat on the other side."}, {"key": "goblet_squats_descr", "value": "Stand with your feet shoulder width apart while holding a light dumbbell to your chest. You should hold the dumbbell by one end between your hands, with the other end extending down your torso.\r\nSquat down keeping your slightly arched and pushing your hips back.\r\nContinue down until your thighs are parallel to the floor.\r\nHold for a count of one.\r\nReturn to the start position."}, {"key": "jump_squats_descr", "value": "Stand with your feet hip width apart. Your toes should be pointing straight ahead or only slightly outward.\r\nCross your arms in front of your body, place your hands behind your head or at the sides of your head.\r\nKeep your weight on your heels and bend your knees while lowering your hips towards the ground as if you are sitting down on a chair.\r\nContinue until you feel a slight stretch in your quadriceps. Do not let your knees extend out beyond the level of your toes.\r\nIn an explosive movement, drive down through your heels pushing yourself up of the floor with your quads.\r\nLand with your knees slightly bent to absorb the impact."}, {"key": "lateral_squats_descr", "value": "Start in a standing position, take your feet out sideways so that they are wider than shoulder width and toes turned out slightly. \r\nReach your hands forwards at shoulder height. Then bend your left knee and push you hips backwards, to lower yourself down over your left foot. \r\nYour right leg should remain as straight as possible. Keep your heels down on the floor.\r\nFrom here, straighten your left leg to stand up and return to the centre position. Then bend your right knee to squat over to your right hand side with your left leg straight."}, {"key": "little_pulse_jumps_descr", "value": "Stand with your feet shoulder-width apart and the toes pointing slightly outward.\r\nBend your knees, pressing your hips back, and squat until the thighs are parallel with the floor.\r\nLift your hips a few inches up and squat again.\r\nPush through the heels to jump straight up.\r\nLand with your knees slightly bent and go back into the squat position."}, {"key": "sumo_squats_descr", "value": "Stand with your feet in a wide stance, with your toes slightly pointed outwards – like a sumo wrestler. Hold your hands together in front of your chest.\r\nKeeping your back straight, lower your body towards the ground by bending your knees.\r\nAs you reach a fully squatting position (legs are bent at a 90-degree angle), hold the pose for 1 second before driving your feet into the floor and slowly push your body back up to the starting position.\r\nFor a bonus, tense your glutes at the top of the movement."}, {"key": "prisoner_squats_descr", "value": "Wrap your fingers around the back of your head.\r\nAdopt a stance with your feet wider than shoulder-width apart.\r\nSink down until your thighs are horizontal, keeping your back straight and knees in line with your feet.\r\nLook forward and maintain a natural arch in your back throughout the move."}, {"key": "regular_squats_descr", "value": "Stand up with your feet shoulder-width apart.\r\nBend your knees, press your hips back and stop the movement once the hip joint is slightly lower than the knees.\r\nPress your heels into the floor to return to the initial position.\r\nRepeat until set is complete."}, {"key": "reverse_lunge_and_kicks_descr", "value": "Step back with your left foot, coming into a deep lunge and bending both knees to 90 degrees.\r\nPress the right heel into the ground as you push off with your left foot, kicking your left leg.\r\nWith control, return to the lunge position. This completes one rep.\r\nRepeat on the other side."}, {"key": "rolling_squats_descr", "value": "Stand up at the front of the mat with your feet shoulder-width apart.\r\nBend your knees and press your hips back, until the glutes touch the mat.\r\nRoll back with your knees bent.\r\nRoll forward into the squat position and stand up.\r\nRepeat until the set is complete."}, {"key": "running_in_place_descr", "value": "Stand straight with your feet shoulder width apart and face forward, opening up your chest.\r\nStart pulling your knees up, and slowly land on the balls of your feet.\r\nRepeat until set is complete."}, {"key": "side_lunges_descr", "value": "Stand straight with your feet hip-width apart.\r\nStep out to the side and transfer your weight to that leg.\r\nUse your lead foot to push you back to the starting position.\r\nRepeat and then switch sides."}, {"key": "side_to_side_squats_descr", "value": "Stand straight with your feet shoulder-width apart.\r\nPress your hips back and squat.\r\nStand up, take a small step to the side and squat again.\r\nReturn to the initial position and repeat this side to side movement until the set is complete."}, {"key": "single_leg_deadlifts_descr", "value": "Stand on one leg with your knee slightly bent.\r\nStart bending at the hips and extend your free leg behind you.\r\nLower your torso until you’re parallel to the floor.\r\nReturn to the starting position and repeat with the opposite leg."}, {"key": "split_jump_squats_descr", "value": "Stand upright and take one step forward into a split stance.\r\nDrop into a split squat so that your leading thigh is parallel to the floor.\r\nPush up from the balls of your feet rapidly to jump up, jumping as high as you can and switching legs midair.\r\nLand in the opposite stance dropping back down into a squat."}, {"key": "split_lunge_jumps_descr", "value": "Take a large step backward and lower your hips, so that your back knee is just above the floor, and your front thigh is parallel to the floor.\r\nJump into the air and switch leg positions.\r\nJump again and return to the starting position.\r\nRepeat the exercise until set is complete."}, {"key": "squat_holds_descr", "value": "Stand up with your feet shoulder-width apart.\r\nBend your knees, press your hips back and stop the movement once the hip joint is slightly lower than the knees.\r\nAs soon as you reach depth, maintain the position for several seconds before driving your body back upwards by pushing through your heels.\r\nPress your heels into the floor to return to the initial position.\r\nRepeat until set is complete."}, {"key": "squat_tuck_jumps_descr", "value": "Start with your feet about shoulder-width apart and hands by your side.\r\nGet down into a quarter squat.Push your hips back and lean forward, but keep your back flat.\r\nPut all your force and jump up by pushing the ground down.As you jump into the air as high as you can, try to bring your knees to your chest and grab them with your hands.\r\nWhen you come down, land softly so as it does not become stressful for your knee joints.\r\nLand on your mid foot, and then rolls back on your heels without a jerk.Try to push your hips down and back as you land to consume the impact. Also, be ready to jump again."}, {"key": "wide_squats_descr", "value": "Stand with your feet in a wide stance and with your toes pointing out to the sides.\r\nLower yourself by bending your knees and pressing your hips back.\r\nOnce your thighs are parallel to the floor, come back up and repeat."}, {"key": "sumo_squats_jumps_descr", "value": "Stand with your feet shoulder width apart and slightly turned out, arms resting on your hips.\r\nBend your knees and plié, then jump up explosively. Keep your core engaged.\r\nLand with control, lowering your body back into the squat position to complete one rep."}, {"key": "bear_crawls_descr", "value": "Get down on your hands and feet with your knees slightly bent and your back flat.\r\nWalk your right hand and your left foot forward.\r\nWalk your left hand and your right foot forward.\r\nKeep walking and alternating sides until the set is complete."}, {"key": "meal_steps", "value": "Steps"}, {"key": "arm_circles_descr", "value": "Stand straight with your feet shoulder-width apart.\r\nRaise and extend your arms to the sides without bending the elbows.\r\nSlowly rotate your arms forward, making small circles of about 1 foot in diameter.\r\nComplete a set in one direction and then switch, rotating backward."}, {"key": "tuck_jumps_descr", "value": "Place your feet at shoulder-width with your toes pointed slightly out. Brace your core and keep your chest up.\r\nBend first at the knees then at the hips. Lower yourself slightly to pre-engage the hamstring muscles. Now launch yourself up into the air.\r\nAt the same time, bring your knees up towards your chest in midair. Land on both feet with slightly bent knees. Move right into your next jump."}, {"key": "bicycles_descr", "value": "Lie on your back, and bend your knees at 90 degrees so that your thighs are pointing toward the ceiling.Place your hands behind your ears. \r\nPump your legs back and forth, as if riding a bicycle. Simultaneously, rotate your torso from side to side by moving an armpit, but not elbow, up toward the opposite knee."}, {"key": "crunches_descr", "value": "Lie on your back on the floor. Keep your knees bent, feet flat, and place your hands to the sides of your head. \r\nLift your head and shoulders off the floor, keeping your feet flat on the floor throughout the movement.Resist the urge to pull your head up with your hands - focus on using your abdominal and core muscles to do the work. \r\nLower to the starting position and repeat."}, {"key": "downward_facing_dogs_descr", "value": "Start on all fours in a tabletop position with your wrists under your shoulders and fingers spread. Step your feet back into a plank - a high push- up with legs and belly engaged. \r\nFrom there, lift your hips and press your chest towards your legs as you stretch your heels towards the floor Push the floor away with your hands in order to bring your upper body towards your legs. \r\nRoot down firmly with the index finger and thumb of each hand and place your feet hips distance apart, with your heels lined up behind your second and third toes."}, {"key": "heel_push_crunches_descr", "value": "Lie on your back with the knees bent and the hands gently cradling the head. Keep the feet flexed as you contract the abs, lifting the shoulder blades off the floor. \r\nAt the top of the crunch, press your heels into the floor while pressing your back against the mat and slightly raising the glutes off the floor."}, {"key": "high_knees_descr", "value": "Start standing with feet hip-distance apart.\r\nLift up right knee as high as it will go and raise the opposite arm, then switch quickly so left knee is up before right foot lands. Continue pulling knees up quickly for as long as desired."}, {"key": "knee_up_crunches_descr", "value": "With your back flat on the floor, bend your knees and hips at right angles, as illustrated. Your hands should be behind your head with elbows out toward your sides. \r\nSlowly lift your head and shoulders off the floor, as if doing a crunch, but also ensure that your hips are coming off the floor by crunching your knees up towards your chest."}, {"key": "leg_raises_descr", "value": "Lie with your back on the floor with legs extended straight.\r\nRaise your legs straight up, slightly lifting your lower back off the floor. Step 3 Hold for a few seconds, then lower your legs back down to the start position. Keep your legs straight throughout the motion."}, {"key": "leg_cycle__russian_twists_descr", "value": "Sit on a gym mat or floor. Maintain your balance as you raise your feet off the floor and place your hands in front of you, as shown. \r\nRotate your torso to the left. Pause, then rotate around to the right. Keep your head in line with your torso throughout the movement, and perform it as quickly as possible."}, {"key": "long_arm_crunches_descr", "value": "Lie on your back with the knees bent and arms extended in front of you.\r\nSlowly lift your head and shoulders. As you curl upward, attempt to reach as high as possible with your fingers. \r\nHold. Then return to the starting position."}, {"key": "mountain_climbers_descr", "value": "Get into pushup position on the floor. Bring your left knee to your right elbow. \r\nStraighten your left leg to return to the starting position. Then, bring your right knee to the left elbow. \r\nStraighten your right leg to return to the starting position. Repeat at a fairly quick pace."}, {"key": "opposite_arm_and_leg_crunches_descr", "value": "Kneel on all fours with your knees under your hips and hip-distance apart and put your hands on the floor a shoulder-width apart under your shoulders. Put your spine into the neutral position. Activate your core by drawing your abdominal muscles back toward your spine.\r\nWithout any sideways movement and keeping your abdomen pulled in, exhale and slowly raise an opposite arm and leg until they are parallel to the floor - be careful to follow the tempo you have set. \r\nInhale and slowly return your arm and leg to the starting position."}, {"key": "plank_descr", "value": "Begin by getting into pushup position, but bend your elbows and rest your weight on your forearms instead of using your hands as illustrated. Your body should form a straight line from your shoulders down to your ankles. \r\nBrace your core by contracting your abdominal muscles in this position."}, {"key": "plank_on_hands_descr", "value": "Plant the hands directly under the shoulders (slightly wider than shoulder-width apart) like you’re about to do a push-up.\r\nGround the toes into the floor and squeeze the glutes to stabilize the body. Your legs should be working in the move too; careful not to lock or hyperextend your knees.\r\nNeutralise the neck and spine by looking at a spot on the floor about a foot beyond the hands. Your head should be in line with your back."}, {"key": "plank_push_ups_descr", "value": "Begin by getting into pushup position, but bend your elbows and rest your weight on your forearms instead of using your hands as illustrated. Your body should form a straight line from your shoulders down to your ankles.\r\nPush yourself up from the floor one arm at a time into a push-up, maintaining your body in a straight line. Slowly return to the starting plank position the same way, one arm at a time."}, {"key": "side_crunches_descr", "value": "Lie down on your back with feet flat on the floor and knees bent. Place your fingers lightly behind your head. \r\nBegin to gradually curl your right shoulder toward your left knee until your right shoulder blade raises above the floor. \r\nSlowly, return to starting position and repeat with the opposite side of the body."}, {"key": "side_plank_descr", "value": "Lie on your left side with your knees straight and prop your upper body up on your left elbow and forearm, as illustrated. \r\nBrace your core by contracting your abs forcefully. Then, slowly raise your hips until your body form a straight line from your ankles down to your shoulders."}, {"key": "sit_ups_descr", "value": "Lie on your back with the knees bent and hands behind your ears. \r\nUse your abs to flex your spine and slowly lift your upper body. Finally, roll back down, slowly and with control. Repeat as desired."}, {"key": "spider_crawls_descr", "value": "Get into pushup position on the floor. Bring your left knee to your right elbow.\r\nStraighten your left leg to return to the starting position. Then, bring your right knee to the left elbow. \r\nStraighten your right leg to return to the starting position. Repeat at a fairly quick pace."}, {"key": "profile", "value": "Profile"}, {"key": "personalized_workouts", "value": "Personalized Workouts"}, {"key": "vertical_leg_crunches_descr", "value": "While lying on your back, extend your legs toward the ceiling. Your hands should be behind your head with elbows out toward your sides.  \r\nSlowly lift your head and shoulders off the floor."}, {"key": "back_extensions_descr", "value": "Start lying face down on mat. Lift abs away from mat to engage them and slide the shoulders down the back. The head is lifted in low hover. Your body is one long line\r\nUsing your back muscles and core, lift the chest away from the mat into extension as you exhale. Think of lengthening from the crown of the head\r\nInhale and return back down to the mat slowly getting longer through the spine as you return."}, {"key": "box_push_ups_descr", "value": "Bend your elbows and lower your upper body slowly to the ground.\r\nWhen your nose reaches the floor, press up with your arms and return to the starting position."}, {"key": "close_arm_push_ups_descr", "value": "Get down on all fours and place your hands on the floor so that they are slightly wider than your shoulders. Cross your ankles behind you as illustrated.\r\nLower your body until your chest nearly touches the ground, pause, then push yourself back to the starting position. Maintain proper form throughout by preventing your hips from sagging at any point, keep your core stiff by bracing your abdominal muscles and straighten your legs while placing your weight on your toes."}, {"key": "cobras_descr", "value": "Lie on your belly and place your palms face down next to your bottom ribs, just above your waist. Lift your upper body off of the ground, pressing into your hands, and leave your knees, shins, and tops of the feet grounded. \r\nReach your tailbone towards your heels to engage your lower abdominals and only straighten your arms to the extent that you can do so without discomfort to your lower back. \r\nLeaving your left palm grounded, turn your chest and head to the right and slide your right hand down the back of your right thigh. Return to center and repeat on the left side."}, {"key": "decline_push_ups_descr", "value": "Place your feet on a bench or any elevated step and get into a standard push up position. \r\nLower your body until your chest nearly touches the floor, pause, then push yourself back to the starting position. Maintain proper form throughout by preventing your hips from sagging at any point, keep your core stiff by bracing your abdominal muscles and straighten your legs while placing your weight on your toes."}, {"key": "dip_and_kick_outs_descr", "value": "Place your palms at the edge of the bench with fingers curled around the edge of the chair. Keep your arms wider than shoulder width apart. Position the rest of your body in a sitting position in front of the chair keep knees bent step one leg extended.\r\nBend your arms until your upper arms are nearly parallel with the ground. Hold for a second return to the starting position."}, {"key": "floor_tricep_dips_descr", "value": "Place your palms on a bench so they face forward, and position yourself as illustrated. keep your legs relatively straight out in front, with both heels firmly on the floor. Straighten your arms and move your torso forward so that your behind and back are just in front of the bench. \r\nBend your arms to right angles, lowering your behind toward the floor as shown. You can make the exercise easier by by bending your knees and moving your feet closer to the bench."}, {"key": "hindu_push_ups_descr", "value": "Come onto the floor on your hands and knees. Set your knees directly below your hips and your hands slightly forward of your shoulders. Spread your palms, index fingers parallel or slightly turned out, and turn your toes under.\r\nBend your elbows and lower your chest. Arch your lower and upper back. As your chest gets lower to the ground, in a scooping motion, scoop your head upward while arching your lower and upper back. Exhale through your mouth as you do this motion. At this point, you are essentially at the “bottom” of the push-up\r\nStraighten your arms and look up. After scooping your head in a round, upward motion and arching your back, straighten your arms, lift your torso, and look upwards. Return to the starting position."}, {"key": "incline_push_ups_descr", "value": "Place your hands on a bench and get into a standard push up position. \r\nLower your body until your chest nearly touches the bench, pause, then push yourself back to the starting position. Maintain proper form throughout by preventing your hips from sagging at any point, keep your core stiff by bracing your abdominal muscles and straighten your legs while placing your weight on your toes. Repeat."}, {"key": "knee_diamond_push_ups_descr", "value": "Lie down chest first with your hands on the floor. Form a diamond shape with your hands. Position hands under your lower chest. Lift yourself with your arms so that only your hands and knees are on the floor. Maintain a straight back. \r\nLower yourself until your chest is inches away from the floor by bending your elbows. The elbows should bend towards your feet. Hold for one second return to starting position."}, {"key": "pike_push_ups_descr", "value": "Get down on your hands and feet, with your hands shoulder-width apart, your legs straight and your hips up.\r\nBend your elbows, until your arms form a 90 degree angle, and bring your head close to the mat.\r\nStraighten your elbows, pushing your body away from the mat, and return to the starting position."}, {"key": "power_punches_descr", "value": "Stand in a boxer's stance with your fists guarding your face. Keep your back upright, your wrists straight and deliver forceful punches! Look the part. Take form to extend and release the punches."}, {"key": "push_ups_descr", "value": "Push Ups are basically a form of bench pressing without the bench. Keep your hands slightly wider than shoulder width apart, and maintain a straight and rigid torso. \r\nLower your chest to the ground slowly and raise it back up. The difficulty of the exercise can be increased by elevating the legs by placing them on a bench for example."}, {"key": "side_rotation_push_ups_descr", "value": "Start in a plank position, with your shoulders over your wrists and legs out behind you with your feet hip distance apart. Pull your navel in and keep your back straight.\r\nAs you lower and exhale, bend your elbows outward to the sides. Hold at the bottom for one breath.\r\nRaise back up to top push-up position. As you reach the top, keep moving in a fluid motion to side plank position: release your right arm and raise it to the ceiling, keeping your body in a long diagonal line. Hold for one breath, then move back into plank position.\r\nRepeat the push-up, twisting the opposite direction bringing your left arm toward the ceiling. Return to plank position to complete one rep."}, {"key": "arms", "value": "Arms"}, {"key": "butt", "value": "Butt"}, {"key": "legs", "value": "Legs"}, {"key": "shoulders", "value": "Shoulders"}, {"key": "body_endurance_name", "value": "Fat Burning"}, {"key": "flat_belly", "value": "Toned Abs"}, {"key": "toned_legs", "value": "<PERSON><PERSON>"}, {"key": "sculpted_arms", "value": "Arms & Back"}, {"key": "better_butt", "value": "<PERSON><PERSON>"}, {"key": "no_annoining_ads", "value": "Meal Plan"}, {"key": "workouts", "value": "Workouts"}, {"key": "exercises_title", "value": "Exercises"}, {"key": "exercises_plural", "value": "Exercises"}, {"key": "search", "value": "Search"}, {"key": "cancel", "value": "Cancel"}, {"key": "sec", "value": "sec"}, {"key": "step", "value": "Step"}, {"key": "body_parts_involved", "value": "BODY PARTS INVOLVED"}, {"key": "you_will_need", "value": "You Will Need"}, {"key": "start", "value": "Start"}, {"key": "go", "value": "Go"}, {"key": "nutrition", "value": "Nutrition"}, {"key": "day", "value": "Day"}, {"key": "meal", "value": "Dish"}, {"key": "healthy_meals", "value": "Healthy Meals"}, {"key": "subscription_info", "value": "Subscription Info"}, {"key": "email_support", "value": "Contact Us"}, {"key": "restore_purchase", "value": "Restore purchases"}, {"key": "enter_your_parameters", "value": "Enter your parameters"}, {"key": "get", "value": "Get"}, {"key": "personalized_plan", "value": "personalized plan!"}, {"key": "what’s_your_name", "value": "What’s your name?"}, {"key": "gender", "value": "Gender"}, {"key": "age", "value": "Age"}, {"key": "current_weight", "value": "Current weight"}, {"key": "target_weight", "value": "Target weight"}, {"key": "i_work_out_once_in_a_while", "value": "I work out once in a while"}, {"key": "i_work_out_almost_daily", "value": "I work out almost daily"}, {"key": "i’m_in_great_shape", "value": "I’m in a great shape"}, {"key": "newbie", "value": "<PERSON><PERSON>"}, {"key": "advanced", "value": "Advanced"}, {"key": "continue", "value": "Continue"}, {"key": "male", "value": "Male"}, {"key": "female", "value": "Female"}, {"key": "other", "value": "Other"}, {"key": "ideal_body", "value": "Ideal Body"}, {"key": "program", "value": "Program"}, {"key": "is_ready!", "value": "Is Ready!"}, {"key": "after_4_weeks", "value": "After\r\n28 days"}, {"key": "now", "value": "Now"}, {"key": "week", "value": "week"}, {"key": "daily_routine", "value": "Daily Routine"}, {"key": "workout_time", "value": "Workout Time"}, {"key": "calories", "value": "Calories"}, {"key": "steps", "value": "Steps"}, {"key": "water", "value": "Water"}, {"key": "get_my_plan", "value": "Get My Plan"}, {"key": "which_area_needs", "value": "Which area needs"}, {"key": "more", "value": "more"}, {"key": "attention", "value": "attention?"}, {"key": "open_workout", "value": "Open workout"}, {"key": "get_fit_with_your_personalized_workout!", "value": "Get fit with your personalized workout!"}, {"key": "show_exercises", "value": "Show Exercises"}, {"key": "start_workout", "value": "Start Workout"}, {"key": "of", "value": "of"}, {"key": "version", "value": "version"}, {"key": "years", "value": "years"}, {"key": "chest", "value": "Chest"}, {"key": "belly", "value": "<PERSON>y"}, {"key": "workout", "value": "Workout"}, {"key": "next", "value": "Next"}, {"key": "retry_later", "value": "Something went wrong. Please check your internet connection and try again."}, {"key": "could_not_connect_to_the_network", "value": "Could not connect to the network"}, {"key": "purchase_restore_has_failed", "value": "Purchase restore has failed"}, {"key": "there_is_nothing_to_be_restored", "value": "There is nothing to be restored"}, {"key": "you_successfully_purchased_premium_access", "value": "You successfully purchased premium access"}, {"key": "there_is_nothing_to_be_purchased", "value": "There is nothing to be restored"}, {"key": "subscription_has_expired", "value": "Subscription has expired"}, {"key": "no_valid_product", "value": "No valid product"}, {"key": "something_went_wrong", "value": "Something went wrong"}, {"key": "unknown_error._please_contact_support", "value": "Unknown error. Please contact support"}, {"key": "not_allowed_to_make_the_payment", "value": "Not allowed to make the payment"}, {"key": "payment_has_been_cancelled", "value": "Payment has been cancelled"}, {"key": "the_purchase_identifier_was_invalid", "value": "The purchase identifier was invalid"}, {"key": "the_device_is_not_allowed_to_make_the_payment", "value": "The device is not allowed to make the payment"}, {"key": "the_product_is_not_available_in_the_current_storefront", "value": "The product is not available in the current storefront"}, {"key": "access_to_cloud_service_information_is_not_allowed", "value": "Access to cloud service information is not allowed"}, {"key": "user_has_revoked_permission_to_use_this_cloud_service", "value": "User has revoked permission to use this cloud service"}, {"key": "you_already_got_premium_access", "value": "You already got premium access"}, {"key": "payment_received", "value": "Payment Received!"}, {"key": "thank_you_for_your_payment", "value": "Thank you for your payment! You can always check the status of your subscription in the Profile Tab."}, {"key": "personalized", "value": "Personalized"}, {"key": "subscription_automatically_renews", "value": "Subscription automatically renews unless auto-renew is turned off at least 24-hours before the end of the current period. Subscriptions may be managed by the user and auto-renewal may be turned off by going to the user’s Account Settings after purchase. Any unused portion of a free trial period, if offered, will be forfeited when the user purchases a subscription to that publication, where applicable.\r\nTo manage your subscriptions see this link for a step by step guide: http://support.apple.com/kb/ht4098\r\n\r\nRead our terms of service at https://vgfit.com/terms and our privacy policy at https://vgfit.com/privacy"}, {"key": "monthly_plan", "value": "Monthly Plan"}, {"key": "yearly_plan", "value": "Yearly"}, {"key": "pay_now", "value": "Pay now"}, {"key": "7_days_free", "value": "7 days\r\nFREE"}, {"key": "week2", "value": "week"}, {"key": "free_trial_enabled", "value": "Free trial enabled"}, {"key": "not_sure", "value": "Not sure?"}, {"key": "enable_free_trial", "value": "Enable Free Trial"}, {"key": "join_millions_of_happy_users", "value": "Join millions of happy users"}, {"key": "personal_training_plan", "value": "Personal Training Plan"}, {"key": "professional_workout_programms", "value": "Professional Workout \r\nPrograms"}, {"key": "only_now_one_time_offer", "value": "Only now!\r\nOne time offer!"}, {"key": "join_with_our_best_offer_ever", "value": "Join with our best offer ever"}, {"key": "save_offer", "value": "Save"}, {"key": "not_now", "value": "Not now"}, {"key": "weekly_plan", "value": "Weekly Plan"}, {"key": "workout_done", "value": "Workout done"}, {"key": "close", "value": "Close"}, {"key": "get_discount", "value": "Get Discount"}, {"key": "my_profile", "value": "My Profile"}, {"key": "creating_your_personal_plan", "value": "Creating Your Personal Plan"}, {"key": "please_wait", "value": "Please wait..."}, {"key": "creating_you", "value": "Creating Your"}, {"key": "personal_plan", "value": "Personal Plan"}, {"key": "7_days_free_trial", "value": "after 7-day free trial"}, {"key": "update_available", "value": "Update available!"}, {"key": "youre_currently_using_not_the_last_version_of_the_app.", "value": "You're currently using not the last version of the app."}, {"key": "update", "value": "Update"}, {"key": "appearance", "value": "Appearance"}, {"key": "light", "value": "Light"}, {"key": "dark", "value": "Dark"}, {"key": "your", "value": "Your"}, {"key": "weight_loss", "value": "Weight Loss"}, {"key": "plan_is_ready", "value": "Plan is Ready!"}, {"key": "sexy_abs", "value": "Sexy\r\n<PERSON>bs"}, {"key": "arms_and_back", "value": "Arms &\r\nBack"}, {"key": "lean_legs", "value": "<PERSON><PERSON>"}, {"key": "fit_butt", "value": "<PERSON><PERSON>"}, {"key": "bikini_body", "value": "Bikini\r\nBody"}, {"key": "what_area_you", "value": "What area you"}, {"key": "wanna_finesse", "value": "wanna finesse?"}, {"key": "in_premium", "value": "In Premium version You will get"}, {"key": "liter", "value": "l"}, {"key": "floz", "value": "fl oz"}, {"key": "premium_free_trial", "value": "<product_freetrial_period> days\r\nFREE"}, {"key": "limited_premium_free_trial", "value": "after <product_freetrial_period>-day free trial"}, {"key": "subscription_info_text", "value": "As a Shapy app user, you can choose the subscription plan that best suits your needs, by considering the workout, and the costs.\r\n  Our Premium 1-year subscription plan offers unlimited access to every level of every imaginable workout routine in exchange for a single payment.  For your convenience, Shapy subscriptions are set to automatically renew within the 24-hour period prior to the subscription end date. You can cancel your subscription at any moment in your iTunes account settings but refunds will not be provided for any unused portion of the terms. Payment will be charged to iTunes Account at confirmation of purchase. Any unused portion of a free trial period, if offered, will be forfeited when the user purchases a subscription to that publication, where applicable. \r\n\r\nWe take the satisfaction and security of our customers very seriously. Read our terms of service at https://vgfit.com/terms and our privacy policy at https://vgfit.com/privacy"}, {"key": "notification_access_title", "value": "Female Fitness needs to send you push notifications"}, {"key": "notification_access_body", "value": "The app will notify you when you are in danger of missing your goal"}, {"key": "“shefit”_would_like_to_send_you_notifications", "value": "“Female Fitness” Would Like to Send You Notifications"}, {"key": "dont_allow", "value": "Don't allow"}, {"key": "allow", "value": "Allow"}, {"key": "shefit_will_remind_you_to_workout!", "value": "Female Fitness will remind  you to workout!"}, {"key": "notifications_title_part1", "value": "Stay"}, {"key": "notifications_title_part2", "value": "motivated"}, {"key": "notifications_title_part3", "value": "with reminders!"}, {"key": "your_target_weight_is", "value": "Your target weight is"}, {"key": "get_unlimited_access_to_your_weight_loss_plan!", "value": "Get unlimited access \r\nto your weight loss plan!"}, {"key": "month", "value": "month"}, {"key": "per", "value": "per"}, {"key": "year", "value": "year"}, {"key": "popular", "value": "POPULAR"}, {"key": "plan", "value": "Plan"}, {"key": "amateur", "value": "Amateur"}, {"key": "meals", "value": "Today's Meal Plan"}, {"key": "mo", "value": "Mo"}, {"key": "tu", "value": "Tu"}, {"key": "we", "value": "We"}, {"key": "th", "value": "Th"}, {"key": "fr", "value": "Fr"}, {"key": "sa", "value": "Sa"}, {"key": "su", "value": "Su"}, {"key": "todays_workout", "value": "Today's Workout"}, {"key": "breakfast", "value": "Breakfast"}, {"key": "morning_snack", "value": "Morning snack"}, {"key": "lunch", "value": "Lunch"}, {"key": "afternoon_snack", "value": "Afternoon snack"}, {"key": "dinner", "value": "Dinner"}, {"key": "rest_day", "value": "Rest day"}, {"key": "meal_plan", "value": "Meal plan"}, {"key": "beginner_workout", "value": "Beginner Workout"}, {"key": "intermediate_workout", "value": "Intermediate Workout"}, {"key": "advanced_workout", "value": "Advanced Workout"}, {"key": "x_day_free_trial", "value": "<product_freetrial_period>-day\r\nFree trial"}, {"key": "after_free_x_day_trial_period", "value": "After free <product_freetrial_period>-day\r\ntrial period"}, {"key": "billed_annually", "value": "Billed annually"}, {"key": "billed_monthly", "value": "Billed monthly"}, {"key": "just", "value": "Just"}, {"key": "sale", "value": "SALE"}, {"key": "special_offer", "value": "Special Offer"}, {"key": "only_today", "value": "ONLY\r\nTODAY"}, {"key": "pausing_workout", "value": "Pausing workout"}, {"key": "resuming_workout", "value": "Resuming workout"}, {"key": "last_exercise", "value": "Last exercise"}, {"key": "rest_time_key", "value": "Rest time <rest_time> seconds"}, {"key": "10_more_seconds_key", "value": "10 more seconds"}, {"key": "settings", "value": "Settings"}, {"key": "flat_belly_1._beginner", "value": "Core Slicer"}, {"key": "flat_belly_2._beginner", "value": "Quick Six Pack"}, {"key": "flat_belly_1._intermediate", "value": "Chizzled Abs"}, {"key": "flat_belly_3._intermediate", "value": "Killer Abs"}, {"key": "flat_belly_1._advanced", "value": "Slim & Cut"}, {"key": "flat_belly_2._advanced", "value": "Tough <PERSON>"}, {"key": "flat_belly_3._advanced", "value": "Hard and Slim"}, {"key": "body_strength_1.beginner", "value": "Strong & Toned"}, {"key": "body_strength_2.beginner", "value": "Sculpt Yourself"}, {"key": "body_strength_3.beginner", "value": "Body Strength"}, {"key": "body_strength_1.intermediate", "value": "Lean and Toned"}, {"key": "body_strength_2._intermediate", "value": "Beach Body"}, {"key": "body_strength_3._intermediate", "value": "<PERSON><PERSON> and <PERSON>y"}, {"key": "body_strength_1._advanced", "value": "Melt Fat"}, {"key": "body_strength_2._advanced", "value": "Fire Physique"}, {"key": "body_strength_3._advanced", "value": "Bikini Ready"}, {"key": "toned_legs_1.beginner", "value": "Fine Cut"}, {"key": "toned_legs_2.beginner", "value": "<PERSON><PERSON>"}, {"key": "toned_legs_3.beginner", "value": "Strong & Fit"}, {"key": "toned_legs_1.intermediate", "value": "Toned Legs"}, {"key": "toned_legs_2.intermediate", "value": "Muscle Builders"}, {"key": "toned_legs_3.intermediate", "value": "Powerful Legs"}, {"key": "toned_legs_1.advanced", "value": "Beach Model"}, {"key": "toned_legs_2.advanced", "value": "Ultimate Legs"}, {"key": "toned_legs_3.advanced", "value": "Killer Legs"}, {"key": "better_butt_1.beginner", "value": "Perfect Booty"}, {"key": "better_butt_2.beginner", "value": "Perfect Peach"}, {"key": "better_butt_3.beginner", "value": "<PERSON><PERSON><PERSON>"}, {"key": "better_butt_1.intermediate", "value": "<PERSON><PERSON><PERSON>camp"}, {"key": "better_butt_2.intermediate", "value": "Malibu Beach Butt"}, {"key": "better_butt_3.intermediate", "value": "Tight Butt"}, {"key": "better_butt_1.advanced", "value": "Brazilian Booty"}, {"key": "better_butt_2.advanced", "value": "Million Dollar Glutes"}, {"key": "better_butt_3.advanced", "value": "<PERSON><PERSON><PERSON>"}, {"key": "body_endurance_1._beginner", "value": "Energy Booster"}, {"key": "body_endurance_2._beginner", "value": "Everyday Routine"}, {"key": "body_endurance_3._beginner", "value": "<PERSON> Blaster"}, {"key": "body_endurance_1._intermediate", "value": "Summer Body"}, {"key": "body_endurance_2._intermediate", "value": "Visible Results"}, {"key": "body_endurance_3._intermediate", "value": "Perfect Body"}, {"key": "body_endurance_1._advanced", "value": "Cut Up"}, {"key": "body_endurance_2._advanced", "value": "Body Rock"}, {"key": "body_endurance_3._advanced", "value": "Full Body"}, {"key": "sculpted_arms_1.beginner", "value": "Mission Fit"}, {"key": "sculpted_arms_2.beginner", "value": "Upper Body Blast"}, {"key": "sculpted_arms_3.beginner", "value": "Diamond Back"}, {"key": "sculpted_arms_1.intermediate", "value": "Strong Upper Body"}, {"key": "sculpted_arms_2.intermediate", "value": "Toned Arms"}, {"key": "sculpted_arms_3.intermediate", "value": "<PERSON>n <PERSON> Strong Back"}, {"key": "sculpted_arms_1.advanced", "value": "Attack the Lats"}, {"key": "sculpted_arms_2.advanced", "value": "Sculpted Body"}, {"key": "sculpted_arms_3.advanced", "value": "Strong and Lean"}, {"key": "flat_belly_2._intermediate", "value": "Summer Abs"}, {"key": "meal_plan2", "value": "Meal Plan"}, {"key": "save", "value": "Save"}, {"key": "use_system_appearance", "value": "Use system appearance"}, {"key": "retry", "value": "Retry"}, {"key": "no_connection", "value": "No connection"}, {"key": "check_connection_and_try_again", "value": "Check your Internet connection \r\nand try again"}, {"key": "whats_your_fitness_goal", "value": "What's your fitness goal?"}, {"key": "whats_your_fitness_goal_selected_word", "value": "fitness"}, {"key": "lose_weight", "value": "Lose weight 🔥"}, {"key": "daily_workout_get_fitter_and_tone_muscle", "value": "Get fitter and tone muscle ⚡"}, {"key": "increase_muscle_mass_and_size", "value": "Increase muscle mass and size 💪"}, {"key": "where_do_you_want_to_train", "value": "Where do you want to train?"}, {"key": "where_do_you_want_to_train_selected_word", "value": "train?"}, {"key": "home", "value": "Home"}, {"key": "gym", "value": "Gym"}, {"key": "how_often_per_week_do_you_want_to_workout", "value": "How often per week do you want to workout?"}, {"key": "often_per_week", "value": "often"}, {"key": "monday", "value": "Monday"}, {"key": "tuesday", "value": "Tuesday"}, {"key": "wednesday", "value": "Wednesday"}, {"key": "thursday", "value": "Thursday"}, {"key": "friday", "value": "Friday"}, {"key": "saturday", "value": "Saturday"}, {"key": "sunday", "value": "Sunday"}, {"key": "three_months", "value": "3 Months Plan"}, {"key": "choose_your_muscle_zones", "value": "Choose your muscle zones"}, {"key": "muscle", "value": "muscle"}, {"key": "error", "value": "Error"}, {"key": "height", "value": "Height"}, {"key": "return_to_app_notifications_title", "value": "You've been selected!"}, {"key": "return_to_app_notifications_body", "value": "🎁Special Offer🏆\r\n❗Fully Free❗"}, {"key": "app_would_like_to_send_you_notifications", "value": "“Shape It Up” Would Like to Send You Notifications"}, {"key": "notification_access_title2", "value": "\"Shape It Up\" needs to send you push notifications"}, {"key": "shefit_will_remind_you_to_workout2", "value": "\"Shape It Up\" will remind  you to workout!"}, {"key": "skip", "value": "<PERSON><PERSON>"}, {"key": "anywere", "value": "Anywhere"}, {"key": "survey_parameters_title_selected_word", "value": "parameters"}, {"key": "survey_parameters_title", "value": "Enter your parameters"}, {"key": "today", "value": "Today"}, {"key": "start_free_trial", "value": "START FREE TRIAL"}, {"key": "limited_premium_after_trial", "value": "after"}, {"key": "scrolling_premium_title", "value": "Open Premium\r\nAccess"}, {"key": "scrolling_premium_subtitle", "value": "You will get:"}, {"key": "premium_feature_0", "value": "Ideal body"}, {"key": "premium_feature_1", "value": "Daily Recipes"}, {"key": "premium_feature_2", "value": "Personalized\r\nWorkout Plans"}, {"key": "scrolling_premium_continue", "value": "START FREE TRIAL"}, {"key": "return_to_app_free_workouts_notifications_body", "value": "🎁Special Offer🏆\r\n❗Free Workouts❗"}, {"key": "your_current_weight", "value": "Your current weight?"}, {"key": "your_goal_weight", "value": "Your goal weight?"}, {"key": "lightly_active", "value": "Lightly Active"}, {"key": "moderately_active", "value": "Moderately Active"}, {"key": "very_active", "value": "Very Active"}, {"key": "analyzing_your_profile", "value": "Analyzing your profile"}, {"key": "estimating_your_metabolic_age", "value": "Estimating your metabolic age"}, {"key": "adopting_the_plan_to_your_schedule", "value": "Adopting the plan to your schedule"}, {"key": "selecting_suitable_workouts_and_recipes", "value": "Selecting suitable workouts and recipes"}, {"key": "which_areas_need_the_most_attention", "value": "Which areas need the most attention?"}, {"key": "how_old_are_you", "value": "How old are you?"}, {"key": "how_many_cups_of_coffee_do_you_drink", "value": "How many cups of coffee do you drink?"}, {"key": "your_height", "value": "Your height?"}, {"key": "your_heightt", "value": "Your height?"}, {"key": "no", "value": "No"}, {"key": "how_many_hours_do_you_sleep", "value": "How many hours do you sleep?"}, {"key": "welcome", "value": "Welcome"}, {"key": "workout_plan", "value": "Workout plan"}, {"key": "meal_plans", "value": "Meal plan"}, {"key": "join_shapeitup_in_building_your_perfect_body", "value": "Join <PERSON>hape It Up in building your perfect body"}, {"key": "step_by_step_guide_to_get_your_best_body_ever", "value": "Step by step guide to get your best body ever"}, {"key": "balance_your_calorie_intake_and_boost_your_metabolism", "value": "Balance your calorie intake and boost your metabolism"}, {"key": "shape_up_your_life", "value": "Shape Up Your Life"}, {"key": "get_started", "value": "Get started"}, {"key": "how_active_are_you_now", "value": "How active are you now?"}, {"key": "5_7_hours", "value": "5-7 hours"}, {"key": "7_10_hours", "value": "7-10 hours"}, {"key": "over_10_hours", "value": "over 10 hours"}, {"key": "yes!", "value": "YES!"}, {"key": "action_is_the_key_to_all_success", "value": "Action is the key to all success"}, {"key": "start_your_membership_now", "value": "Start your membership now"}, {"key": "continue_with", "value": "Continue with"}, {"key": "email", "value": "Email"}, {"key": "be_premium", "value": "Be premium"}, {"key": "get_unlimited_access", "value": "Get unlimited access"}, {"key": "premium_workout_programs", "value": "Premium workout programs"}, {"key": "100+_healthy_recipes_for_losing_weight", "value": "100+ Healthy Recipes for losing weight"}, {"key": "proper_exercise_technique", "value": "Proper exercise technique"}, {"key": "per_context:_5$_per_month", "value": "per"}, {"key": "what’s_your_fitness_goal", "value": "What’s your fitness goal?"}, {"key": "tone_my_body", "value": "Tone my body"}, {"key": "gain_muscles", "value": "Gain muscles"}, {"key": "be_more_active", "value": "Be more active"}, {"key": "lose_weightt", "value": "Lose weight"}, {"key": "armss", "value": "Arms"}, {"key": "abss", "value": "Abs"}, {"key": "backk", "value": "Back"}, {"key": "buttt", "value": "Butt"}, {"key": "legss", "value": "Legs"}, {"key": "kg", "value": "kg"}, {"key": "lbs", "value": "lbs"}, {"key": "cm", "value": "cm"}, {"key": "inches", "value": "inches"}, {"key": "what’s_your_favorite_food", "value": "What’s your favorite food?"}, {"key": "how_often_do_you_want_to_workout", "value": "How often do you want to workout?"}, {"key": "annual_plan", "value": "Annual plan"}, {"key": "off_context:_50%_off", "value": "OFF"}, {"key": "free_trial", "value": "Free Trial"}, {"key": "after_trial", "value": "after trial"}, {"key": "bicycle", "value": "Bicycle"}, {"key": "glute_bridges", "value": "Glut<PERSON>"}, {"key": "leg_raises", "value": "Leg Raises"}, {"key": "lateral_squats", "value": "Lateral Squats"}, {"key": "plank", "value": "Plank"}, {"key": "posterior_plank", "value": "Posterior Plank"}, {"key": "pulse_jumps", "value": "Pulse Jumps"}, {"key": "push_ups", "value": "Push Ups"}, {"key": "side_plank", "value": "Side Plank"}, {"key": "wide_squats", "value": "Wide Squats"}, {"key": "skipp", "value": "<PERSON><PERSON>"}, {"key": "weather_location", "value": "Weather location"}, {"key": "allow_geolocation_access", "value": "Please allow get access to your location, you will receive weather Information when you using the app."}, {"key": "first_day_of_week", "value": "First day of week"}, {"key": "sign_in", "value": "Sign in"}, {"key": "already_have_an_account", "value": "Already have an account?"}, {"key": "make", "value": "Log in"}, {"key": "seems_you_dont_have_internet_connection", "value": "Seems you don't have internet connection"}, {"key": "purchase_was_cancelled", "value": "Purchase was cancelled"}, {"key": "purchase_has_failed", "value": "Purchase has failed"}, {"key": "no_products", "value": "No Products"}, {"key": "no_current_offering_configured", "value": "No current offering configured"}, {"key": "are_you_sure_you_want_to_delete_your_account", "value": "Are you sure you want to delete your account?"}, {"key": "are_you_sure_you_want_to_logout", "value": "Are you sure you want to logout?"}, {"key": "purchases_successfully_restored", "value": "Purchases successfully restored"}, {"key": "terms_of_use", "value": "Terms of use"}, {"key": "privacy_policy", "value": "Privacy policy"}, {"key": "weekly", "value": "Weekly"}, {"key": "monthly", "value": "Monthly"}, {"key": "2_months", "value": "2 Months"}, {"key": "3_months", "value": "3 Months"}, {"key": "yearly", "value": "Yearly"}, {"key": "6_months", "value": "6 Months"}, {"key": "lifetime", "value": "Lifetime"}, {"key": "subscription_short_info", "value": "Subscription automatically renews unless auto-renew is turned off at least 24-hours before the end of the current period. Subscriptions may be managed by the user and auto-renewal may be turned off by going to the user's Account Settings after purchase. Any unused portion of a free trial period, if offered, will be forfeited when the user purchases a subscription to that publication, where applicable."}, {"key": "days", "value": "days"}, {"key": "days5andmore", "value": "days"}, {"key": "weeks", "value": "weeks"}, {"key": "weeks5andmore", "value": "weeks"}, {"key": "months", "value": "months"}, {"key": "years5andmore", "value": "years"}, {"key": "months5andmore", "value": "months"}, {"key": "no_payment_if_canceled_24_hours_before_the_trial_ends", "value": "No payment if canceled 24 hours before the trial ends."}, {"key": "best_value", "value": "best value"}, {"key": "most_popular", "value": "most popular"}, {"key": "special_deal", "value": "Special deal"}, {"key": "per_year", "value": "per year"}, {"key": "per_month_after_trial", "value": "per month after trial"}, {"key": "then", "value": "then"}, {"key": "free", "value": "free"}, {"key": "min", "value": "min"}, {"key": "kcal", "value": "kcal"}, {"key": "meal_plan_for", "value": "Meal plan for"}, {"key": "language", "value": "Language"}, {"key": "create_yourself", "value": "Create yourself"}, {"key": "warning", "value": "Warning"}, {"key": "mail_services_are_not_available", "value": "Mail services are not available"}, {"key": "yes", "value": "Yes"}, {"key": "done", "value": "Done"}, {"key": "choose_first_day_of_week", "value": "Choose first day of week"}, {"key": "choose_language", "value": "Choose language"}, {"key": "workout_paused", "value": "Workout paused"}, {"key": "rest", "value": "Rest"}, {"key": "let’s_go", "value": "Let's go"}, {"key": "get_your_personalized_workout_and_meal_plan", "value": "Get your personalized workout and meal plan <in just 1 minute> to help you achieve your fitness goals"}, {"key": "lets", "value": "Make sports a daily habit with"}, {"key": "get_your", "value": "Let's get to know you better"}, {"key": "great!", "value": "Great!"}, {"key": "we’ll_customize_your_plan_according_to_your_personal_goals", "value": "We'll customize your plan according to your personal goals"}, {"key": "got_it", "value": "Got it"}, {"key": "lack_of_sleep_can_often_result_in_low_energy_and_morning_anxiety", "value": "Lack of sleep can often result in <low energy> and <morning anxiety>, which is why many of us <crave sweets> on days like that. Make sure to get to bed on time tonight!"}, {"key": "your_personal_plan_is_almost_ready!", "value": "Your personal plan is almost ready!"}, {"key": "turn_on_notifications", "value": "Turn on notifications"}, {"key": "to_establish_the_habit_and_ensure_you_don’t_miss_your_workout", "value": "to establish the habit and ensure you don't miss your workout"}, {"key": "go_back", "value": "Back"}, {"key": "have_you_been_into_sports_lately", "value": "Have you been into sports lately?"}, {"key": "been_hitting_the_gym", "value": "Been hitting the gym"}, {"key": "how’ll_you_reward_yourself_for_crushing_your_goals", "value": "How'll you reward yourself for crushing your goals?"}, {"key": "i’m_going_to_have_a_spa_day", "value": "I'm going to have a spa day"}, {"key": "not_much", "value": "Not much"}, {"key": "too_busy_at_work", "value": "Too busy at work"}, {"key": "trying_to_get_back_into_it", "value": "Trying to get back into it"}, {"key": "pick_training_days", "value": "Pick training days"}, {"key": "your_plan", "value": "Your plan"}, {"key": "weight", "value": "Weight"}, {"key": "your_workout_days", "value": "Your workout days"}, {"key": "edit_profile", "value": "Edit profile"}, {"key": "we’ll_provide_you_with_recipes_for_healthy_and_delicious_meals", "value": "We’ll provide you with recipes for healthy and delicious meals"}, {"key": "i’ll_buy_myself_a_nice_present", "value": "I’ll buy myself a nice present"}, {"key": "i’m_going_on_a_short_vacation", "value": "I’m going on a short vacation"}, {"key": "ready_to_start", "value": "Ready to start?"}, {"key": "here’s_a_quick_preview_of_your", "value": "Here’s a quick preview of your"}, {"key": "personal_plan!", "value": "personal plan!"}, {"key": "new_workout", "value": "New workout"}, {"key": "extra_workouts", "value": "Extra workouts"}, {"key": "protein", "value": "<PERSON><PERSON>"}, {"key": "fats", "value": "Fats"}, {"key": "carbs", "value": "<PERSON><PERSON>"}, {"key": "vegetables", "value": "Vegetables"}, {"key": "fish", "value": "Fish"}, {"key": "soups", "value": "Soups"}, {"key": "meat", "value": "Meat"}, {"key": "salads", "value": "Salads"}, {"key": "cal", "value": "Cal"}, {"key": "germany", "value": "Germany"}, {"key": "keep_calm_and_rest", "value": "Keep calm and rest"}, {"key": "it’s_time_for_sport!", "value": "It’s time for sport!"}, {"key": "pick_the_areas_in_your_life_that_you_want_to_improve", "value": "Pick the areas in your life that you want to improve"}, {"key": "energy_and_motivation", "value": "Energy and motivation"}, {"key": "look_hot", "value": "Look hot"}, {"key": "enhance_sleep", "value": "Enhance sleep"}, {"key": "physical_health", "value": "Physical health"}, {"key": "look_gorgeous", "value": "Look gorgeous"}, {"key": "self_confidence", "value": "Self-confidence"}, {"key": "build_strength", "value": "Build strength"}, {"key": "core", "value": "Core"}, {"key": "check_your_internet_connection_and_try_again", "value": "Check your internet connection and try again"}, {"key": "what_do_our_users_say", "value": "What do our users say?"}, {"key": "unlimited_workouts_anytime_anywhere", "value": "Unlimited workouts anytime anywhere"}, {"key": "go_premium", "value": "Go premium"}, {"key": "toned_abs", "value": "Toned abs"}, {"key": "change_your_life", "value": "Change your life"}, {"key": "lose_weight_with_no_diet", "value": "Lose weight with no diet"}, {"key": "at_home_workouts_for_all_levels", "value": "At home workouts for all levels"}, {"key": "daily_workouts", "value": "daily workouts"}, {"key": "80+_step_by_step_instructive_videos_for_every_muscle_group", "value": "80+ step-by-step instructive videos for every muscle group"}, {"key": "programs_for_specific_areas", "value": "Programs for specific areas"}, {"key": "personalized_daily_workouts", "value": "Personalized daily workouts"}, {"key": "workouts_adapted_to_your_fitness_level,_goals_and_sсhedule", "value": "Workouts adapted to your fitness level, goals and sсhedule"}, {"key": "about_free_trial", "value": "About free trial"}, {"key": "150+_easy_and_yummy_recipes_based_on_your_food_preferences", "value": "150+ easy and yummy recipes based on your food preferences"}, {"key": "cancel_anytime_by_one_click_during_the_trial", "value": "Cancel anytime by one click during the trial"}, {"key": "access_to_all_types_of_trainings_and_meal_plans", "value": "Access to all types of trainings and meal plans"}, {"key": "zero_hidden_charges_during_the_trial", "value": "Zero hidden charges during the trial"}, {"key": "you_need_1_app_to_achieve_your", "value": "You need 1 app to achieve your"}, {"key": "dream_body", "value": "dream body"}, {"key": "80+_trainings", "value": "80+ trainings"}, {"key": "get_unlimited_access_to", "value": "Get unlimited access to"}, {"key": "monday’s_meal_plan", "value": "Monday’s\r\nmeal plan"}, {"key": "wednesday’s_meal_plan", "value": "Wednesday’s \r\nmeal plan"}, {"key": "tuesday’s_meal_plan", "value": "Tuesday’s \r\nmeal plan"}, {"key": "thursday’s_meal_plan", "value": "Thursday’s \r\nmeal plan"}, {"key": "friday’s_meal_plan", "value": "Friday’s \r\nmeal plan"}, {"key": "saturday’s_meal_plan", "value": "Saturday’s \r\nmeal plan"}, {"key": "sunday’s_meal_plan", "value": "Sunday’s \r\nmeal plan"}, {"key": "sunday’s_workout", "value": "Sunday’s workout"}, {"key": "saturday’s_workout", "value": "Saturday’s workout"}, {"key": "friday’s_workout", "value": "Friday’s workout"}, {"key": "thursday’s_workout", "value": "Thursday’s workout"}, {"key": "wednesday’s_workout", "value": "Wednesday’s workout"}, {"key": "tuesday’s_workout", "value": "Tuesday’s workout"}, {"key": "monday’s_workout", "value": "Monday’s workout"}, {"key": "usa", "value": "USA"}, {"key": "italy", "value": "Italy"}, {"key": "united_kingdom", "value": "United Kingdom"}, {"key": "papaya_smoothie", "value": "Papaya smoothie"}, {"key": "healthy_shrimp_and_veggie_pasta", "value": "Healthy Shrimp & Veggie Pasta"}, {"key": "garlic_roasted_asparagus_with_parmesan", "value": "Garlic roasted asparagus with parmesan"}, {"key": "tomato_soup_with_chicken_and_broccoli", "value": "Tomato soup with chicken and broccoli"}, {"key": "fitness_goal", "value": "Fitness goal"}, {"key": "you_can", "value": "You can"}, {"key": "do_you_like_this_exercise", "value": "Do you like this exercise?"}, {"key": "beach_body", "value": "Beach body"}, {"key": "enter_email", "value": "Enter email"}, {"key": "successfully_logged_out", "value": "Successfully logged out"}, {"key": "successfully_deleted_your_account", "value": "Successfully deleted your account"}, {"key": "delete_account", "value": "Delete account"}, {"key": "success", "value": "Success"}, {"key": "enter_password", "value": "Enter password"}, {"key": "logout", "value": "Logout"}, {"key": "invalid_email_address", "value": "Invalid email address"}, {"key": "sign_up", "value": "Sign Up"}, {"key": "invalid_password", "value": "Invalid password"}, {"key": "create_password", "value": "Create password"}, {"key": "password_reset_mail_has_been_sent", "value": "Password reset mail has been sent"}, {"key": "email_has_been_verified", "value": "Email has been verified"}, {"key": "password_strength", "value": "Password strength"}, {"key": "resend", "value": "Resend"}, {"key": "verify", "value": "Verify"}, {"key": "verification_mail_has_been_sent", "value": "Verification mail has been sent"}, {"key": "verify_email", "value": "Verify email"}, {"key": "email_has_not_yet_been_verified", "value": "Email has not yet been verified"}, {"key": "request_has_timed_out", "value": "Request has timed out"}, {"key": "forgot_password", "value": "Forgot password?"}, {"key": "didn’t_get_a_verification_mail", "value": "Didn’t get a verification mail?"}, {"key": "didn’t_get_a_reset_mail", "value": "Didn’t get a reset mail?"}, {"key": "don’t_have_an_account", "value": "Don’t have an account?"}, {"key": "weak", "value": "weak"}, {"key": "fair", "value": "fair"}, {"key": "strong", "value": "strong"}, {"key": "make_sports_a_daily_habit_with", "value": "Make sports a daily habit with"}, {"key": "let’s_get_to_know_you_better", "value": "Let’s get to know you better"}, {"key": "you_must_select_minimum_2_and_maximum_6_days", "value": "Please select a minimum of 2 days and a maximum of 6 days"}, {"key": "you_must_select_minimum_two_areas", "value": "Please choose at least one area"}, {"key": "todays_meal_plan", "value": "Today's \r\nmeal plan"}, {"key": "body_parameters", "value": "Body parameters"}, {"key": "purchase_my_plan", "value": "purchase my plan"}, {"key": "get_full_access", "value": "get full access"}, {"key": "push_my_limits", "value": "push my limits"}, {"key": "lets_get_started", "value": "lets get started"}, {"key": "get_fit", "value": "get fit"}, {"key": "buy", "value": "buy"}, {"key": "lets_get_fit", "value": "let's get fit"}, {"key": "healthy_pancakes_with_berries_steps", "value": "1. In a large bowl, mix together the flour, baking powder, baking soda, and cinnamon.\r\n2. In a separate bowl, mash the banana with a fork. Add the egg, plant-based milk, and olive oil. Mix well.\r\n3. Add the wet ingredients to the dry ingredients and gently stir until just combined.\r\n4. Heat a skillet over medium heat and grease with olive oil. Scoop the batter onto the skillet using a spoon and cook the pancakes for 2-3 minutes per side.\r\n5. Serve pancakes with berries and maple syrup, if desired."}, {"key": "broccoli_with_white_beans", "value": "Broccoli with white beans"}, {"key": "broccoli_with_white_beans_ingr", "value": "400g broccoli\r\n1 can (400g) beans (e.g. kidney beans)\r\n1 onion\r\n150g spinach\r\n2 tbsp olive oil\r\n1 garlic clove\r\nsalt and pepper to taste"}, {"key": "broccoli_with_white_beans_steps", "value": "1. Separate the broccoli into florets. Boil in salted water for 3-4 minutes until parboiled.\r\n2. Finely chop the onion. Heat the olive oil in a pan and sauté the onion for 3-4 minutes.\r\n3. Add the finely chopped garlic and sauté for another 1 minute.\r\n4. Add the spinach and cook for 2-3 more minutes until wilted.\r\n5. Add the boiled beans and lightly mash with a fork. Season with salt and pepper.\r\n6. Add the broccoli and mix everything together. Simmer for another 3-5 minutes.\r\n7. Serve hot, garnished with herbs of your choice. Enjoy!"}, {"key": "healthy_pancakes_with_berries_ingr", "value": "2 cups whole wheat flour\r\n2 tbsp baking powder\r\n1 tsp baking soda\r\n1 tsp cinnamon \r\n1 banana\r\n1 egg\r\n1 cup almond or soy milk\r\n1⁄4 cup olive oil\r\n1 cup berries (strawberries, blueberries, raspberries)"}, {"key": "flat_belly_3._beginner", "value": "Flat Abs"}, {"key": "healthy_pancakes_with_berries", "value": "Healthy Pancakes With Berries"}, {"key": "it’s_time_to_workout!", "value": "It’s time to workout!"}, {"key": "hey!_you_didn’t_workout_for_few_days,_come_back_and_do_it!", "value": "Hey! You didn’t workout for few days, come back and do it!"}, {"key": "select_reminder_time", "value": "Select reminder time"}, {"key": "reminder_sound", "value": "Reminder sound"}, {"key": "reminder", "value": "Reminder"}, {"key": "interval", "value": "Interval"}, {"key": "sound", "value": "Sound"}, {"key": "default", "value": "<PERSON><PERSON><PERSON>"}, {"key": "daily_workout_reminder", "value": "Daily workout reminder"}, {"key": "after_few_days_of_inactivity", "value": "After few days of inactivity"}, {"key": "after", "value": "After"}, {"key": "of_inactivity", "value": "of inactivity"}, {"key": "you’ve_received_full_and_free_access_to_the_app", "value": "You’ve received full and free access to the app"}, {"key": "congratulations!", "value": "🎉 Congratulations!"}, {"key": "by_signing_in,_you_agree_to_our_terms_of_use_and_acknowledge_our_privacy_policy", "value": "By signing in, you agree to our <t>Terms of Use<t> and acknowledge our <p>Privacy Policy<p>"}, {"key": "connection_error", "value": "Connection error"}, {"key": "theme", "value": "Theme"}, {"key": "choose_theme", "value": "Choose theme"}, {"key": "light_theme", "value": "Light"}, {"key": "dark_theme", "value": "Dark"}, {"key": "system_theme", "value": "System"}, {"key": "delete_account_info", "value": "Please open the email we sent to the address associated with your account and follow the instructions provided to delete your account. If you can't find the email in your inbox, please check your spam folder. If it's not in spam either, please <c>contact us<c> for further assistance."}, {"key": "sign_in_information", "value": "<Sign In> to Your %@ Account"}, {"key": "by_signing_in,_you_agree_to_our_terms_of_service_and_acknowledge_our_privacy_policy", "value": "By signing in, you agree to our <t>Terms of Use<t> and acknowledge our <p>Privacy Policy<p>"}, {"key": "fill_the_email_first", "value": "Fill the email address first"}, {"key": "reset_password_email_has_been_sent", "value": "Reset password email has been sent"}, {"key": "reset_password_info", "value": "Please open the email we sent to the address associated with your account and follow the instructions provided to reset your password. If you can't find the email in your inbox, please check your spam folder. If it's not in spam either, please <c>contact us<c> for further assistance."}, {"key": "please_wait_five_or_more_seconds", "value": "Please wait %@ seconds"}, {"key": "please_wait_one_second", "value": "Please wait 1 second"}, {"key": "please_wait_two_to_four_seconds", "value": "Please wait %@ seconds"}, {"key": "check_your_email_inbox", "value": "Check your email inbox"}, {"key": "password", "value": "Password"}, {"key": "are_you_sure_you_want_to_sign_out", "value": "Are you sure you want to sign out?"}, {"key": "sign_out", "value": "Sign out"}, {"key": "get_unlimited_access_to_your_personal_plan", "value": "Get unlimited access to your personal plan!"}, {"key": "premium_plans:", "value": "Premium plans:"}, {"key": "to_reach_your_goal,_you’ll_receive_access_to:", "value": "To reach your goal, you’ll receive access to:"}, {"key": "custom_plan_unlock", "value": "A customized plan designed specifically to meet your needs."}, {"key": "six_months", "value": "Six months"}, {"key": "you_can’t_upgrage_to_premium_in_the_app", "value": "You can’t upgrage to Premium in the app"}, {"key": "account_is_not_deleted_yet", "value": "Account is not deleted yet"}, {"key": "account_successfully_deleted", "value": "Account successfully deleted"}, {"key": "premium_active", "value": "Premium active"}, {"key": "or", "value": "Or"}, {"key": "by_continuing_you_accept_privacy_policy_and_terms_of_use", "value": "By continuing you accept <p>Privacy Policy<p> and <t>Terms of Use<t>"}, {"key": "calories_intake", "value": "Calories intake"}, {"key": "download", "value": "Download"}, {"key": "would_you_like_to_download_the_videos_for_offline_viewing", "value": "Would you like to download the videos for offline viewing?"}, {"key": "no_data", "value": "No data"}, {"key": "user_not_found", "value": "User not found"}, {"key": "user_is_suspended", "value": "User is suspended"}, {"key": "not_found", "value": "Not found"}, {"key": "user_with_such_email_does_not_exist", "value": "User with such email does not exist"}, {"key": "incorrect_email_or_password", "value": "Incorrect email or password"}, {"key": "wrong_username_and_or_password", "value": "Wrong username and/or password"}, {"key": "user_did_not_select_his_experience", "value": "User did not select his experience"}, {"key": "user_is_inactive", "value": "User is inactive"}, {"key": "please_wait_for_1_minute", "value": "Please wait for 1 minute"}, {"key": "you_must_have_at_least_one_rest_day", "value": "You must have at least one rest day"}, {"key": "password_cannot_be_empty", "value": "Password cannot be empty"}, {"key": "email_cannot_be_empty", "value": "Email cannot be empty"}, {"key": "please_enter_a_valid_email_address", "value": "Please enter a valid email address"}, {"key": "enter_text_message", "value": "Enter text message"}, {"key": "account", "value": "Account"}, {"key": "not_logged_in", "value": "Not signed in"}, {"key": "my_progress", "value": "My Progress"}, {"key": "completed", "value": "completed"}, {"key": "completed_workouts", "value": "Completed workouts"}, {"key": "duration", "value": "Duration"}, {"key": "training_completed", "value": "training completed"}, {"key": "my_workouts", "value": "My workouts"}, {"key": "daily_plan", "value": "Daily plan"}, {"key": "in_progress", "value": "in progress:"}, {"key": "passed", "value": "passed:"}, {"key": "calendar", "value": "Calendar"}, {"key": "first_step", "value": "First Step!"}, {"key": "warming_up", "value": "Warming Up!"}, {"key": "in_the_zone", "value": "In the Zone!"}, {"key": "shape_in_progress", "value": "Shape in Progress!"}, {"key": "halfway_there", "value": "Halfway There!"}, {"key": "stronger_than_you_think", "value": "Stronger Than You Think!"}, {"key": "fitness_queen", "value": "Fitness Queen!"}, {"key": "you_did_it", "value": "You Did It!"}, {"key": "you_burned_the_calories", "value": "You burned the calories of two Big Macs 🍔🍔 — guilt-free!"}, {"key": "burned_off_a_pizza_and_dessert", "value": "Burned off a pizza and dessert — it’s like erasing a Friday night 🍕🍰"}, {"key": "that’s_a_full_day_of_fast_food", "value": "That’s a full day of fast food: burgers, fries, and soda 🍔🍟🥤"}, {"key": "you_melted", "value": "You melted 1.3 kg of fat 🥤 — like dropping a weight off your waist 🏋️‍♀️"}, {"key": "that’s_the_calories_of_a_whole", "value": "That’s the calories of a whole holiday week: cakes, salads, champagne 🎄🍰🍾"}, {"key": "equal_to_100_chocolate_bars", "value": "Equal to 100 chocolate bars 🍫 — and none stuck to your thighs!"}, {"key": "burned_off_7_pizzas", "value": "Burned off 7 pizzas, 10 desserts & wine — you’re a restaurant’s nightmare 😅🍕🍰🍷"}, {"key": "you_lost_6.5_kg_of_fat", "value": "You lost 6.5 kg of fat — like carrying a fat-filled suitcase all year and dropping it! 🧳🔥"}, {"key": "reset_workout_progress", "value": "RESET WORKOUT PROGRESS"}, {"key": "are_you_sure_you_want_to_reset", "value": "Are you sure you want to reset the progress for this workout?"}, {"key": "the_training_was_missed", "value": "The training was missed."}, {"key": "free_usage_0_title", "value": "Welcome to <PERSON><PERSON><PERSON> 💖"}, {"key": "free_usage_0_body", "value": "Start your first quick workout – just 7 minutes to feel amazing."}, {"key": "free_usage_1_title", "value": "Morning stretch for you"}, {"key": "free_usage_1_body", "value": "Wake up your body with a gentle full-body warmup."}, {"key": "free_usage_6_title", "value": "Track your journey 📈"}, {"key": "free_usage_2_title", "value": "Feel confident, every day"}, {"key": "free_usage_2_body", "value": "Tone your legs and glutes – it’s easier than you think."}, {"key": "free_usage_3_title", "value": "Your glow-up starts here ✨"}, {"key": "free_usage_3_body", "value": "Start building your dream body with today’s 10-minute burn."}, {"key": "free_usage_4_title", "value": "Missed a workout?"}, {"key": "free_usage_4_body", "value": "No worries – <PERSON><PERSON><PERSON>’s short routines fit any schedule."}, {"key": "free_usage_5_title", "value": "Strong is beautiful"}, {"key": "free_usage_5_body", "value": "Today’s focus: core strength and posture boost."}, {"key": "free_usage_6_body", "value": "Open your dashboard and see how far you’ve come."}, {"key": "free_usage_7_title", "value": "One week of Shapy 💪"}, {"key": "free_usage_7_body", "value": "Celebrate your first 7 days – consistency is power!"}, {"key": "free_usage_8_title", "value": "Want a personalized plan?"}, {"key": "free_usage_8_body", "value": "Unlock Premium for smart routines made for your body."}, {"key": "free_usage_9_title", "value": "Workout, your way"}, {"key": "free_usage_9_body", "value": "Customize your focus: belly, booty, arms – your choice."}, {"key": "free_usage_10_title", "value": "Your next level starts now"}, {"key": "free_usage_10_body", "value": "More energy, better sleep, tighter curves – it’s all coming."}, {"key": "free_usage_11_title", "value": "Thousands of women choose <PERSON><PERSON><PERSON>"}, {"key": "free_usage_11_body", "value": "Join them and unlock your strongest self 💪"}, {"key": "free_usage_12_title", "value": "Time to glow"}, {"key": "free_usage_12_body", "value": "Your skin, your tone, your energy – powered by movement."}, {"key": "free_usage_13_title", "value": "Don’t stop now ✨"}, {"key": "free_usage_13_body", "value": "Today’s a new chance to take care of you."}, {"key": "free_usage_14_title", "value": "Premium = results"}, {"key": "premium_active_0_title", "value": "Welcome, Premium Queen 👑"}, {"key": "free_usage_14_body", "value": "Advanced workouts, meal plans, full tracking – all in one."}, {"key": "free_usage_15_title", "value": "Limited-time boost 🔥"}, {"key": "free_usage_15_body", "value": "Upgrade now and stay committed to your goals."}, {"key": "free_usage_16_title", "value": "Keep shining 🌟"}, {"key": "free_usage_16_body", "value": "Your progress is real. Keep going with Shapy Premium!"}, {"key": "premium_active_0_body", "value": "Let’s begin your personal journey to your strongest self."}, {"key": "premium_active_1_title", "value": "<PERSON><PERSON><PERSON> loves your energy 💫"}, {"key": "premium_active_1_body", "value": "Open your dashboard and see the progress you’re building."}, {"key": "premium_active_2_title", "value": "<PERSON><PERSON><PERSON> 💡"}, {"key": "premium_active_2_body", "value": "Your body responds best to consistency. Keep it up!"}, {"key": "premium_active_3_title", "value": "One week in, one step stronger 💖"}, {"key": "premium_active_3_body", "value": "Celebrate your commitment – you’ve earned it."}, {"key": "premium_active_4_title", "value": "Time to glow even brighter ✨"}, {"key": "premium_active_4_body", "value": "Try a booty-focused challenge this week."}, {"key": "premium_active_5_title", "value": "Two weeks of wins!"}, {"key": "premium_active_5_body", "value": "Let’s keep the streak going – you're shining!"}, {"key": "premium_active_6_title", "value": "New recipes just dropped 🍓"}, {"key": "premium_active_6_body", "value": "Fuel your progress with delicious meal ideas."}, {"key": "premium_active_7_title", "value": "Progress = confidence"}, {"key": "premium_active_7_body", "value": "Look how far you’ve come – and keep it going."}, {"key": "premium_active_8_title", "value": "Your strong is showing 💪"}, {"key": "premium_active_8_body", "value": "Stay consistent. You’re an inspiration!"}, {"key": "premium_active_9_title", "value": "1 Month of Shapy 🌸"}, {"key": "premium_active_9_body", "value": "Your body is transforming – let’s go further!"}, {"key": "premium_canceled_0_title", "value": "We miss your glow 💔"}, {"key": "premium_canceled_0_body", "value": "Your custom plan is still here – just one tap away."}, {"key": "premium_canceled_1_title", "value": "Your goals are waiting"}, {"key": "premium_canceled_1_body", "value": "Rejoin Premium and keep lifting yourself up."}, {"key": "premium_canceled_2_title", "value": "Still want that glow?"}, {"key": "premium_canceled_2_body", "value": "Keep the curves, tone, and energy going."}, {"key": "premium_canceled_3_title", "value": "Restart your self-care 💖"}, {"key": "premium_canceled_3_body", "value": "You deserve a moment to care for yourself."}, {"key": "premium_canceled_4_title", "value": "Progress doesn’t pause"}, {"key": "premium_canceled_4_body", "value": "Pick up where you left off – we’re here for you."}, {"key": "premium_canceled_5_title", "value": "No pressure. Just love."}, {"key": "premium_canceled_5_body", "value": "<PERSON><PERSON><PERSON>’s here whenever you're ready to return."}, {"key": "premium_canceled_6_title", "value": "Ready for a fresh start?"}, {"key": "premium_canceled_6_body", "value": "Let’s build that confident body together again."}, {"key": "premium_canceled_7_title", "value": "You’re worth the time 💕"}, {"key": "premium_canceled_7_body", "value": "Invest in you. Premium is still available."}, {"key": "premium_canceled_8_title", "value": "New workouts just dropped 🔥"}, {"key": "premium_canceled_8_body", "value": "Come back and explore what’s new."}, {"key": "premium_canceled_9_title", "value": "Your journey is still here 🌈"}, {"key": "premium_canceled_9_body", "value": "Let’s keep growing stronger – restart today!"}, {"key": "you_burned", "value": "You’ve burned"}, {"key": "share", "value": "Share"}, {"key": "view_reward", "value": "View reward"}, {"key": "no_completed_workouts", "value": "No completed workouts"}, {"key": "start_a_workout_to_see_your_history", "value": "Start a workout to see your history"}, {"key": "are_you_sure_you_want_to_exit", "value": "Are you sure you want to exit?"}, {"key": "ok", "value": "Ok"}]