<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <!-- Primary View - se întinde când gesture în jos -->
    <ImageView
        android:id="@+id/primary_image"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:src="@drawable/ic_youcan"
        android:scaleType="centerCrop"
        android:background="#E3F2FD" />

    <!-- Secondary View - se comprimă când gesture în jos -->
    <LinearLayout
        android:id="@+id/secondary_container"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:orientation="vertical"
        android:background="#FFF3E0"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Secondary Container"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Acest container se va comprima când faci gesture în jos și se va întinde când faci gesture în sus. Efectul elastic parallax funcționează fără ScrollView!"
            android:textSize="14sp"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="#FF9800"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="• Funcționează cu LinearLayout\n• Funcționează cu FrameLayout\n• Funcționează cu ConstraintLayout\n• Funcționează cu orice View container!"
            android:textSize="12sp"
            android:lineSpacingExtra="4dp" />

    </LinearLayout>

    <!-- Spacer pentru a umple restul spațiului -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#E8F5E8" />

</LinearLayout>
