<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/mainProfile"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/parallax_container"
                android:layout_width="match_parent"
                android:layout_height="350dp"
                android:background="@color/text_color_lime">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/yourSelf"
                    android:layout_alignParentTop="true"
                    android:layout_marginStart="25dp"
                    android:layout_marginTop="40dp"
                    android:layout_marginEnd="25dp"
                    android:layout_marginBottom="10dp"
                    android:src="@drawable/ic_youcan" />

                <TextView
                    android:id="@+id/yourSelf"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:fontFamily="@font/inter_medium"
                    android:text="Create yourself"
                    android:textAllCaps="true"
                    android:textSize="14sp"
                    android:textStyle="normal" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_profile_menu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white">

                <RelativeLayout
                    android:id="@+id/rl_subscribe"
                    android:layout_width="match_parent"
                    android:layout_height="63dp"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/labelFullAccess"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="28dp"
                        android:fontFamily="@font/inter_semibold"
                        android:text="Get Full Access"
                        android:textAllCaps="true"
                        android:textColor="#040100"
                        android:textSize="15sp" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="29dp"
                        android:src="@drawable/arrow_black_next" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_alignParentBottom="true"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="1dp"
                        android:background="#d1d1d1" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_info"
                    android:layout_width="match_parent"
                    android:layout_height="63dp"
                    android:layout_below="@+id/rl_subscribe"
                    android:background="@drawable/selector_item_profile">

                    <TextView
                        android:id="@+id/labelSubscrInfo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="28dp"
                        android:fontFamily="@font/inter_semibold"
                        android:text="Subscription info"
                        android:textAllCaps="true"
                        android:textColor="#040100"
                        android:textSize="15sp" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="29dp"
                        android:src="@drawable/arrow_black_next" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_alignParentBottom="true"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="1dp"
                        android:background="#d1d1d1" />
                </RelativeLayout>


                <RelativeLayout
                    android:id="@+id/rl_email"
                    android:layout_width="match_parent"
                    android:layout_height="63dp"
                    android:layout_below="@id/rl_info"
                    android:background="@drawable/selector_item_profile">

                    <TextView
                        android:id="@+id/labelEmailSuport"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="28dp"
                        android:fontFamily="@font/inter_semibold"
                        android:text="Email Support"
                        android:textAllCaps="true"
                        android:textColor="#040100"
                        android:textSize="15sp" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="29dp"
                        android:src="@drawable/arrow_black_next" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_alignParentBottom="true"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="1dp"
                        android:background="#d1d1d1" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_restore"
                    android:layout_width="match_parent"
                    android:layout_height="63dp"
                    android:layout_below="@id/rl_email"
                    android:background="@drawable/selector_item_profile">

                    <TextView
                        android:id="@+id/labelRestorePurch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="28dp"
                        android:fontFamily="@font/inter_semibold"
                        android:text="Restore Purchase"
                        android:textAllCaps="true"
                        android:textColor="#040100"
                        android:textSize="15sp" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="29dp"
                        android:src="@drawable/arrow_black_next" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_alignParentBottom="true"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="1dp"
                        android:background="#d1d1d1" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_profile"
                    android:layout_width="match_parent"
                    android:layout_height="63dp"
                    android:layout_below="@id/rl_restore"
                    android:background="@drawable/selector_item_profile">


                    <TextView
                        android:id="@+id/labelMyProfile"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="28dp"
                        android:fontFamily="@font/inter_semibold"
                        android:text="My Profile"
                        android:textAllCaps="true"
                        android:textColor="#040100"
                        android:textSize="15sp" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="29dp"
                        android:src="@drawable/arrow_black_next" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_alignParentBottom="true"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="1dp"
                        android:background="#d1d1d1" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_progress"
                    android:layout_width="match_parent"
                    android:layout_height="63dp"
                    android:layout_below="@id/rl_profile"
                    android:background="@drawable/selector_item_profile">


                    <TextView
                        android:id="@+id/labelMyProgress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="28dp"
                        android:fontFamily="@font/inter_semibold"
                        android:text="My Progress #"
                        android:textAllCaps="true"
                        android:textColor="#040100"
                        android:textSize="15sp" />

                    <TextView
                        android:id="@+id/labelMyProgressValue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="15dp"
                        android:layout_toLeftOf="@+id/arrowNext"
                        android:background="@drawable/selector_start_open"
                        android:fontFamily="@font/inter_bold"
                        android:paddingLeft="10dp"
                        android:paddingTop="5dp"
                        android:paddingRight="10dp"
                        android:paddingBottom="5dp"
                        android:text="New"
                        android:textAllCaps="true"
                        android:textColor="@drawable/selector_start_text"
                        android:textSize="11sp" />

                    <ImageView
                        android:id="@+id/arrowNext"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="29dp"
                        android:src="@drawable/arrow_black_next" />


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_alignParentBottom="true"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="1dp"
                        android:background="#d1d1d1" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rl_settings"
                    android:layout_width="match_parent"
                    android:layout_height="63dp"
                    android:layout_below="@id/rl_progress"
                    android:background="@drawable/selector_item_profile">


                    <TextView
                        android:id="@+id/labelSettings"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="28dp"
                        android:fontFamily="@font/inter_semibold"
                        android:text="Settings"
                        android:textAllCaps="true"
                        android:textColor="#040100"
                        android:textSize="15sp" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="29dp"
                        android:src="@drawable/arrow_black_next" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_alignParentBottom="true"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="1dp"
                        android:background="#d1d1d1" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/containerMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/rl_settings"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="50dp" />
            </RelativeLayout>
        </LinearLayout>
    </ScrollView>

</RelativeLayout>