<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:autofit="http://schemas.android.com/tools"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/selectWorkoutDay"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">


    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="50dp"
        android:padding="5dp"
        android:src="@drawable/ic_arrow_back"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ProgressBar
        android:id="@+id/linearProgress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="150dp"
        android:layout_height="5dp"
        android:progress="37"
        android:progressDrawable="@drawable/gradient_progress_day"
        app:layout_constraintBottom_toBottomOf="@+id/iv_back"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_back"
        app:layout_constraintVertical_bias="0.5" />


    <TextView
        android:id="@+id/textView16"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:layout_marginTop="30dp"
        android:layout_marginEnd="30dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="center"
        android:text="Your current weight?"
        android:textColor="@color/black"
        android:textSize="24sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearProgress"
        app:layout_constraintVertical_bias="0.36" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="17dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="17dp"
        android:layout_marginBottom="17dp"
        android:orientation="vertical"
        android:weightSum="6.6
"
        app:layout_constraintBottom_toTopOf="@+id/linearLayoutCompat3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView16">

        <RelativeLayout
            android:id="@+id/mondayDay"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:layout_weight="1"
             android:background="@drawable/selector_item_onboarding">

            <com.vgfit.shefit.util.AutoResizeTextView
                android:id="@+id/mondayDayTxt"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:layout_marginStart="26dp"
                android:layout_marginEnd="26dp"
                android:fontFamily="@font/inter_semibold"
                android:gravity="center|start"
                android:maxLines="1"
                android:text="Monday"
                android:textColor="@drawable/selector_start_text"
                android:textAllCaps="true"
                android:textSize="40sp"
                autofit:minTextSize="8sp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/tuesdayDay"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:layout_weight="1"
            android:background="@drawable/selector_item_onboarding">

            <com.vgfit.shefit.util.AutoResizeTextView
                android:id="@+id/tuesdayDayTxt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="26dp"
                android:layout_marginEnd="26dp"
                android:fontFamily="@font/inter_semibold"
                android:gravity="center|start"
                android:maxLines="1"
                android:text="Tuesday"
                android:textAllCaps="true"
                android:textColor="@drawable/selector_start_text"
                android:textSize="40sp"
                autofit:minTextSize="8sp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/wednesdayDay"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:layout_weight="1"
             android:background="@drawable/selector_item_onboarding">

            <com.vgfit.shefit.util.AutoResizeTextView
                android:id="@+id/wednesdayDayTxt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="26dp"
                android:layout_marginEnd="26dp"
                android:fontFamily="@font/inter_semibold"
                android:gravity="center|start"
                android:textColor="@drawable/selector_start_text"
                android:maxLines="1"
                android:text="Wednesday"
                android:textAllCaps="true"
                android:textSize="40sp"
                autofit:minTextSize="8sp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/thursdayDay"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:layout_weight="1"
            android:background="@drawable/selector_item_onboarding">

            <com.vgfit.shefit.util.AutoResizeTextView
                android:id="@+id/thursdayDayTxt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="26dp"
                android:layout_marginEnd="26dp"
                android:fontFamily="@font/inter_semibold"
                android:gravity="center|start"
                android:maxLines="1"
                android:text="Thursday"
                android:textAllCaps="true"
                android:textColor="@drawable/selector_start_text"
                android:textSize="40sp"
                autofit:minTextSize="8sp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/fridayDay"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:layout_weight="1"
             android:background="@drawable/selector_item_onboarding">

            <com.vgfit.shefit.util.AutoResizeTextView
                android:id="@+id/fridayDayTxt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="26dp"
                android:layout_marginEnd="26dp"
                android:fontFamily="@font/inter_semibold"
                android:textColor="@drawable/selector_start_text"
                android:gravity="center|start"
                android:maxLines="1"
                android:text="Friday"
                android:textAllCaps="true"
                android:textSize="40sp"
                autofit:minTextSize="8sp" />

        </RelativeLayout>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:weightSum="2">

            <RelativeLayout
                android:id="@+id/satDay"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="7dp"
                android:layout_weight="1"
                 android:background="@drawable/selector_item_onboarding">

                <com.vgfit.shefit.util.AutoResizeTextView
                    android:id="@+id/satDayTxt"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="26dp"
                    android:layout_marginEnd="26dp"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center|start"
                    android:textColor="@drawable/selector_start_text"
                    android:maxLines="1"
                    android:text="Sat"
                    android:textAllCaps="true"
                    android:textSize="40sp"
                    autofit:minTextSize="8sp" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/sunDay"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="7dp"
                android:layout_weight="1"
                 android:background="@drawable/selector_item_onboarding">

                <com.vgfit.shefit.util.AutoResizeTextView
                    android:id="@+id/sunDayTxt"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="26dp"
                    android:layout_marginEnd="26dp"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center|start"
                    android:maxLines="1"
                    android:textColor="@drawable/selector_start_text"
                    android:text="Sun"
                    android:textAllCaps="true"
                    android:textSize="40sp"
                    autofit:minTextSize="8sp" />
            </RelativeLayout>

        </androidx.appcompat.widget.LinearLayoutCompat>
    </LinearLayout>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/linearLayoutCompat3"
        android:layout_width="match_parent"
        android:layout_height="66dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="50dp"
        android:orientation="horizontal"
        android:weightSum="1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <RelativeLayout
            android:id="@+id/backLayout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="0.35">

            <TextView
                android:id="@+id/backTxt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/inter_extrabold"
                android:text="back"
                android:textAllCaps="true"
                android:textColor="@drawable/selector_back_text"
                android:textSize="16sp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_continue"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_alignParentBottom="true"
            android:layout_weight="0.65"
            android:background="@drawable/selector_start"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:id="@+id/btn_continue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="27dp"
                android:background="@android:color/transparent"
                android:fontFamily="@font/inter_extrabold"
                android:text="NEXT"
                android:textAllCaps="true"
                android:textColor="@drawable/selector_start_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="17dp"
                android:layout_height="17dp"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="23dp"
                android:layout_marginBottom="30dp"
                android:src="@drawable/selector_start_arrow" />
        </RelativeLayout>
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="80dp"
        app:layout_behavior="@string/top_sheet_behavior"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/top_sheet"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:background="#000000"
            app:behavior_peekHeight="0dp"
            app:layout_behavior="@string/top_sheet_behavior">

            <TextView
                android:id="@+id/titleAlert"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="30dp"
                android:fontFamily="@font/inter_extrabold"
                android:text="Warning"
                android:textColor="#FA6164"
                android:textSize="14sp" />


            <TextView
                android:id="@+id/messageAlert"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginStart="10dp"
                android:layout_marginBottom="10dp"
                android:ellipsize="marquee"
                android:fadingEdge="horizontal"
                android:fontFamily="@font/inter_bold"
                android:marqueeRepeatLimit="marquee_forever"
                android:scrollHorizontally="true"
                android:singleLine="true"
                android:text="Top sheet content"
                android:textAppearance="?android:textAppearanceMedium"
                android:textColor="@color/text_color_lime"
                android:textSize="12sp" />

        </FrameLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>


</androidx.constraintlayout.widget.ConstraintLayout>