package com.vgfit.shefit.authorization;

import android.util.Log;

import androidx.annotation.NonNull;

import com.vgfit.shefit.BaseApplication;
import com.vgfit.shefit.util.Constant;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SecDevice {

    private SecDevice() {
        //private
    }

    public static String getUUID(PrefsUtilsWtContext prefsUtilsWtContext) {
        String uuId = prefsUtilsWtContext.getStringPreference(Constant.UUId);
        if (uuId == null) {
            uuId = UUID.randomUUID().toString();
            prefsUtilsWtContext.setStringPreference(Constant.UUId, uuId);
        }
        return uuId;
    }

    private static String md5(final String s) {
        final String MD5 = "MD5";
        try {
            // Create MD5 Hash
            MessageDigest digest = MessageDigest
                    .getInstance(MD5);
            digest.update(s.getBytes());
            byte[] messageDigest = digest.digest();

            // Create Hex String
            StringBuilder hexString = new StringBuilder();
            for (byte aMessageDigest : messageDigest) {
                StringBuilder h = new StringBuilder(Integer.toHexString(0xFF & aMessageDigest));
                while (h.length() < 2)
                    h.insert(0, "0");
                hexString.append(h);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException ignored) {
            //ignore exception
        }
        return "";
    }

    private static String md5Security(String uuId) {
        String u1 = uuId.toUpperCase();
        String a = uuId.replace("-", ")*$$"); //for SheFit
//        String a = UUid.replace("-", "{#@"); //for ProFit
        String b = md5(a);
        String c = u1 + b;
        return md5(c);
    }

    public static void getRefreshToken(PrefsUtilsWtContext prefsUtilsWtContext, SuccessToken successToken) {
        String refreshToken = prefsUtilsWtContext.getStringPreference(Constant.REFRESH_TOKEN);
        if (refreshToken == null) getLoginToken(prefsUtilsWtContext, successToken);
        else
            BaseApplication.getApiFemale().getRefreshToken(refreshToken).enqueue(new Callback<AccessToken>() {
                @Override
                public void onResponse(@NonNull Call<AccessToken> call, @NonNull Response<AccessToken> response) {
                    if (response.isSuccessful()) {
                        AccessToken token = response.body();
                        if (token != null) {
                            String tokenValid = "Bearer " + token.getAccess();
                            prefsUtilsWtContext.setStringPreference(Constant.ACCESS_TOKEN, tokenValid);
                            successToken.responseToken(tokenValid);
                        }
                    } else if (response.code() == 401) {
                        getLoginToken(prefsUtilsWtContext, successToken);
                    } else if (response.code() == 403) {
                        successToken.responseToken("403");
                    } else if (response.code() == 500) {
                        getLoginToken(prefsUtilsWtContext, successToken);
                    } else if (response.code() == 404) {
                        String tokenValid = "Bearer " ;
                        prefsUtilsWtContext.setStringPreference(Constant.ACCESS_TOKEN, tokenValid);
                        successToken.responseToken(tokenValid);
                    }
                }

                @Override
                public void onFailure(@NonNull Call<AccessToken> call, @NonNull Throwable t) {
                    //ignore failure
                }
            });
    }

    private static void getLoginToken(PrefsUtilsWtContext prefsUtilsWtContext, SuccessToken successToken) {
        String uuId = getUUID(prefsUtilsWtContext);
        Login login = new Login();
        login.setUsername(uuId);
        login.setPassword(md5Security(uuId));
        BaseApplication.getApiFemale().getToken(login.getUsername(), login.getPassword()).enqueue(new Callback<Token>() {
            @Override
            public void onResponse(@NonNull Call<Token> call, @NonNull Response<Token> response) {
                if (response.isSuccessful()) {
                    Token token = response.body();
                    if (token != null) {
                        String tokenValid = "Bearer " + token.getAccess();
                        prefsUtilsWtContext.setStringPreference(Constant.ACCESS_TOKEN, tokenValid);
                        prefsUtilsWtContext.setStringPreference(Constant.REFRESH_TOKEN, token.getRefresh());
                        successToken.responseToken(tokenValid);
                    }
                } else if (response.code() == 500) {
                    getLoginToken(prefsUtilsWtContext, successToken);
                } else if (response.code() == 404) {
                    String tokenValid = "Bearer ";
                    prefsUtilsWtContext.setStringPreference(Constant.ACCESS_TOKEN, tokenValid);
                    prefsUtilsWtContext.setStringPreference(Constant.REFRESH_TOKEN, tokenValid);
                    successToken.responseToken(tokenValid);
                }
            }

            @Override
            public void onFailure(@NonNull Call<Token> call, @NonNull Throwable t) {
                Log.e("TestLogin", "Error gelLoginToken ==> " + t.getMessage());
            }
        });
    }
}