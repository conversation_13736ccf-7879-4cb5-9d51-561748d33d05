package com.vgfit.shefit.authorization;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class Users {
    public Users() {
    }

    @Expose
    private String id;
    @Expose
    private String username;
    @SerializedName("first_name")
    @Expose
    private String firstName;
    @SerializedName("last_name")
    @Expose
    private String lastName;
    @Expose
    private String email;
    @Expose
    private int gender;
    @Expose
    private int age = 0;
    @Expose
    private float weight = 0;
    @SerializedName("weight_measure_unit")
    @Expose
    private String weightMeasureUnit = "kg";
    @Expose
    private double height = 0.0;
    @SerializedName("height_measure_unit")
    @Expose
    private String heightMeasureUnit = "cm";
    @SerializedName("body_fat")
    @Expose
    private int bodyFat = 0;
    @Expose
    private int experience = 0;
    @SerializedName("fitness_goal")
    @Expose
    private int fitnessGoal;
    @SerializedName("active_daily_workout")
    @Expose
    private int activeDailyWorkout = 0;
    @SerializedName("workout_days")
    @Expose
    private String workoutDays = "1,2";
    @Expose
    private String timezone;
    @SerializedName("goal_weight")
    @Expose
    private float goalWeight;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public float getWeight() {
        return weight;
    }

    public void setWeight(float weight) {
        this.weight = weight;
    }

    public String getWeightMeasureUnit() {
        return weightMeasureUnit;
    }

    public void setWeightMeasureUnit(String weightMeasureUnit) {
        this.weightMeasureUnit = weightMeasureUnit;
    }

    public double getHeight() {
        return height;
    }

    public void setHeight(double height) {
        this.height = height;
    }

    public String getHeightMeasureUnit() {
        return heightMeasureUnit;
    }

    public void setHeightMeasureUnit(String heightMeasureUnit) {
        this.heightMeasureUnit = heightMeasureUnit;
    }

    public int getBodyFat() {
        return bodyFat;
    }

    public void setBodyFat(int bodyFat) {
        this.bodyFat = bodyFat;
    }

    public int getExperience() {
        return experience;
    }

    public void setExperience(int experience) {
        this.experience = experience;
    }

    public int getFitnessGoal() {
        return fitnessGoal;
    }

    public void setFitnessGoal(int fitnessGoal) {
        this.fitnessGoal = fitnessGoal;
    }

    public int getActiveDailyWorkout() {
        return activeDailyWorkout;
    }

    public void setActiveDailyWorkout(int activeDailyWorkout) {
        this.activeDailyWorkout = activeDailyWorkout;
    }

    public String getWorkoutDays() {
        return workoutDays;
    }

    public void setWorkoutDays(String workoutDays) {
        this.workoutDays = workoutDays;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public float getGoalWeight() {
        return goalWeight;
    }

    public void setGoalWeight(float goalWeight) {
        this.goalWeight = goalWeight;
    }
}
