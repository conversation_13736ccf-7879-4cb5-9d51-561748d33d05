package com.vgfit.shefit.authorization;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class DayExercises {
    @Expose
    private int id;
    @Expose
    private Exercise exercise;
    @Expose
    private int day;
    @Expose
    private String name;
    @SerializedName("warm_up_sets")
    @Expose
    private String warmUpSets;
    @SerializedName("warm_up_repetitions")
    @Expose
    private String warmUpRepetitions;
    @Expose
    private String sets;
    @Expose
    private String repetitions;
    @Expose
    private int order;

    public DayExercises() {
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Exercise getExercise() {
        return exercise;
    }

    public void setExercise(Exercise exercise) {
        this.exercise = exercise;
    }

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWarmUpSets() {
        return warmUpSets;
    }

    public void setWarmUpSets(String warmUpSets) {
        this.warmUpSets = warmUpSets;
    }

    public String getWarmUpRepetitions() {
        return warmUpRepetitions;
    }

    public void setWarmUpRepetitions(String warmUpRepetitions) {
        this.warmUpRepetitions = warmUpRepetitions;
    }

    public String getSets() {
        return sets;
    }

    public void setSets(String sets) {
        this.sets = sets;
    }

    public String getRepetitions() {
        return repetitions;
    }

    public void setRepetitions(String repetitions) {
        this.repetitions = repetitions;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }
}