package com.vgfit.shefit.authorization;

import com.google.gson.annotations.Expose;

import java.util.ArrayList;

public class Exercise {
    @Expose
    private int id;
    @Expose
    private String name;
    @Expose
    private String description;
    @Expose
    private ArrayList<ImagesDaily> images;
    @Expose
    private String video;
    @Expose
    private int category;

    public Exercise() {
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ArrayList<ImagesDaily> getImages() {
        return images;
    }

    public void setImages(ArrayList<ImagesDaily> images) {
        this.images = images;
    }

    public String getVideo() {
        return video;
    }

    public void setVideo(String video) {
        this.video = video;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }
}