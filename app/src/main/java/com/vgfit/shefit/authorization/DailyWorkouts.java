package com.vgfit.shefit.authorization;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;

public class DailyWorkouts {
    @Expose
    private String description;
    @SerializedName("warm_up_description")
    @Expose
    private String warmUpDescription;
    @Expose
    private String recommendations;
    @Expose
    private ArrayList<ExerciseDaily> exercises;

    public DailyWorkouts() {
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getWarmUpDescription() {
        return warmUpDescription;
    }

    public void setWarmUpDescription(String warmUpDescription) {
        this.warmUpDescription = warmUpDescription;
    }

    public String getRecommendations() {
        return recommendations;
    }

    public void setRecommendations(String recommendations) {
        this.recommendations = recommendations;
    }

    public ArrayList<ExerciseDaily> getExercises() {
        return exercises;
    }

    public void setExercises(ArrayList<ExerciseDaily> exercises) {
        this.exercises = exercises;
    }
}