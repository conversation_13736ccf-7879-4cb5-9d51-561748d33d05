package com.vgfit.shefit.draw_utils;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathEffect;
import android.graphics.PathMeasure;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

public class PathView extends View {
    Path path;
    Paint paint;
    float length;

    public PathView(Context context) {
        super(context);
    }

    public PathView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public PathView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void init(int startX, int starY, int endX, int endY) {
        paint = new Paint();
        paint.setColor(Color.parseColor("#7BF09F"));
        paint.setStrokeWidth(5);
        paint.setStyle(Paint.Style.STROKE);
        path = new Path();
        path.moveTo(startX, starY);
        path.lineTo(startX, endY);
        path.lineTo(endX, endY);
        DashPathEffect dashPathEffect = new DashPathEffect(new float[]{15f, 5f}, 0);
        paint.setPathEffect(dashPathEffect);
        // Measure the path
        PathMeasure measure = new PathMeasure(path, false);
        length = measure.getLength();
        ObjectAnimator animator = ObjectAnimator.ofFloat(PathView.this, "phase", 1.0f, 0.0f);
        animator.setDuration(500);
        animator.start();
    }

    //is called by animator object
    @Keep
    public void setPhase(float phase) {
        paint.setPathEffect(createPathEffect(length, phase));
        invalidate();//will call onDraw
    }

    private static PathEffect createPathEffect(float pathLength, float phase) {
        return new DashPathEffect(new float[]{pathLength, pathLength},
                Math.max(phase * pathLength, (float) 0.0));
    }

    @Override
    public void onDraw(@NonNull Canvas c) {
        super.onDraw(c);
        c.drawPath(path, paint);
    }
}