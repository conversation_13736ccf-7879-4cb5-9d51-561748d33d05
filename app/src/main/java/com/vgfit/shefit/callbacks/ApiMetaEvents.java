package com.vgfit.shefit.callbacks;

import com.vgfit.shefit.api.model.MetaAppActivateInfo;
import com.vgfit.shefit.api.model.MetaAppInstallInfo;
import com.vgfit.shefit.api.model.MetaEventModel;
import com.vgfit.shefit.api.model.MetaInitiatedCheckoutInfo;
import com.vgfit.shefit.api.model.MetaTrialInfo;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface ApiMetaEvents {
    @POST("extinfo")
    Call<Void> sendEventMeta(@Body MetaEventModel metaEventModel);

    @POST("app_install")
    Call<Void> sendAppInstall(@Body MetaAppInstallInfo metaAppInstallInfo);

    @POST("trial")
    Call<Void> sendTrial(@Body MetaTrialInfo metaTrialInfo);

    @POST("app_activate")
    Call<Void> sendAppActivate(@Body MetaAppActivateInfo metaAppActivateInfo);

    @POST("initiated_checkout")
    Call<Void> sendInitiatedCheckout(@Body MetaInitiatedCheckoutInfo metaInitiatedCheckoutInfo);
}
