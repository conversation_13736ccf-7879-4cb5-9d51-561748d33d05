package com.vgfit.shefit.callbacks;

import com.vgfit.shefit.ItemTemperature;
import com.vgfit.shefit.api.modelweather.CurrentWeather;

import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

public interface ApiWeather {
    //    https://api.weatherstack.com/current?access_key=********************************&query=47.043344628264386,28.85581588306049
    @GET("current")
    Call<ItemTemperature> getWeather(@Query("access_key") String key, @Query("query") String latitude);
    @GET("current")
    Call<CurrentWeather> getWeatherNew(@Query("access_key") String key, @Query("query") String location);
}