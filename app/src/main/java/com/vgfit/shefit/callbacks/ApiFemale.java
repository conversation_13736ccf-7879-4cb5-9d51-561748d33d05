package com.vgfit.shefit.callbacks;

import com.vgfit.shefit.api.loginweb.model.SubscribeInfoWeb;
import com.vgfit.shefit.api.loginweb.model.UserExists;
import com.vgfit.shefit.api.loginweb.model.UserInfoWeb;
import com.vgfit.shefit.api.model.UpdatesModel;
import com.vgfit.shefit.authorization.AccessToken;
import com.vgfit.shefit.authorization.DailyWorkouts;
import com.vgfit.shefit.authorization.DayExercises;
import com.vgfit.shefit.authorization.Token;
import com.vgfit.shefit.authorization.Users;
import com.vgfit.shefit.json.model.Categories;
import com.vgfit.shefit.json.model.Exercises;
import com.vgfit.shefit.json.model.Items;
import com.vgfit.shefit.json.model.LanguageServer;
import com.vgfit.shefit.json.model.Levels;
import com.vgfit.shefit.json.model.MealsServer;
import com.vgfit.shefit.json.model.NutritionPlans;
import com.vgfit.shefit.json.model.Supersets;
import com.vgfit.shefit.json.model.TranslatorData;
import com.vgfit.shefit.json.model.Updates;
import com.vgfit.shefit.json.model.Workouts;
import com.vgfit.shefit.realm.Category;
import com.vgfit.shefit.realm.CoverDayPlan;
import com.vgfit.shefit.realm.CoverVideoPlan;
import com.vgfit.shefit.realm.DaysWorkoutsPlan;
import com.vgfit.shefit.realm.Exercise;
import com.vgfit.shefit.realm.Languages;
import com.vgfit.shefit.realm.Localizations;
import com.vgfit.shefit.realm.Meal;
import com.vgfit.shefit.realm.NutritionPlan;
import com.vgfit.shefit.realm.PhotoMeal;
import com.vgfit.shefit.realm.Superset;
import com.vgfit.shefit.realm.Workout;
import com.vgfit.shefit.realm.WorkoutExercise;
import com.vgfit.shefit.realm.WorkoutLevel;

import java.util.ArrayList;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.PATCH;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface ApiFemale {
    //supersets
    @GET("supersets")
    Call<ArrayList<Supersets>> getAllSupersets();

    @GET("supersets/{supersetId}")
    Call<Supersets> getSuperset(@Path("supersetId") String idSuperset);

    //categories
    @GET("categories")
    Call<ArrayList<Categories>> getAllCategories();

    @GET("categories/{categoryId}")
    Call<Categories> getCategory(@Path("categoryId") String idCategory);

    //workouts
    @GET("workouts")
    Call<ArrayList<Workouts>> getAllWorkouts();

    @GET("workouts/{workoutId}")
    Call<Workouts> getWorkout(@Path("workoutId") String idWorkout);

    @GET("workouts/{workoutId}/items")
    Call<ArrayList<Items>> getAllItemsFromWorkout(@Path("workoutId") String idWorkout);

    @GET("workouts/{workoutId}/items/{itemId}")
    Call<Items> getItemFromWorkout(@Path("workoutId") String idWorkout, @Path("itemId") String idItem);

    //exercises
    @GET("exercises")
    Call<ArrayList<Exercises>> getAllExercises();

    @GET("exercises/{exerciseId}")
    Call<Exercises> getExercise(@Path("exerciseId") String idExercise);

    //nutrition plans
    @GET("nutrition-plans")
    Call<ArrayList<NutritionPlans>> getAllNutritionPlans();

    @GET("nutrition-plans/{nutritionPlanId}")
    Call<NutritionPlans> getNutritionPlan(@Path("nutritionPlanId") String idNutritionPlan);

    //levels
    @GET("levels")
    Call<ArrayList<Levels>> getAllLevels();

    @GET("levels/{levelId}")
    Call<Levels> getLevel(@Path("levelId") String idLevel);
//    Call<Levels> getLevel(@Header("Authorization") String token,@Path("levelId") String idLevel);

    //meals
    @GET("meals")
    Call<ArrayList<MealsServer>> getAllMeals();

    @GET("meals/{mealId}")
    Call<MealsServer> getMeal(@Path("mealId") String idMeal);

    //languages
    @GET("languages")
    Call<ArrayList<LanguageServer>> getAllLanguages();

    //localizations
    @GET("localizations/{languageCode}")
    Call<ArrayList<TranslatorData>> getAllLocalizations(@Path("languageCode") String languageCode);


    @GET("localizations/{languageCode}/{key}")
    Call<TranslatorData> getLocalization(@Path("languageCode") String languageCode, @Path("key") String key);

    //updates
    @GET("updates/{timestamp}")
    Call<ArrayList<Updates>> getUpdates(@Path("timestamp") String timestamp);


    @FormUrlEncoded
    @POST("auth/token")
    Call<Token> getToken(@Field("username") String username, @Field("password") String password);

    @FormUrlEncoded
    @POST("auth/token/refresh")
    Call<AccessToken> getRefreshToken(@Field("refresh") String refreshToken);

    @PATCH("users/self")
    Call<Users> setUsersData(@Header("Authorization") String token, @Body Users users);

    @FormUrlEncoded
    @PATCH("users/self")
    Call<Users> updateProfileFull(@Header("Authorization") String token, @Field("first_name") String firstName, @Field("gender") int gender, @Field("age") int age, @Field("weight") double weight, @Field("weight_measure_unit") String weightMeasureUnit, @Field("height") double height, @Field("height_measure_unit") String heightMeasureUnit, @Field("experience") int workLevel, @Field("timezone") String timezone, @Field("goal_weight") double goalWeight, @Field("workout_days") String workoutDays, @Field("fitness_goal") int fitnessGoal);

    @GET("users/self")
    Call<Users> getUserData(@Header("Authorization") String token);

    @GET("dailyworkouts/current")
    Call<ArrayList<DailyWorkouts>> getDailyWorkouts(@Header("Authorization") String token);

    @GET("dayexercises/{dayExerciseId}")
    Call<DayExercises> getExerciseDayData(@Header("Authorization") String token, @Path("dayExerciseId") int idDayExercise);

    @GET("dayexercises")
    Call<ArrayList<DayExercises>> getExerciseDayDataList(@Header("Authorization") String token);


    /*** Localization***/
    @GET("localizations/{languageCode}")
    Call<ArrayList<TranslatorData>> getTranslatesLang(@Header("Authorization") String token, @Path("languageCode") String languageCode);

    @GET("localizations/{languageCode}/{key}")
    Call<Localizations> getLocalizationServer(@Header("Authorization") String token, @Path("languageCode") String languageCode, @Path("key") String key);

    /*** Category***/
    @GET("categories")
    Call<ArrayList<Category>> getAllCategory(@Header("Authorization") String token);

    @GET("categories/{categoryId}")
    Call<Category> getCategoryServer(@Header("Authorization") String token, @Path("categoryId") String idCategory);


    /*** Exercise***/
    @GET("exercises")
    Call<ArrayList<Exercise>> getAllExercisesData(@Header("Authorization") String token);

    @GET("exercises/{exerciseId}")
    Call<Exercise> getExerciseServer(@Header("Authorization") String token, @Path("exerciseId") String idExercise);

    /*** LanguageServer***/
    @GET("languages")
    Call<ArrayList<Languages>> getAllLanguages(@Header("Authorization") String token);

    @GET("languages/{languageId}")
    Call<Languages> getLanguageServer(@Header("Authorization") String token, @Path("languageId") String idLanguage);


    /*** Meals***/
    @GET("meals")
    Call<ArrayList<Meal>> getAllMealsServer(@Header("Authorization") String token);

    @GET("meals/{mealId}")
    Call<Meal> getMealServer(@Header("Authorization") String token, @Path("mealId") String idMeal);

    //nutrition plans
    @GET("nutrition-plans")
    Call<ArrayList<NutritionPlan>> getAllNutritionPlansServer(@Header("Authorization") String token);


    /*** Suepersets***/

    @GET("supersets")
    Call<ArrayList<Superset>> getAllSupersetsServer(@Header("Authorization") String token);

    @GET("supersets/{supersetId}")
    Call<Superset> getSupersetServer(@Header("Authorization") String token, @Path("supersetId") String idSuperset);

    /*** Workouts***/
    @GET("workouts/{workoutId}")
    Call<Workout> getWorkoutServer(@Header("Authorization") String token, @Path("workoutId") String idWorkout);

    @GET("workouts/{workoutId}/items/{itemId}")
    Call<WorkoutExercise> getItemFromWorkoutServer(@Header("Authorization") String token, @Path("workoutId") String idWorkout, @Path("itemId") String idItem);

    /*** Levels***/
    @GET("levels/{levelId}")
    Call<WorkoutLevel> getLevelServer(@Header("Authorization") String token, @Path("levelId") String idLevel);

    /*** Updates***/
    @GET("updates/{timestamp}")
    Call<ArrayList<UpdatesModel>> getUpdatesServer(@Header("Authorization") String token, @Path("timestamp") long timestamp);

    /**
     * Daily Workouts per date
     **/
    @GET("dailyworkouts/current")
    Call<DaysWorkoutsPlan> getDailyWorkoutPlan(@Header("Authorization") String token);

    /**
     * Photo Meal in Workout Plan
     **/
    @GET("planvideos/meal")
    Call<ArrayList<PhotoMeal>> getPhotoMealPlan(@Header("Authorization") String token);

    /*** Cover Video Plan **/
    @GET("planvideos/workout")
    Call<ArrayList<CoverVideoPlan>> getCoverVideoPlan(@Header("Authorization") String token);

    @GET("weekday-images")
    Call<ArrayList<CoverDayPlan>> getCoverDayPlan(@Header("Authorization") String token);

    /*********** LOGIN WEB PARTS REQUEST**/
    @FormUrlEncoded
    @POST("users/login")
    Call<Token> getUser(@Field("email") String email, @Field("password") String password, @Field("mobile") Boolean mobile);

    @FormUrlEncoded
    @POST("users/login")
    Call<Object> getUserAdvanced(@Field("email") String email, @Field("password") String password);

    @GET("payment/get_subscription_info")
    Call<SubscribeInfoWeb> getSubscribeInfo(@Header("Authorization") String token);

    @GET("payment/get_subscription_info")
    Call<Object> getSubscribeInfoAdvanced(@Header("Authorization") String token);

    @FormUrlEncoded
    @POST("users/forgot_password")
    Call<Void> sendForgotPassword(@Field("email") String email);

    @POST("users/send_delete_instructions")
    Call<Void> sendDeleteInstruction(@Header("Authorization") String token);

    @GET("users/self")
    Call<Object> getUserInfoAdvanced(@Header("Authorization") String token);

    @GET("users/self")
    Call<UserInfoWeb> getUserInfo(@Header("Authorization") String token);

    @GET("users/exists")
    Call<UserExists> isExistUser(@Header("Authorization") String token, @Query("user_id") String userId);
}
