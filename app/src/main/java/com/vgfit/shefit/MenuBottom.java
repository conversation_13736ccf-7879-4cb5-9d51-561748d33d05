package com.vgfit.shefit;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;
import static com.vgfit.shefit.util.screen.PresentBottomBar.isPresentBottomBar;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.vgfit.shefit.util.Translate;

public class MenuBottom {
    private final RelativeLayout containerMenu;
    private final MainActivity activity;
    private RelativeLayout item1;
    private RelativeLayout item2;
    private RelativeLayout item3;
    private TextView item1Txt;
    private TextView item2Txt;
    private TextView item3Txt;
    private final String tabSelected1 = "tab0";
    private final String tabSelected2 = "tab1";
    private final String tabSelected3 = "tab4";
    private boolean isCustomMenu = false;

    public MenuBottom(MainActivity activity, RelativeLayout containerMenu) {
        this.activity = activity;
        this.containerMenu = containerMenu;
        initView();
    }

    public MenuBottom(MainActivity activity, RelativeLayout containerMenu, boolean isCustomMenu) {
        this.containerMenu = containerMenu;
        this.activity = activity;
        this.isCustomMenu = isCustomMenu;
        initView();
    }

    private void initView() {
        LayoutInflater inflater = (LayoutInflater) activity.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View menuBottom;
        if (isCustomMenu)
            menuBottom = inflater.inflate(R.layout.menu_buttom_custom, containerMenu, false);
        else
            menuBottom = inflater.inflate(R.layout.menu_buttom, containerMenu, false);

        item1 = menuBottom.findViewById(R.id.item1);
        item2 = menuBottom.findViewById(R.id.item2);
        item3 = menuBottom.findViewById(R.id.item3);
        item1Txt = menuBottom.findViewById(R.id.item1Txt);
        item2Txt = menuBottom.findViewById(R.id.item2Txt);
        item3Txt = menuBottom.findViewById(R.id.item3Txt);
        containerMenu.addView(menuBottom);
        isPresentBottomBar(activity, containerMenu);
        setupMenu();
    }

    private void setupMenu() {

        item1.setOnClickListener(v -> {
            setVibrate(v);
            item1.setSelected(true);
            item2.setSelected(false);
            item3.setSelected(false);
            activity.onTabChanged(tabSelected1);
        });
        item2.setOnClickListener(v -> {
            setVibrate(v);
            item1.setSelected(false);
            item2.setSelected(true);
            item3.setSelected(false);
            activity.onTabChanged(tabSelected2);
        });
        item3.setOnClickListener(v -> {
            setVibrate(v);
            item1.setSelected(false);
            item2.setSelected(false);
            item3.setSelected(true);
            activity.onTabChanged(tabSelected3);
        });
        translateMenu();
        String selectedMenu = activity.tabSelected;
        defaultSelectedMenu(selectedMenu);
    }

    private void defaultSelectedMenu(String selectedMenu) {
        item1.setSelected(false);
        item2.setSelected(false);
        item3.setSelected(false);
        switch (selectedMenu) {
            case tabSelected1:
                item1.setSelected(true);
                break;
            case tabSelected2:
                item2.setSelected(true);
                break;
            case tabSelected3:
                item3.setSelected(true);
                break;
        }
    }

    private void translateMenu() {
        item1Txt.setText(Translate.getValue("plan"));
        item2Txt.setText(Translate.getValue("workouts"));
        item3Txt.setText(Translate.getValue("profile"));
    }
}
