package com.vgfit.shefit.apprate.rateimplementation;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;

public class SharedPreferenceAd {
    private SharedPreferences sharedPreferences;
    private SharedPreferences.Editor editor;

    public SharedPreferenceAd() {
    }

    public int getSharedPreferences_int(Context context, String launchCount) {
        if (sharedPreferences == null) {
            sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context);
        }
        return sharedPreferences.getInt(launchCount, 0);
    }

    public void saveSharedPreferences_int(String launchCount, int launch_count) {
        if (editor == null) {
            editor = sharedPreferences.edit();
        }
        editor.putInt(launchCount, launch_count);
        editor.apply();
    }

    public int getSharedPreferencesAmount_int(Context context) {
        if (sharedPreferences == null) {
            sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context);
        }
        return sharedPreferences.getInt("amountDownloader", 0);
    }

    public void saveSharedPreferencesAmount_int(int amount) {
        if (editor == null) {
            editor = sharedPreferences.edit();
        }
        editor.putInt("amountDownloader", amount);
        editor.apply();
    }


    public String getSharedPreferences_string(Context context, String interst) {
        if (sharedPreferences == null) {
            sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context);
        }
        return sharedPreferences.getString(interst, "");
    }

    public void saveSharedPreferences_string(String interst, String valueOf, Context context) {
        if (editor == null) {
            editor = sharedPreferences.edit();
        }
        editor.putString(interst, valueOf);
        editor.apply();
    }

    public void saveSharedPreferencesBoolean(String key, boolean value) {
        if (editor == null) {
            editor = sharedPreferences.edit();
        }
        editor.putBoolean(key, value);
        editor.apply();
    }
    public boolean getSharedPreferencesBoolean(Context context, String key) {
        if (sharedPreferences == null) {
            sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context);
        }
        return sharedPreferences.getBoolean(key, false);
    }
}