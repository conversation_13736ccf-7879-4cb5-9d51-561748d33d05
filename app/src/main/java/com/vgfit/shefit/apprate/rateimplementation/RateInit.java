package com.vgfit.shefit.apprate.rateimplementation;

import android.content.Context;


public class RateInit {

    private static final String LAUNCH_COUNT = "launch_count_beauty";
    private static final String SHOWED_IN_LAUNCHED_COUNT = "is_launched_count";
    Context context;

    public RateInit(Context context) {
        this.context = context;
    }

    public void appIsLaunched() {
        SharedPreferenceAd sh = new SharedPreferenceAd();
        int launch_count = sh.getSharedPreferences_int(context, LAUNCH_COUNT);
        launch_count++;
        sh.saveSharedPreferences_int(LAUNCH_COUNT, launch_count);
    }

    public boolean isValidShowRate() {
        SharedPreferenceAd sh = new SharedPreferenceAd();
        int launch_count = sh.getSharedPreferences_int(context, LAUNCH_COUNT);
        int show_launched_count = sh.getSharedPreferences_int(context, SHOWED_IN_LAUNCHED_COUNT);

        if (launch_count > 0 && launch_count != show_launched_count) {
            sh.saveSharedPreferences_int(SHOWED_IN_LAUNCHED_COUNT, launch_count);
            return true;
        }
        return false;
    }
}