# Shapy Local Push Notifications System

## Overview

This system implements local push notifications for the Shapy app with 3 different user scenarios:
- **Free/Trial Users**: 30-day engagement flow
- **Premium Active**: Motivation and progress flow  
- **Premium Cancelled**: Re-engagement flow

## Features

✅ **Local notifications** (no server required)
✅ **WorkManager powered** (reliable, battery-efficient)
✅ **Automatic flow switching** (no overlapping notifications)
✅ **Configurable timing** via JSON files
✅ **Logging support** for debugging
✅ **Respects user permissions**
✅ **Survives app restarts** and system reboots
✅ **Easy integration** with existing app logic

## Architecture

```
notification/
├── model/                          # Data models
│   ├── LocalNotification.java      # Notification wrapper
│   ├── NotificationTrigger.java    # Trigger timing
│   ├── Reminder.java               # Individual notification
│   ├── ReminderType.java           # Flow types enum
│   └── TimeModel.java              # Time representation
├── service/                        # Core services
│   ├── NotificationManager.java    # WorkManager-powered notification handling
│   ├── NotificationWorker.java     # WorkManager worker class
│   ├── NotificationService.java    # Business logic
│   ├── NotificationHelper.java     # Easy integration API
│   └── NotificationIntegrationExample.java # Usage examples
└── README.md                       # This file
```

## JSON Configuration

Notification content is stored in `assets/notificationJson/`:

- `free_usage.json` - Free/Trial user notifications
- `premium_active.json` - Premium active user notifications  
- `premium_canceled.json` - Premium cancelled user notifications

### JSON Structure
```json
[
  {
    "id": 0,
    "type": "free_usage",
    "dayOffset": 0,
    "intervalSec": 300,  // 5 minutes (Day 0 only)
    "title": "free_usage_0_title",    // Translation key for Translate.getValue()
    "body": "free_usage_0_body"      // Translation key for Translate.getValue()
  },
  {
    "id": 1,
    "type": "free_usage",
    "dayOffset": 1,
    "time": {"hour": 8, "min": 0, "sec": 0},  // 08:00 local time
    "title": "free_usage_1_title",    // Translation key for Translate.getValue()
    "body": "free_usage_1_body"      // Translation key for Translate.getValue()
  }
]
```

**Note**: `title` and `body` fields contain translation keys that are processed through `Translate.getValue()` to get localized text.

## Integration

### Basic Usage

```java
// Get the helper instance
NotificationHelper helper = NotificationHelper.getInstance(context);

// Start appropriate flow based on user status
helper.onAppFirstLaunch();        // Free/Trial flow
helper.onPremiumSubscribed();     // Premium active flow  
helper.onPremiumCancelled();      // Premium cancelled flow
helper.onPremiumResubscribed();   // Back to premium active

// Cancel all notifications
helper.cancelAllNotifications();
```

### Integration Points

1. **App First Launch** (after onboarding):
```java
NotificationHelper.getInstance(this).onAppFirstLaunch();
```

2. **Premium Subscription**:
```java
NotificationHelper.getInstance(this).onPremiumSubscribed();
```

3. **Premium Cancellation**:
```java
NotificationHelper.getInstance(this).onPremiumCancelled();
```

4. **Premium Resubscription**:
```java
NotificationHelper.getInstance(this).onPremiumResubscribed();
```

## Flow Logic

### Free/Trial Flow
- **Trigger**: First app launch
- **Day 0**: 5 minutes after app close
- **Day 1-30**: Daily at 08:00 local time
- **Content**: Workout benefits, motivation, soft Premium upsell (from Day 9)

### Premium Active Flow  
- **Trigger**: Premium subscription
- **Schedule**: Days 1, 3, 5, 7, 10, 14, 18, 22, 26, 30
- **Time**: 08:00 local time
- **Content**: Motivation, progress tracking, self-care reminders

### Premium Cancelled Flow
- **Trigger**: 1 day after cancellation
- **Schedule**: Days 1, 3, 5, 7, 10, 14, 18, 22, 26, 30 after cancellation
- **Time**: 08:00 local time  
- **Content**: Supportive re-engagement, feature reminders

## Technical Details

### WorkManager Benefits
- **Battery Optimized**: Respects Doze Mode and App Standby automatically
- **Reliable Execution**: Guaranteed execution even after app restarts
- **Constraint Support**: Can add WiFi, charging, or other constraints
- **Retry Logic**: Built-in retry mechanism for failed notifications
- **Modern API**: Recommended by Google for background tasks

### Permissions Required
```xml
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
<!-- WorkManager handles scheduling automatically - no additional permissions needed -->
```

### Dependencies Required
```gradle
implementation 'androidx.work:work-runtime:2.10.0'
```

### Storage
- Flow state stored in SharedPreferences
- WorkManager handles work persistence automatically
- No database required for notifications
- Automatic cleanup on flow changes

## Debugging

All operations are logged with tag patterns:
- `NotificationManager` - Scheduling and display
- `NotificationService` - Business logic
- `NotificationHelper` - Integration calls

## Customization

### Update Notification Content
1. Add translation keys to your localization system
2. Update JSON files in `assets/notificationJson/` with the translation keys
3. Rebuild app - changes take effect immediately

**Example**:
- JSON: `"title": "workout_reminder_title"`
- Localization: Add `workout_reminder_title` to your translation files
- Runtime: `Translate.getValue("workout_reminder_title")` returns localized text

### Modify Timing
1. Update `dayOffset` and `time` in JSON files
2. For Day 0 timing, modify `intervalSec` (currently 300 = 5 minutes)

### Add New Flows
1. Add new `ReminderType` enum value
2. Create corresponding JSON file
3. Update `NotificationService.getJsonFileNameForType()`
4. Add helper methods in `NotificationHelper`

## Testing

Use `NotificationIntegrationExample` class for testing different scenarios:

```java
// Test free flow
NotificationIntegrationExample.onAppFirstLaunch(context);

// Test premium flow  
NotificationIntegrationExample.onUserSubscribedToPremium(context);

// Check current status
NotificationIntegrationExample.checkCurrentNotificationStatus(context);
```

## Notes

- **Powered by WorkManager** for maximum reliability and battery efficiency
- **Survives system events**: app updates, device reboots, and memory pressure
- **Respects Android power management**: Doze Mode, App Standby, battery optimization
- Only one flow active at a time (automatic cancellation)
- Respects system notification permissions
- Works offline (no internet required)
- **Better than AlarmManager**: More reliable on modern Android versions
