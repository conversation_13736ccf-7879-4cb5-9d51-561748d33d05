package com.vgfit.shefit.notification.model;

import java.util.Date;

public class NotificationTrigger {
    private final String identifier;
    private final Date triggerTime;
    private final boolean repeats;

    public NotificationTrigger(String identifier, Date triggerTime, boolean repeats) {
        this.identifier = identifier;
        this.triggerTime = triggerTime;
        this.repeats = repeats;
    }

    public String getIdentifier() { return identifier; }
    public Date getTriggerTime() { return triggerTime; }
    public boolean isRepeats() { return repeats; }
}