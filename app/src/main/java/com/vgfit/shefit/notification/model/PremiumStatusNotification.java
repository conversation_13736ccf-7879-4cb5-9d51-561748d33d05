package com.vgfit.shefit.notification.model;

public enum PremiumStatusNotification {
    NOT_SET(-1),
    FALSE(0),
    ACTIVE(1);

    private final int value;

    PremiumStatusNotification(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static PremiumStatusNotification fromValue(int value) {
        for (PremiumStatusNotification status : values()) {
            if (status.value == value) {
                return status;
            }
        }
        return NOT_SET;
    }
}