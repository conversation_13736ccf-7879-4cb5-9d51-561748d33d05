package com.vgfit.shefit.notification.service;

import android.content.Context;
import android.util.Log;

import java.util.Date;

/**
 * Example integration class showing how to use the notification system
 * in different parts of the Shapy app.
 * <p>
 * This is just an example - you should integrate these calls into your actual
 * business logic where subscription status changes occur.
 */
public class NotificationIntegrationExample {

    /**
     * Call this when user first opens the app (onboarding complete)
     * This starts the Free/Trial notification flow
     */
    public static void onAppFirstLaunch(Context context) {
        NotificationHelper.getInstance(context).onAppFirstLaunch();
    }

    /**
     * Call this when user successfully subscribes to Premium
     * This stops free flow and starts premium active flow
     */
    public static void onUserSubscribedToPremium(Context context, Date date) {
        NotificationHelper.getInstance(context).onPremiumSubscribed(date);
    }

    /**
     * Call this when user cancels Premium subscription
     * This stops premium active flow and starts premium cancelled flow
     */
    public static void onUserCancelledPremium(Context context) {
        NotificationHelper.getInstance(context).onPremiumCancelled();
    }

    /**
     * Call this when user resubscribes to Premium after cancellation
     * This stops cancelled flow and starts premium active flow again
     */
    public static void onUserResubscribedToPremium(Context context, Date date) {
        NotificationHelper.getInstance(context).onPremiumResubscribed(date);
    }

    /**
     * Call this if user opts out of notifications or for testing
     */
    public static void cancelAllNotifications(Context context) {
        NotificationHelper.getInstance(context).cancelAllNotifications();
    }

    /**
     * Example of how to check current notification flow
     */
    public static void checkCurrentNotificationStatus(Context context) {
        NotificationHelper helper = NotificationHelper.getInstance(context);

        if (helper.isInFreeFlow()) {
            // User is in free usage flow
            Log.d("NotificationStatus", "User is in FREE usage flow");
        } else if (helper.isInPremiumActiveFlow()) {
            // User is in premium active flow
            Log.d("NotificationStatus", "User is in PREMIUM ACTIVE flow");
        } else if (helper.isInPremiumCancelledFlow()) {
            // User is in premium cancelled flow
            Log.d("NotificationStatus", "User is in PREMIUM CANCELLED flow");
        } else {
            // No active flow
            Log.d("NotificationStatus", "No active notification flow");
        }
    }
}

/*
INTEGRATION POINTS IN YOUR APP:

1. In BaseApplication.onCreate() or after onboarding:
   NotificationIntegrationExample.onAppFirstLaunch(this);

2. In your subscription/billing logic when user subscribes:
   NotificationIntegrationExample.onUserSubscribedToPremium(this);

3. In your subscription/billing logic when user cancels:
   NotificationIntegrationExample.onUserCancelledPremium(this);

4. In your subscription/billing logic when user resubscribes:
   NotificationIntegrationExample.onUserResubscribedToPremium(this);

5. In settings or for testing:
   NotificationIntegrationExample.cancelAllNotifications(this);

IMPORTANT NOTES:
- Notifications are scheduled locally and don't require internet
- Each flow automatically cancels previous flows (no overlap)
- Notifications are sent at 08:00 local time (except Day 0 which is 5 minutes after app close)
- All notification texts come from the JSON files in assets/notificationJson/
- You can update notification content by modifying the JSON files
- The system respects user notification permissions
*/
