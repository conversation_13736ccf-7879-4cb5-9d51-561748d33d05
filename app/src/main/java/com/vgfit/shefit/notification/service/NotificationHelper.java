package com.vgfit.shefit.notification.service;

import android.content.Context;
import android.util.Log;

import com.vgfit.shefit.notification.model.ReminderType;

import java.util.Date;

/**
 * Helper class for managing notification flows in the Shapy app.
 * This class provides easy-to-use methods for triggering different notification scenarios.
 */
public class NotificationHelper {
    private static final String TAG = "NotificationHelper";

    private static NotificationHelper instance;
    private final NotificationService notificationService;

    private NotificationHelper(Context context) {
        this.notificationService = new NotificationService(context);
    }

    public static synchronized NotificationHelper getInstance(Context context) {
        if (instance == null) {
            instance = new NotificationHelper(context.getApplicationContext());
        }
        return instance;
    }

    /**
     * Call this when user first opens the app (Free/Trial scenario)
     * Starts the 30-day free usage notification flow
     */
    public void onAppFirstLaunch() {
        notificationService.startFreeUsageFlow();
    }

    /**
     * Call this when user subscribes to Premium with custom start date
     * Stops free flow and starts premium active flow
     * Use this for web users who registered earlier
     */
    public void onPremiumSubscribed(Date startDate) {
        Log.d(TAG, "Premium subscribed - starting premium active flow with custom start date: " + startDate);
        notificationService.startPremiumActiveFlow(startDate);
    }

    /**
     * Call this when user cancels Premium subscription
     * Stops premium active flow and starts premium cancelled flow
     */
    public void onPremiumCancelled() {
        Log.d(TAG, "Premium cancelled - starting premium cancelled flow");
        notificationService.startPremiumCancelledFlow();
    }

    /**
     * Call this when user resubscribes to Premium after cancellation with custom start date
     * Stops cancelled flow and starts premium active flow again
     * Use this for web users who resubscribed earlier
     */
    public void onPremiumResubscribed(Date startDate) {
        Log.d(TAG, "Premium resubscribed - starting premium active flow with custom start date: " + startDate);
        notificationService.startPremiumActiveFlow(startDate);
    }

    /**
     * Get current notification flow type
     */
    public ReminderType getCurrentFlow() {
        return notificationService.getCurrentNotificationFlow();
    }

    /**
     * Cancel all notifications (useful for testing or user opt-out)
     */
    public void cancelAllNotifications() {
        Log.d(TAG, "Cancelling all notifications");
        notificationService.cancelCurrentFlow();
    }

    /**
     * Check if user is currently in free usage flow
     */
    public boolean isInFreeFlow() {
        return getCurrentFlow() == ReminderType.FREE_USAGE;
    }

    /**
     * Check if user is currently in premium active flow
     */
    public boolean isInPremiumActiveFlow() {
        return getCurrentFlow() == ReminderType.PREMIUM_ACTIVE;
    }

    /**
     * Check if user is currently in premium cancelled flow
     */
    public boolean isInPremiumCancelledFlow() {
        return getCurrentFlow() == ReminderType.PREMIUM_CANCELED;
    }
}
