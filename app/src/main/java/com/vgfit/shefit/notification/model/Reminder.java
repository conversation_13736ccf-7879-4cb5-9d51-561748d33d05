package com.vgfit.shefit.notification.model;

import android.content.Context;

import com.google.gson.annotations.SerializedName;

import java.util.Calendar;
import java.util.Date;

public class Reminder {

    @SerializedName("id")
    private int id;

    @SerializedName("type")
    private ReminderType type;

    @SerializedName("dayOffset")
    private int dayOffset;

    @SerializedName("intervalSec")
    private Integer intervalSec; // Optional

    @SerializedName("time")
    private TimeModel time; // Optional

    @SerializedName("title")
    private String title;

    @SerializedName("body")
    private String body;

    @SerializedName("repeats")
    private Boolean repeats = false;

    // Getters & Setters

    public int getId() { return id; }

    public void setId(int id) { this.id = id; }

    public ReminderType getType() { return type; }

    public void setType(ReminderType type) { this.type = type; }

    public int getDayOffset() { return dayOffset; }

    public void setDayOffset(int dayOffset) { this.dayOffset = dayOffset; }

    public Integer getIntervalSec() { return intervalSec; }

    public void setIntervalSec(Integer intervalSec) { this.intervalSec = intervalSec; }

    public TimeModel getTime() { return time; }

    public void setTime(TimeModel time) { this.time = time; }

    public String getTitle() { return title; }

    public void setTitle(String title) { this.title = title; }

    public String getBody() { return body; }

    public void setBody(String body) { this.body = body; }

    public Boolean getRepeats() { return repeats != null && repeats; }

    public void setRepeats(Boolean repeats) { this.repeats = repeats; }

    public boolean canBeSent(Context context) {
        return type != null && type.isEnabled(context);
    }

    public String getSound() {
        return "default";
    }

    public Date getNotificationDate(Date startDate) {
        if (startDate == null) return null;

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        calendar.add(Calendar.DAY_OF_YEAR, dayOffset);

        Date baseDate = calendar.getTime();

        if (intervalSec != null) {
            return new Date(baseDate.getTime() + (intervalSec * 1000L));
        }

        if (time != null) {
            calendar.setTime(baseDate);
            calendar.set(Calendar.HOUR_OF_DAY, time.getHour());
            calendar.set(Calendar.MINUTE, time.getMin());
            calendar.set(Calendar.SECOND, time.getSec());
            calendar.set(Calendar.MILLISECOND, 0);
            return calendar.getTime();
        }

        return null;
    }
}