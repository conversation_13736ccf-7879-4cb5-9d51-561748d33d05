package com.vgfit.shefit.notification.model;

import com.google.gson.annotations.SerializedName;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class TimeModel {

    @SerializedName("hour")
    private int hour;

    @SerializedName("min")
    private int min = 0;

    @SerializedName("sec")
    private int sec = 0;

    // Constructor
    public TimeModel(int hour, int min, int sec) {
        this.hour = hour;
        this.min = min;
        this.sec = sec;
    }

    // Getters & Setters
    public int getHour() { return hour; }
    public void setHour(int hour) { this.hour = hour; }

    public int getMin() { return min; }
    public void setMin(int min) { this.min = min; }

    public int getSec() { return sec; }
    public void setSec(int sec) { this.sec = sec; }

    // Equality check
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof TimeModel)) return false;
        TimeModel other = (TimeModel) obj;
        return hour == other.hour && min == other.min && sec == other.sec;
    }

    // Optional: formatted time like `humanReadableTime`
    public String getFormattedTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, min);
        calendar.set(Calendar.SECOND, sec);
        Date date = calendar.getTime();

        SimpleDateFormat formatter = new SimpleDateFormat("h:mm a", Locale.getDefault());
        return formatter.format(date);
    }

    // Static utility: from Date to TimeModel
    public static TimeModel fromDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return new TimeModel(
                calendar.get(Calendar.HOUR_OF_DAY),
                calendar.get(Calendar.MINUTE),
                calendar.get(Calendar.SECOND)
        );
    }

    // Static utility: convert TimeModel to Date (with today's date)
    public Date toDateToday() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, min);
        calendar.set(Calendar.SECOND, sec);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
}