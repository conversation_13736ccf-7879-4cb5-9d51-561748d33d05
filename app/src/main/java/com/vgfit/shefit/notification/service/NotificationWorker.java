package com.vgfit.shefit.notification.service;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import com.vgfit.shefit.R;
import com.vgfit.shefit.util.Translate;

public class NotificationWorker extends Worker {
    private static final String TAG = "NotificationWorker";
    private static final String CHANNEL_ID = "shapy_notifications";

    public static final String KEY_TITLE = "title_key";
    public static final String KEY_BODY = "body_key";
    public static final String KEY_IDENTIFIER = "identifier";

    public NotificationWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
    }

    @NonNull
    @Override
    public Result doWork() {
        try {
            // Get input data
            String titleKey = getInputData().getString(KEY_TITLE);
            String bodyKey = getInputData().getString(KEY_BODY);
            String identifier = getInputData().getString(KEY_IDENTIFIER);

            if (titleKey == null || bodyKey == null || identifier == null) {
                Log.e(TAG, "Missing required notification data");
                return Result.failure();
            }

            // Create notification channel if needed
            createNotificationChannel();

            // Translate the title and body using the translation keys
            String translatedTitle = Translate.getValue(titleKey);
            String translatedBody = Translate.getValue(bodyKey);

            // Show notification
            showNotification(translatedTitle, translatedBody, identifier);

            Log.d(TAG, "Notification shown successfully: " + identifier);
            return Result.success();

        } catch (Exception e) {
            Log.e(TAG, "Failed to show notification: " + e.getMessage(), e);
            return Result.failure();
        }
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            android.app.NotificationChannel channel = new android.app.NotificationChannel(
                    CHANNEL_ID,
                    "Shapy Notifications",
                    android.app.NotificationManager.IMPORTANCE_DEFAULT
            );
            channel.setDescription("Local notifications for Shapy app");

            android.app.NotificationManager manager = getApplicationContext().getSystemService(android.app.NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }

    private void showNotification(String title, String body, String identifier) {
        // Check if notification permission is granted
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(getApplicationContext(),
                    android.Manifest.permission.POST_NOTIFICATIONS) != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "Notification permission not granted, skipping notification: " + identifier);
                return;
            }
        }

        NotificationManagerCompat notificationManager = NotificationManagerCompat.from(getApplicationContext());

        // Check if notifications are enabled
        if (!notificationManager.areNotificationsEnabled()) {
            Log.d(TAG, "Notifications are disabled by user, skipping notification: " + identifier);
            return;
        }

        NotificationCompat.Builder builder = new NotificationCompat.Builder(getApplicationContext(), CHANNEL_ID)
                .setSmallIcon(R.mipmap.ic_launcher_nv)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setAutoCancel(true);

        int notificationId = identifier.hashCode();
        notificationManager.notify(notificationId, builder.build());

        Log.d(TAG, "Showed notification: " + identifier);
    }
}
