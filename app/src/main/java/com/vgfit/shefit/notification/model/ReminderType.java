package com.vgfit.shefit.notification.model;

import android.content.Context;

import androidx.annotation.NonNull;

import com.vgfit.shefit.authorization.PrefsUtilsWtContext;

public enum ReminderType {
    RETURN_TO_APP("return.to.app"),
    FREE_USAGE("free_usage"),
    PREMIUM_ACTIVE("premium_active"),
    PREMIUM_CANCELED("premium_canceled");

    private final String rawValue;

    ReminderType(String rawValue) {
        this.rawValue = rawValue;
    }

    public String getRawValue() {
        return rawValue;
    }

    private String getStateKey() {
        return rawValue + "_state";
    }

    public boolean isEnabled(Context context) {
        PrefsUtilsWtContext prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        boolean defaultValue = this != RETURN_TO_APP;
        return prefsUtilsWtContext.getBooleanPreferenceProfile(getStateKey(), defaultValue);
    }

    public void setEnabled(Context context, boolean value) {
        PrefsUtilsWtContext prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        prefsUtilsWtContext.setBooleanPreferenceProfile(getStateKey(), value);
    }

    @NonNull
    @Override
    public String toString() {
        return rawValue;
    }

    public static ReminderType fromRawValue(String rawValue) {
        for (ReminderType type : ReminderType.values()) {
            if (type.rawValue.equals(rawValue)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown raw value: " + rawValue);
    }
}