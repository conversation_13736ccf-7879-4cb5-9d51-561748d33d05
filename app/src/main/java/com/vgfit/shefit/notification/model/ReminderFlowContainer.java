package com.vgfit.shefit.notification.model;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class ReminderFlowContainer {

    @NonNull
    @SerializedName("id")
    private String id;

    @NonNull
    @SerializedName("flowType")
    private String flowType;

    @NonNull
    @SerializedName("notificationTriggers")
    private List<NotificationTrigger> notificationTriggers;

    public ReminderFlowContainer(@NonNull String id,
                                 @NonNull String flowType,
                                 @NonNull List<NotificationTrigger> notificationTriggers) {
        this.id = id;
        this.flowType = flowType;
        this.notificationTriggers = notificationTriggers;
    }

    @NonNull
    public String getId() {
        return id;
    }

    public void setId(@NonNull String id) {
        this.id = id;
    }

    @NonNull
    public String getFlowType() {
        return flowType;
    }

    public void setFlowType(@NonNull String flowType) {
        this.flowType = flowType;
    }

    @NonNull
    public List<NotificationTrigger> getNotificationTriggers() {
        return notificationTriggers;
    }

    public void setNotificationTriggers(@NonNull List<NotificationTrigger> notificationTriggers) {
        this.notificationTriggers = notificationTriggers;
    }
}