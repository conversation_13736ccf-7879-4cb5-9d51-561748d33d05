package com.vgfit.shefit.notification.model;

import java.util.Collections;
import java.util.Date;
import java.util.List;

public class LocalNotification {
    private String title;
    private String body;
    private List<NotificationTrigger> triggers;
    private String sound = "default"; // Echivalent UNNotificationSound.default

    @Override
    public String toString() {
        return "📮 LocalNotification:\n" +
                "• Title: " + title + "\n" +
                "• Body: " + body;
    }

    public LocalNotification(String title, String body, List<NotificationTrigger> triggers, String sound) {
        this.title = title;
        this.body = body;
        this.triggers = triggers;
        this.sound = sound;
    }

    public LocalNotification(Reminder reminder, Date startDate) {
        this.title = reminder.getTitle();
        this.body = reminder.getBody();
        this.sound = "default";

        switch (reminder.getType()) {
            case RETURN_TO_APP:
                // Niciun trigger
                break;

            case FREE_USAGE:
            case PREMIUM_ACTIVE:
            case PREMIUM_CANCELED:
                String identifier = reminder.getType().name() + "_" + reminder.getId();

                if (startDate != null) {
                    Date date = reminder.getNotificationDate(startDate);
                    if (date != null) {
                        boolean repeats = reminder.getRepeats() != null && reminder.getRepeats();

                        NotificationTrigger trigger = new NotificationTrigger(identifier, date, repeats);
                        this.triggers = Collections.singletonList(trigger);
                    }
                }
                break;
        }
    }

    // Getteri și setteri (opțional)
    public String getTitle() { return title; }
    public String getBody() { return body; }
    public List<NotificationTrigger> getTriggers() { return triggers; }
    public String getSound() { return sound; }
}