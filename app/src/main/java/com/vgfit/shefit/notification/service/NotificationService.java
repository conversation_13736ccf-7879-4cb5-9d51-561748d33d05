package com.vgfit.shefit.notification.service;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.notification.model.LocalNotification;
import com.vgfit.shefit.notification.model.PremiumStatusNotification;
import com.vgfit.shefit.notification.model.Reminder;
import com.vgfit.shefit.notification.model.ReminderType;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NotificationService {
    private static final String TAG = "NotificationService";
    private static final String PREFS_NAME = "notification_prefs";
    private static final String KEY_FREE_START_DATE = "free_start_date";
    private static final String KEY_PREMIUM_START_DATE = "premium_start_date";
    private static final String KEY_PREMIUM_CANCEL_DATE = "premium_cancel_date";
    private static final String KEY_CURRENT_FLOW = "current_flow";
    private static final String KEY_IS_PREMIUM = "is_premium";
    private static final String PATH_FILE = "notificationJson/";

    private static final String KEY_STARTED_NOTIFICATION = "key_started_notification";

    private final Context context;
    private final NotificationManager notificationManager;
    private final SharedPreferences prefs;
    private final Gson gson;
    private final PrefsUtilsWtContext prefsUtilsWtContext;

    public NotificationService(Context context) {
        this.context = context.getApplicationContext();
        this.notificationManager = new NotificationManager(context);
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.gson = new Gson();
        this.prefsUtilsWtContext = new PrefsUtilsWtContext(context);
    }

    // Main methods for starting notification flows
    public void startFreeUsageFlow() {
        boolean isStartedNotification = prefsUtilsWtContext.getBooleanPreferenceProfile(KEY_STARTED_NOTIFICATION, false);
        if (!isStartedNotification) {
            Log.d(TAG, "Starting Free Usage notification flow");
            prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_STARTED_NOTIFICATION, true);
            // Cancel any existing flows
            cancelAllFlows();

            // Set current flow and start date
            setCurrentFlow(ReminderType.FREE_USAGE);
            setFreeStartDate(new Date());

            // Schedule notifications
            scheduleNotificationsForType(ReminderType.FREE_USAGE);
        }
    }

    public void enableFreeUsageFlow() {
        prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_STARTED_NOTIFICATION, false);
        startFreeUsageFlow();
    }

    private void setUserPremium(boolean isPremium) {
        int newPremiumStatus = isPremium
                ? PremiumStatusNotification.ACTIVE.getValue()
                : PremiumStatusNotification.FALSE.getValue();

        int currentStatus = prefsUtilsWtContext.getIntegerPreferenceProfile(
                KEY_IS_PREMIUM,
                PremiumStatusNotification.NOT_SET.getValue()
        );

        if (currentStatus != newPremiumStatus) {
            prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_IS_PREMIUM, newPremiumStatus);
            Log.d(TAG, "Updated premium status to: " + newPremiumStatus);
        } else {
            Log.d(TAG, "Premium status already set: " + currentStatus);
        }
    }

    public void logOutCheckNotification(boolean lastPremiumStatus, boolean currentPremiumStatus) {
        if (lastPremiumStatus && !currentPremiumStatus) {
            setUserPremium(false);
            enableFreeUsageFlow();
        }
    }

    public void checkStatusUserNotification(boolean isPremium, Date date) {
        int newStatus = isPremium ? PremiumStatusNotification.ACTIVE.getValue() : PremiumStatusNotification.FALSE.getValue();
        int currentStatus = prefsUtilsWtContext.getIntegerPreferenceProfile(
                KEY_IS_PREMIUM,
                PremiumStatusNotification.NOT_SET.getValue()
        );

        // If it was not previously set
        if (currentStatus == PremiumStatusNotification.NOT_SET.getValue()) {
            setUserPremium(isPremium);
            if (isPremium) {
                startPremiumActiveFlow(date);
                Log.d(TAG, "Initial install: user is premium – starting active flow");
            } else {
                Log.d(TAG, "Initial install: user is not premium – no flow started");
            }
            return;
        }

        // If the status has changed
        if (currentStatus != newStatus) {
            setUserPremium(isPremium);
            if (isPremium) {
                startPremiumActiveFlow(date);
                Log.d(TAG, "User upgraded to premium – starting active flow");
            } else {
                startPremiumCancelledFlow();
                Log.d(TAG, "User downgraded from premium – starting cancelled flow");
            }
            return;
        }

        // Status is already synced, just make sure it is saved
        setUserPremium(isPremium);
        Log.d(TAG, "Premium status unchanged: " + newStatus);
    }

    public void startPremiumActiveFlow(Date date) {
        Log.d(TAG, "Starting Premium Active notification flow");

        // Cancel existing flows
        cancelAllFlows();

        // Set current flow and start date
        setCurrentFlow(ReminderType.PREMIUM_ACTIVE);
        setPremiumStartDate(date);

        // Schedule notifications
        scheduleNotificationsForType(ReminderType.PREMIUM_ACTIVE);
    }

    public void startPremiumCancelledFlow() {
        Log.d(TAG, "Starting Premium Cancelled notification flow");

        // Cancel existing flows
        cancelAllFlows();

        // Set current flow and start date
        setCurrentFlow(ReminderType.PREMIUM_CANCELED);
        setPremiumCancelDate(new Date());

        // Schedule notifications
        scheduleNotificationsForType(ReminderType.PREMIUM_CANCELED);
    }

    private void scheduleNotificationsForType(ReminderType type) {
        try {
            List<Reminder> reminders = loadRemindersFromAssets(type);
            Date startDate = getStartDateForType(type);

            if (reminders == null || reminders.isEmpty()) {
                Log.w(TAG, "No reminders found for type: " + type);
                return;
            }

            if (startDate == null) {
                Log.e(TAG, "Start date is null for type: " + type + ", using current date");
                startDate = new Date();
            }

            Date currentDate = new Date();
            int scheduledCount = 0;
            int skippedCount = 0;

            for (Reminder reminder : reminders) {
                try {
                    if (reminder == null) {
                        Log.w(TAG, "Null reminder found, skipping");
                        continue;
                    }

                    if (reminder.canBeSent(context)) {
                        LocalNotification notification = new LocalNotification(reminder, startDate);

                        // Check if notification date is in the past
                        if (shouldSkipPastNotification(notification, currentDate, startDate)) {
                            skippedCount++;
                            Log.d(TAG, "Skipping past notification: " + reminder.getId() + " for type: " + type);
                            continue;
                        }

                        notificationManager.scheduleNotification(notification);
                        scheduledCount++;
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error processing reminder: " + (reminder != null ? reminder.getId() : "null"), e);
                    skippedCount++;
                }
            }

            Log.d(TAG, "Scheduled " + scheduledCount + " notifications, skipped " + skippedCount + " past notifications for type: " + type);

        } catch (Exception e) {
            Log.e(TAG, "Critical error in scheduleNotificationsForType for type: " + type, e);
        }
    }

    /**
     * Determines if a notification should be skipped because it's in the past
     * This is especially important for Premium users who register on web and enter app later
     */
    private boolean shouldSkipPastNotification(LocalNotification notification, Date currentDate, Date startDate) {
        try {
            // Null checks
            if (notification == null || currentDate == null || startDate == null) {
                Log.w(TAG, "Null parameters in shouldSkipPastNotification");
                return false;
            }

            // Get the notification triggers
            if (notification.getTriggers() == null || notification.getTriggers().isEmpty()) {
                return false;
            }

            // Check if start date is in the past (more than 1 day ago)
            long dayInMillis = 24 * 60 * 60 * 1000;
            boolean startDateIsInPast = (currentDate.getTime() - startDate.getTime()) > dayInMillis;

            if (!startDateIsInPast) {
                // Start date is recent, don't skip any notifications
                return false;
            }

            // Start date is in the past, check each trigger
            for (com.vgfit.shefit.notification.model.NotificationTrigger trigger : notification.getTriggers()) {
                if (trigger == null) {
                    Log.w(TAG, "Null trigger found, skipping trigger check");
                    continue;
                }

                Date triggerDate = trigger.getTriggerTime();
                if (triggerDate != null && triggerDate.before(currentDate)) {
                    // This notification would have been sent in the past
                    Log.d(TAG, "Skipping past notification with trigger date: " + triggerDate +
                               " (current: " + currentDate + ", start: " + startDate + ")");
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            Log.e(TAG, "Error checking if notification should be skipped: " + e.getMessage(), e);
            // If we can't determine, don't skip
            return false;
        }
    }

    private List<Reminder> loadRemindersFromAssets(ReminderType type) {
        String fileName = getJsonFileNameForType(type);
        InputStream inputStream = null;
        try {
            inputStream = context.getAssets().open(PATH_FILE + fileName);
            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);

            String json = new String(buffer, StandardCharsets.UTF_8);
            Type listType = new TypeToken<List<Reminder>>() {
            }.getType();

            List<Reminder> reminders = gson.fromJson(json, listType);
            if (reminders == null) {
                Log.w(TAG, "Parsed reminders list is null for file: " + fileName);
                return new ArrayList<>(); // Return empty list instead of null
            }

            return reminders;

        } catch (IOException e) {
            Log.e(TAG, "Failed to load reminders from assets: " + fileName, e);
            return new ArrayList<>(); // Return empty list instead of null
        } catch (Exception e) {
            Log.e(TAG, "Unexpected error loading reminders: " + fileName, e);
            return new ArrayList<>(); // Return empty list instead of null
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    Log.w(TAG, "Failed to close input stream", e);
                }
            }
        }
    }

    private String getJsonFileNameForType(ReminderType type) {
        switch (type) {
            case FREE_USAGE:
                return "free_usage.json";
            case PREMIUM_ACTIVE:
                return "premium_active.json";
            case PREMIUM_CANCELED:
                return "premium_canceled.json";
            default:
                return null;
        }
    }

    private Date getStartDateForType(ReminderType type) {
        switch (type) {
            case FREE_USAGE:
                return getFreeStartDate();
            case PREMIUM_ACTIVE:
                return getPremiumStartDate();
            case PREMIUM_CANCELED:
                return getPremiumCancelDate();
            default:
                return null;
        }
    }

    private void cancelAllFlows() {
        // Cancel all notification types
        notificationManager.cancelAllNotifications("FREE_USAGE");
        notificationManager.cancelAllNotifications("PREMIUM_ACTIVE");
        notificationManager.cancelAllNotifications("PREMIUM_CANCELED");
    }

    // SharedPreferences helpers
    private void setCurrentFlow(ReminderType type) {
        prefs.edit().putString(KEY_CURRENT_FLOW, type.getRawValue()).apply();
    }

    private ReminderType getCurrentFlow() {
        String flowValue = prefs.getString(KEY_CURRENT_FLOW, null);
        if (flowValue != null) {
            try {
                return ReminderType.fromRawValue(flowValue);
            } catch (IllegalArgumentException e) {
                return null;
            }
        }
        return null;
    }

    private void setFreeStartDate(Date date) {
        prefs.edit().putLong(KEY_FREE_START_DATE, date.getTime()).apply();
    }

    private Date getFreeStartDate() {
        long timestamp = prefs.getLong(KEY_FREE_START_DATE, 0);
        return timestamp > 0 ? new Date(timestamp) : null;
    }

    private void setPremiumStartDate(Date date) {
        prefs.edit().putLong(KEY_PREMIUM_START_DATE, date.getTime()).apply();
    }

    private Date getPremiumStartDate() {
        long timestamp = prefs.getLong(KEY_PREMIUM_START_DATE, 0);
        return timestamp > 0 ? new Date(timestamp) : null;
    }

    private void setPremiumCancelDate(Date date) {
        prefs.edit().putLong(KEY_PREMIUM_CANCEL_DATE, date.getTime()).apply();
    }

    private Date getPremiumCancelDate() {
        long timestamp = prefs.getLong(KEY_PREMIUM_CANCEL_DATE, 0);
        return timestamp > 0 ? new Date(timestamp) : null;
    }

    // Public methods to check current state
    public ReminderType getCurrentNotificationFlow() {
        return getCurrentFlow();
    }

    public void cancelCurrentFlow() {
        ReminderType currentFlow = getCurrentFlow();
        if (currentFlow != null) {
            notificationManager.cancelAllNotifications(currentFlow.name());
            prefs.edit().remove(KEY_CURRENT_FLOW).apply();
            Log.d(TAG, "Cancelled current flow: " + currentFlow);
        }
    }
}
