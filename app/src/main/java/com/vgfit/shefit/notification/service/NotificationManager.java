package com.vgfit.shefit.notification.service;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;
import androidx.work.Data;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;

import com.vgfit.shefit.R;
import com.vgfit.shefit.notification.model.LocalNotification;
import com.vgfit.shefit.notification.model.NotificationTrigger;
import com.vgfit.shefit.util.Translate;

import java.util.Date;
import java.util.concurrent.TimeUnit;

public class NotificationManager {
    private static final String TAG = "NotificationManager";
    private static final String CHANNEL_ID = "shapy_notifications";
    private static final String TAG_UNKNOWN = "UNKNOWN";

    private final Context context;
    private final WorkManager workManager;
    private final NotificationManagerCompat notificationManagerUser;

    public NotificationManager(Context context) {
        this.context = context.getApplicationContext();
        this.workManager = WorkManager.getInstance(context);
        this.notificationManagerUser = NotificationManagerCompat.from(context);
        createNotificationChannel();
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            android.app.NotificationChannel channel = new android.app.NotificationChannel(
                    CHANNEL_ID,
                    "Shapy Notifications",
                    android.app.NotificationManager.IMPORTANCE_DEFAULT
            );
            channel.setDescription("Local notifications for Shapy app");

            android.app.NotificationManager manager = context.getSystemService(android.app.NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }

    public void scheduleNotification(LocalNotification notification) {
        try {
            if (notification == null) {
                Log.w(TAG, "Cannot schedule null notification");
                return;
            }

            if (notification.getTriggers() == null || notification.getTriggers().isEmpty()) {
                Log.w(TAG, "No triggers found for notification");
                return;
            }

            for (NotificationTrigger trigger : notification.getTriggers()) {
                if (trigger != null) {
                    scheduleNotificationTrigger(notification, trigger);
                } else {
                    Log.w(TAG, "Skipping null trigger");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling notification: " + e.getMessage(), e);
        }
    }

    private void scheduleNotificationTrigger(LocalNotification notification, NotificationTrigger trigger) {
        try {
            if (notification == null || trigger == null) {
                Log.w(TAG, "Cannot schedule notification with null notification or trigger");
                return;
            }

            Date triggerDate = trigger.getTriggerTime();
            if (triggerDate == null || triggerDate.before(new Date())) {
                Log.d(TAG, "Skipping notification - date is in the past: " + triggerDate);
                return;
            }

            // Calculate delay in milliseconds
            long currentTime = System.currentTimeMillis();
            long triggerTime = triggerDate.getTime();
            long delayInMillis = triggerTime - currentTime;

            // Validate delay
            if (delayInMillis < 0) {
                Log.w(TAG, "Negative delay calculated, skipping notification: " + delayInMillis);
                return;
            }

            String title = notification.getTitle();
            String body = notification.getBody();
            String identifier = trigger.getIdentifier();

            if (title == null || body == null || identifier == null) {
                Log.w(TAG, "Missing required notification data - title: " + title + ", body: " + body + ", identifier: " + identifier);
                return;
            }

            // Create input data for the worker
            Data inputData = new Data.Builder()
                    .putString(NotificationWorker.KEY_TITLE, title)
                    .putString(NotificationWorker.KEY_BODY, body)
                    .putString(NotificationWorker.KEY_IDENTIFIER, identifier)
                    .build();

            // Create work request with delay
            OneTimeWorkRequest workRequest = new OneTimeWorkRequest.Builder(NotificationWorker.class)
                    .setInputData(inputData)
                    .setInitialDelay(delayInMillis, TimeUnit.MILLISECONDS)
                    .addTag(identifier)
                    .addTag(getNotificationTypeTag(title))
                    .build();

            // Schedule the work
            workManager.enqueue(workRequest);

            Log.d(TAG, "Scheduled notification work: " + identifier + " for " + triggerDate);

        } catch (Exception e) {
            Log.e(TAG, "Error scheduling notification trigger: " + e.getMessage(), e);
        }
    }

    private String getNotificationTypeTag(String title) {
        try {
            if (title == null) {
                return TAG_UNKNOWN;
            }

            if (title.contains("free_usage")) {
                return "FREE_USAGE";
            } else if (title.contains("premium_active")) {
                return "PREMIUM_ACTIVE";
            } else if (title.contains("premium_canceled")) {
                return "PREMIUM_CANCELED";
            }
            return TAG_UNKNOWN;
        } catch (Exception e) {
            Log.w(TAG, "Error determining notification type tag: " + e.getMessage(), e);
            return TAG_UNKNOWN;
        }
    }

    public void cancelNotification(String identifier) {
        workManager.cancelAllWorkByTag(identifier);
        Log.d(TAG, "Cancelled notification work: " + identifier);
    }

    public void cancelAllNotifications(String typePrefix) {
        workManager.cancelAllWorkByTag(typePrefix);
        Log.d(TAG, "Cancelled all notifications with tag: " + typePrefix);
    }

    public void showNotification(String title, String body, String identifier) {
        // Check if notification permission is granted
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && ContextCompat.checkSelfPermission(context,
                android.Manifest.permission.POST_NOTIFICATIONS) != android.content.pm.PackageManager.PERMISSION_GRANTED) {
            Log.d(TAG, "Notification permission not granted, skipping notification: " + identifier);
            return;
        }

        // Check if notifications are enabled
        if (!notificationManagerUser.areNotificationsEnabled()) {
            Log.d(TAG, "Notifications are disabled by user, skipping notification: " + identifier);
            return;
        }

        // Translate the title and body if they are translation keys
        String translatedTitle = Translate.getValue(title);
        String translatedBody = Translate.getValue(body);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.mipmap.ic_launcher_nv)
                .setContentTitle(translatedTitle)
                .setContentText(translatedBody)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setAutoCancel(true);

        int notificationId = identifier.hashCode();
        notificationManagerUser.notify(notificationId, builder.build());

        Log.d(TAG, "Showed notification: " + identifier);
    }
}
