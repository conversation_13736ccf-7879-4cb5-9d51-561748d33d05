package com.vgfit.shefit;

import static com.vgfit.shefit.Utils.isDarkMode;
import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.LockTimeApp.saveSessionApp;
import static com.vgfit.shefit.util.eventCollect.CollectEvent.startAPP;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.Gravity;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TabHost;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;
import androidx.appcompat.app.AppCompatActivity;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.test.espresso.IdlingResource;

import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings;
import com.vgfit.shefit.api.loginweb.LoginWebService;
import com.vgfit.shefit.api.loginweb.callback.LoginStatusResponse;
import com.vgfit.shefit.api.loginweb.callback.UserStatusWebInited;
import com.vgfit.shefit.api.service.DownloadMealPhoto;
import com.vgfit.shefit.apprate.rateimplementation.RateInit;
import com.vgfit.shefit.apprate.rateimplementation.callback.FinishingRate;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.billing.BillingProcessor;
import com.vgfit.shefit.billing.callback.IBillingHandler;
import com.vgfit.shefit.fragment.composeui.HomeFragment;
import com.vgfit.shefit.fragment.more.service.SharedPreferencesData;
import com.vgfit.shefit.fragment.nutrition.NutritionFr;
import com.vgfit.shefit.fragment.personal_plan.PersonalPlanScrollFragment;
import com.vgfit.shefit.fragment.premium.callback.PremiumPurchase;
import com.vgfit.shefit.fragment.premium.callback.ResponseExtra;
import com.vgfit.shefit.fragment.premium.callback.Response_RestoreP;
import com.vgfit.shefit.fragment.premium.redesign.model.PurchasedProduct;
import com.vgfit.shefit.fragment.profile.ProfileFr;
import com.vgfit.shefit.fragment.workouts.widjetworkout.WorkoutFragmentBeautyRecycler;
import com.vgfit.shefit.notification.service.NotificationService;
import com.vgfit.shefit.util.AdvertiseIdUtil;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.FacebookEvent;
import com.vgfit.shefit.util.LockTimeApp;
import com.vgfit.shefit.util.RCUtils;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.dialog.DialogHelper;
import com.vgfit.shefit.util.idle.SimpleIdlingResource;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import top.xjunz.returntransitionpatcher.ReturnTransitionPatcher;

public class MainActivity extends AppCompatActivity implements TabHost.OnTabChangeListener, IBillingHandler, FinishingRate, ResponseExtra, Response_RestoreP {
    public static boolean readyToPurchase;
    private final String KEY_RUN_FIRST_TIME = "KEY_RUN_FIRST_TIME";
    public BillingProcessor bp;
    public PremiumPurchase premiumPurchase;
    String base64EncodedPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlezYvjKbN8L3CJD4u70Z+Iv3/Vq4a1R1hSwGy1mbZ2Mfxc86I7b2fRuhiZPIruXBOujE256yIgEnb24e6WIxg+i0pCAlb3CKh4UFgdCptbS2t9O3LsXwxrU+VEMKeNMGFSvYYUv9VOywii2umQ12vlU1n7NLdS9sL4aOMvyXcd3U1WbFdZcppKjVsDG/L/dnC1FLIx8HoQWrDY/jT4yDkLQ1idl7DAFZf9bi2MRfgLAUXPwRrEdEPvdouDASKUunZd2gJZPczLe2mrFRzxdm56/S1dhrSQMFMTX2+hNNU4SLPQUrjcFNFG8ufyVuoq5oskW2cHKxuQVKxyTUc2oGyQIDAQAB";
    TextView itemPlanTxt, itemWorkoutTxt, itemExerciseTxt, itemNutritionTxt, itemProfileTxt;
    private Map<String, TabInfo> mMapTaInfo;
    private TabInfo mLastTabInfo;
    private TabHost mTabHost;
    private DrawerLayout mDrawer;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private FirebaseRemoteConfig remoteConfig;
    private final ArrayList<LinearLayout> listTab = new ArrayList<>();
    private int selectedTab = 0;
    public String tabSelected = "tab0";
    @Nullable
    private SimpleIdlingResource mIdlingResource;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getApplicationContext());
        FacebookEvent.initFBSettings(this);
        setTheme();
        setContentView(R.layout.new_main);
        //is full screen
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
        Window w = getWindow();
        w.setFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS, WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);

        new Translate().initTranslate(getLanguageValid());
        new RateInit(this).appIsLaunched();
        bp = new BillingProcessor(this, base64EncodedPublicKey, this);
        try {
            bp.initBilling();
        } catch (Exception ignored) {
        }

        mDrawer = findViewById(R.id.drawer_layout);

        itemPlanTxt = findViewById(R.id.title1);
        itemWorkoutTxt = findViewById(R.id.title2);
        itemExerciseTxt = findViewById(R.id.title3);
        itemNutritionTxt = findViewById(R.id.title4);
        itemProfileTxt = findViewById(R.id.title5);

        LinearLayout itemPlan = findViewById(R.id.frag1);
        itemPlanTxt.setText(Translate.getValue("personal_plan"));
        listTab.add(itemPlan);

        LinearLayout itemWorkout = findViewById(R.id.frag2);
        itemWorkoutTxt.setText(Translate.getValue("workouts"));
        listTab.add(itemWorkout);

        LinearLayout itemExercise = findViewById(R.id.frag3);
        itemExerciseTxt.setText(Translate.getValue("exercises_plural"));
        listTab.add(itemExercise);

        LinearLayout itemNutrition = findViewById(R.id.frag4);
        itemNutritionTxt.setText(Translate.getValue("nutrition"));
        listTab.add(itemNutrition);

        LinearLayout itemProfile = findViewById(R.id.frag5);
        itemProfileTxt.setText(Translate.getValue("profile"));
        listTab.add(itemProfile);


        itemPlan.setOnClickListener(v -> {
            mDrawer.closeDrawers();
            selectedTab = 0;
        });
        itemWorkout.setOnClickListener(v -> {
            mDrawer.closeDrawers();
            selectedTab = 1;
        });
        itemExercise.setOnClickListener(v -> {
            mDrawer.closeDrawers();
            selectedTab = 2;
        });
        itemNutrition.setOnClickListener(v -> {
            mDrawer.closeDrawers();
            selectedTab = 3;
        });
        itemProfile.setOnClickListener(v -> {
            mDrawer.closeDrawers();
            selectedTab = 4;
        });
        LinearLayout leftMenu = findViewById(R.id.my_exit);
        leftMenu.setOnClickListener(v -> {
            selectButtonMenu(selectedTab);
            mDrawer.closeDrawers();
        });
        mDrawer.setScrimColor(getResources().getColor(R.color.shadow_left_menu));
        mDrawer.addDrawerListener(new DrawerLayout.DrawerListener() {
            @Override
            public void onDrawerSlide(@NonNull View drawerView, float slideOffset) {

            }

            @Override
            public void onDrawerOpened(@NonNull View drawerView) {
                selectButtonMenu(selectedTab);
            }

            @Override
            public void onDrawerClosed(@NonNull View drawerView) {
                onTabChanged("tab" + selectedTab);
                selectButtonMenu(selectedTab);
            }

            @Override
            public void onDrawerStateChanged(int newState) {

            }
        });
        initTabs();

        remoteConfig = FirebaseRemoteConfig.getInstance();
        getRemoteConfig();

        startAPP(prefsUtilsWtContext);
        ReturnTransitionPatcher.INSTANCE.patchActivity(this);
        lockLeftMenu();
        new DownloadMealPhoto();
        // TODO: 13.03.2025 uncomment this line to send GAID to Amplitude
        new AdvertiseIdUtil().setAdvertiseIdAmplitude(this);
        setConsentFirebase();
//        if (BuildConfig.DEBUG) {
//            Constant.setPremium(true);
//        }
    }

    private void setConsentFirebase() {
        FirebaseAnalytics mFirebaseAnalytics = FirebaseAnalytics.getInstance(this);
        Map<FirebaseAnalytics.ConsentType, FirebaseAnalytics.ConsentStatus> consentMap = new EnumMap<>(FirebaseAnalytics.ConsentType.class);
        consentMap.put(FirebaseAnalytics.ConsentType.ANALYTICS_STORAGE, FirebaseAnalytics.ConsentStatus.GRANTED);
        consentMap.put(FirebaseAnalytics.ConsentType.AD_STORAGE, FirebaseAnalytics.ConsentStatus.GRANTED);
        consentMap.put(FirebaseAnalytics.ConsentType.AD_USER_DATA, FirebaseAnalytics.ConsentStatus.GRANTED);
        consentMap.put(FirebaseAnalytics.ConsentType.AD_PERSONALIZATION, FirebaseAnalytics.ConsentStatus.GRANTED);
        mFirebaseAnalytics.setConsent(consentMap);
        mFirebaseAnalytics.setAnalyticsCollectionEnabled(true);
    }

    private void lockLeftMenu() {
        mDrawer.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
    }

    private void setTheme() {
        if (isDarkMode(prefsUtilsWtContext))
            getTheme().applyStyle(R.style.ThemeDark, true);
    }

    private void selectButtonMenu(int selectedTab) {
        int count = 0;
        for (LinearLayout item : listTab) {
            int finalCount = count;
            new Handler().post(() -> item.setPressed(finalCount == selectedTab));
            count++;
        }
    }

    private String getLanguageValid() {
        String lang = Locale.getDefault().getLanguage();
        String langUser = prefsUtilsWtContext.getStringPreferenceProfile("langDevice");
        if (langUser != null) {
            lang = langUser;
        }
        return lang;
    }

    private void getRemoteConfig() {

        FirebaseRemoteConfigSettings configSettings = new FirebaseRemoteConfigSettings.Builder()
                .setMinimumFetchIntervalInSeconds(0)
                .build();
        remoteConfig.setConfigSettingsAsync(configSettings);
        HashMap<String, Object> defaults = new HashMap<>();
        defaults.put("kMinimumFeaturesAndroid", 0);
        defaults.put("kFuelFeaturePAndroid", 999);
        defaults.put("OnboardingVariantAndroid", 0);
        defaults.put("OnboardingPaywallAndroid", 0);
        defaults.put("MainPaywallAndroid", 0);
        defaults.put("AppStartPaywallAndroid", 0);

        defaults.put("ScrollingPaywallAndroid", 0);
        defaults.put("ShowCloseButtonAfterAndroid", 3);
        remoteConfig.setDefaultsAsync(defaults);
        remoteConfig.fetchAndActivate()
                .addOnCompleteListener(this, task -> {
                    if (task.isSuccessful()) {

                        int onBoardingVariant = (int) remoteConfig.getLong("OnboardingVariantAndroid");
                        int onBoardingPaywallAndroid = (int) remoteConfig.getLong("OnboardingPaywallAndroid");
                        int mainPaywallAndroid = (int) remoteConfig.getLong("MainPaywallAndroid");
                        int appStartPaywallAndroid = (int) remoteConfig.getLong("AppStartPaywallAndroid");
                        int kMinimumFeaturesAndroid = (int) remoteConfig.getLong("kMinimumFeaturesAndroid");
                        int scrollingPaywall = (int) remoteConfig.getLong("ScrollingPaywallAndroid");
                        int showCloseButtonAfter = (int) remoteConfig.getLong("ShowCloseButtonAfterAndroid");

                        prefsUtilsWtContext.setIntegerPreferenceProfile(Constant.onBoardingVariant, onBoardingVariant);
                        RCUtils rcUtils = new RCUtils(this);
                        rcUtils.setOnBoardingVariantAndroid(onBoardingVariant);
                        rcUtils.setOnBoardingPaywallAndroid(onBoardingPaywallAndroid);
                        rcUtils.setMainPaywallAndroid(mainPaywallAndroid);
                        rcUtils.setAppStartPaywallAndroid(appStartPaywallAndroid);

                        rcUtils.setKMinimumFeaturesAndroid(kMinimumFeaturesAndroid);

                        rcUtils.setScrollingPaywall(scrollingPaywall);
                        rcUtils.setShowCloseButtonAfter(showCloseButtonAfter);

                    }
                });
    }


    @Override
    public void onStart() {
        super.onStart();
    }


    @Override
    protected void onResume() {
        super.onResume();
        saveSessionApp(this);
        if (readyToPurchase)
            openMainSubscribe();
    }

    private void openMainSubscribe() {
        LockTimeApp lockTimeApp = new LockTimeApp(getApplicationContext());
        RCUtils rcUtils = new RCUtils(this);
        if (!Constant.premium && (!lockTimeApp.isValidRunApp()) && !lockTimeApp.isFirstLunch()) {
            rcUtils.getAppStartPaywallAndroidOffer(this);
        }
    }

    public void goToPrevious() {
    }

    public void openDrawer() {
        mDrawer.openDrawer(Gravity.LEFT);
    }


    public void selectDrawerItem(MenuItem menuItem) {
        switch (menuItem.getItemId()) {
            case R.id.action_first:
                onTabChanged("tab1");
                break;
            case R.id.action_second:
                onTabChanged("tab2");
                break;
            case R.id.action_third:
                onTabChanged("tab3");
                break;
            case R.id.action_fourth:
                onTabChanged("tab4");
                break;
            default:
                onTabChanged("tab0");
        }

        // Highlight the selected item has been done by NavigationView
        menuItem.setChecked(true);
        // Close the navigation drawer
        mDrawer.closeDrawers();
    }

    private void sendAmplitudeEvents(String tabId) {

        String className = "";
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("withEventProperties", className);
            sendAmplitude("[View] Main view appeared Values: ", jsonObject);
        } catch (JSONException ignored) {
        }
    }

    private void initTabs() {
        mTabHost = findViewById(android.R.id.tabhost);
        mTabHost.setup();

        mMapTaInfo = new HashMap<>();
        addTab(new TabInfo("tab0", "TAB0", PersonalPlanScrollFragment.class.getName()));
        addTab(new TabInfo("tab1", "TAB1", WorkoutFragmentBeautyRecycler.class.getName()));
        addTab(new TabInfo("tab3", "TAB3", NutritionFr.class.getName()));
        addTab(new TabInfo("tab4", "TAB4", ProfileFr.class.getName()));


        mTabHost.setOnTabChangedListener(this);

        onTabChanged("tab0");
        selectButtonMenu(selectedTab);
    }

    @Override
    public void onTabChanged(String tabId) {

        TabInfo newTab = mMapTaInfo.get(tabId);
        if (newTab != null && mLastTabInfo != newTab) {
            FragmentTransaction fragmentTransaction = getSupportFragmentManager().beginTransaction();
            if (mLastTabInfo != null) fragmentTransaction.detach(mLastTabInfo.fragment);
            if (newTab.fragment == null) {
                newTab.fragment = createTabRootFragment(newTab);
                fragmentTransaction.add(R.id.realtabcontent, newTab.fragment);
            } else fragmentTransaction.attach(newTab.fragment);
            mLastTabInfo = newTab;
            sendAmplitudeEvents(tabSelected);
            fragmentTransaction.commit();
            tabSelected = tabId;
        }
    }

    private Fragment createTabRootFragment(TabInfo tabInfo) {
        Bundle args = new Bundle();
        args.putString("root", tabInfo.className);

        RootFragment fragment = new RootFragment();
        fragment.setArguments(args);

        return fragment;
    }

    @Override
    public void onBackPressed() {
        if (!getCurrentFragment().popBackStack()) {
            boolean isExistOfferActive = new RCUtils(this).existOfferActive();
            if (isExistOfferActive) {
                super.onBackPressed();
                return;
            }
            new DialogHelper(this).showDialogExit(isConfirmed -> {
                if (isConfirmed)
                    finish();
            });
        }
    }

    private RootFragment getCurrentFragment() {
        return (RootFragment) getSupportFragmentManager().findFragmentById(R.id.realtabcontent);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mTabHost = null;
        SharedPreferencesData sharedPreferencesData = new SharedPreferencesData(getApplicationContext());
        sharedPreferencesData.setCurrentDate();
        if (bp != null)
            bp.release();
        readyToPurchase = false;
    }

    @Override
    protected void onStop() {
        super.onStop();
//        EventBus.getDefault().unregister(this);
    }

    private void addTab(TabInfo tabInfo) {
        mMapTaInfo.put(tabInfo.tag, tabInfo);

        TabHost.TabSpec tabSpec = mTabHost
                .newTabSpec(tabInfo.tag)
                .setIndicator(tabInfo.label)
                .setContent(new DummyTabFactory(this));

        mTabHost.addTab(tabSpec);
    }

    @Override
    public void finishRate() {

    }


    @Override
    public void onProductPurchased(@NonNull String productId) {
        runOnUiThread(() -> {
            Log.e("IsPurchasedTest", "onProductPurchased Successful");
            sendAmplitude("[IAP] Purchase completed successfully");
            Constant.setPremium(true);
            if (premiumPurchase != null)
                premiumPurchase.isPurchased(true);
            new NotificationService(this).checkStatusUserNotification(Constant.premium, new Date());
        });
    }

    @Override
    public void onPurchaseHistoryRestored() {
        //not implemented
    }

    @Override
    public void onBillingError(int errorCode, Throwable error) {
        //not implemented
    }


    @Override
    public void onBillingInitialized() {
        readyToPurchase = true;
        if (bp != null) {
            verifyPremium();
        }
    }

    public void verifyPremium() {
        runOnUiThread(() -> {
            if (bp != null) {
                boolean isPremium = false;
                boolean haveActivePurchase = !bp.listOwnedSubscriptions().isEmpty() || !bp.listOwnedProducts().isEmpty();
                if (haveActivePurchase) {
                    Constant.setPremium(true);
                    isPremium = true;
                    EventBus.getDefault().post(new PurchasedProduct());
                    if (premiumPurchase != null)
                        premiumPurchase.isPurchased(true);
                } else {
                    LockTimeApp lockTimeApp = new LockTimeApp(this);
                    Constant.setPremium(lockTimeApp.isValidRunApp());
                }
//                if (BuildConfig.DEBUG) {
//                    Constant.setPremium(true);
//                }
                checkWebPremium(isPremium);
            }
        });

    }

    private void checkWebPremium(boolean isPremium) {
        if (prefsUtilsWtContext != null) {
            String email = prefsUtilsWtContext.getStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB);
            if (email != null && !email.isEmpty()) {
                Log.d("TestVerify", "Active");
                LoginWebService.getStatusUser(prefsUtilsWtContext, new LoginStatusResponse() {
                    @Override
                    public void responseStatusSuccess() {
                        EventBus.getDefault().post(new UserStatusWebInited());
                        openMainSubscribe();
                    }

                    @Override
                    public void statusPremium(Date date) {
                        new NotificationService(MainActivity.this).checkStatusUserNotification(Constant.premium, date);
                    }

                    @Override
                    public void responseStatusFailed() {
                       actionAfterCheck(isPremium);
                    }
                });
            } else {
               actionAfterCheck(isPremium);
            }
        }
    }
    private void actionAfterCheck(boolean isPremium){
        Date subscriptionStartDate = bp.getSubscriptionStartDate();
        new NotificationService(MainActivity.this).checkStatusUserNotification(isPremium, subscriptionStartDate != null ? subscriptionStartDate : new Date());
        openMainSubscribe();
    }

    @Override
    public void processFinish_upgrade_or_downgrade(Boolean output) {

    }

    @Override
    public void processFinishRestore(Boolean output) {
        if (premiumPurchase != null) {
            premiumPurchase.isPurchased(true);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }


    private static class DummyTabFactory implements TabHost.TabContentFactory {
        private final Context mContext;

        DummyTabFactory(Context context) {
            mContext = context;
        }

        @Override
        public View createTabContent(String tag) {
            return new View(mContext);
        }
    }

    private static class TabInfo {
        private final String tag;
        private final String label;
        private final String className;
        private Fragment fragment;

        TabInfo(String tag, String label, String className) {
            this.tag = tag;
            this.label = label;
            this.className = className;
        }
    }

    public void statusTest(boolean isStartTest) {
        if (mIdlingResource != null) {
            mIdlingResource.setIdleState(isStartTest);
        }
    }

    @VisibleForTesting
    @NonNull
    public IdlingResource getIdlingResource() {
        if (mIdlingResource == null) {
            mIdlingResource = new SimpleIdlingResource();
        }
        return mIdlingResource;
    }
}