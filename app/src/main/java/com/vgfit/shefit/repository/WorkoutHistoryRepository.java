package com.vgfit.shefit.repository;

import com.vgfit.shefit.realm.WorkoutHistory;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;
import io.realm.Sort;

public class WorkoutHistoryRepository {
    private static final String COMPLETED_DATE = "completedDate";
    private static final String POGRESS_PERCENTAGE = "progressPercentage";
    
    public List<WorkoutHistory> getWorkoutHistoryForCurrentWeek() {

        try (Realm realm = Realm.getDefaultInstance()) {
            // Get start and end of current week
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date startOfWeek = calendar.getTime();

            calendar.add(Calendar.DAY_OF_WEEK, 6);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            Date endOfWeek = calendar.getTime();

            // Query Realm for workout history within the week, excluding 0% progress
            RealmResults<WorkoutHistory> results = realm.where(WorkoutHistory.class)
                    .greaterThanOrEqualTo(COMPLETED_DATE, startOfWeek)
                    .lessThanOrEqualTo(COMPLETED_DATE, endOfWeek)
                    .greaterThan(POGRESS_PERCENTAGE, 0)
                    .sort(COMPLETED_DATE, Sort.DESCENDING)
                    .findAll();

            // Convert to list to avoid Realm managed objects issues
            return realm.copyFromRealm(results);
        }
    }
    
    public List<WorkoutHistory> getWorkoutHistoryForCurrentMonth() {

        try (Realm realm = Realm.getDefaultInstance()) {
            // Get start and end of current month
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date startOfMonth = calendar.getTime();

            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            Date endOfMonth = calendar.getTime();

            // Query Realm for workout history within the month, excluding 0% progress
            RealmResults<WorkoutHistory> results = realm.where(WorkoutHistory.class)
                    .greaterThanOrEqualTo(COMPLETED_DATE, startOfMonth)
                    .lessThanOrEqualTo(COMPLETED_DATE, endOfMonth)
                    .greaterThan(POGRESS_PERCENTAGE, 0)
                    .sort(COMPLETED_DATE, Sort.DESCENDING)
                    .findAll();

            // Convert to list to avoid Realm managed objects issues
            return realm.copyFromRealm(results);
        }
    }
    
    public List<WorkoutHistory> getWorkoutHistoryForCurrentYear() {

        try (Realm realm = Realm.getDefaultInstance()) {
            // Get start and end of current year
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_YEAR, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date startOfYear = calendar.getTime();

            calendar.add(Calendar.YEAR, 1);
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            Date endOfYear = calendar.getTime();

            // Query Realm for workout history within the year, excluding 0% progress
            RealmResults<WorkoutHistory> results = realm.where(WorkoutHistory.class)
                    .greaterThanOrEqualTo(COMPLETED_DATE, startOfYear)
                    .lessThanOrEqualTo(COMPLETED_DATE, endOfYear)
                    .greaterThan(POGRESS_PERCENTAGE, 0)
                    .sort(COMPLETED_DATE, Sort.DESCENDING)
                    .findAll();

            // Convert to list to avoid Realm managed objects issues
            return realm.copyFromRealm(results);
        }
    }

    public List<WorkoutHistory> getAllWorkoutHistory() {

        try (Realm realm = Realm.getDefaultInstance()) {
            // Query all workout history sorted by date (newest first), excluding 0% progress
            RealmResults<WorkoutHistory> results = realm.where(WorkoutHistory.class)
                    .greaterThan(POGRESS_PERCENTAGE, 0)
                    .sort(COMPLETED_DATE, Sort.DESCENDING)
                    .findAll();

            // Convert to list to avoid Realm managed objects issues
            return realm.copyFromRealm(results);
        }
    }

    /**
     * Calculates the total calories burned from all completed workouts
     * @return total calories burned
     */
    public int getTotalCaloriesBurned() {
        try (Realm realm = Realm.getDefaultInstance()) {
            RealmResults<WorkoutHistory> allWorkouts = realm.where(WorkoutHistory.class).findAll();

            int totalCalories = 0;
            for (WorkoutHistory workout : allWorkouts) {
                totalCalories += workout.getKcalBurned();
            }

            return totalCalories;
        }
    }

    /**
     * Calculates the total calories burned for workouts with progress > 0
     * (to exclude workouts that haven't been started)
     * @return total calories burned for completed workouts
     */
    public int getTotalCaloriesBurnedWithProgress() {
        try (Realm realm = Realm.getDefaultInstance()) {
            RealmResults<WorkoutHistory> completedWorkouts = realm.where(WorkoutHistory.class)
                    .greaterThan(POGRESS_PERCENTAGE, 0)
                    .findAll();

            int totalCalories = 0;
            for (WorkoutHistory workout : completedWorkouts) {
                totalCalories += workout.getKcalBurned();
            }

            return totalCalories;
        }
    }

    /**
     * Calculates the total calories burned for 100% completed workouts
     * @return total calories burned for fully completed workouts
     */
    public int getTotalCaloriesBurnedCompleted() {
        try (Realm realm = Realm.getDefaultInstance()) {
            RealmResults<WorkoutHistory> fullyCompletedWorkouts = realm.where(WorkoutHistory.class)
                    .equalTo(POGRESS_PERCENTAGE, 100)
                    .findAll();

            int totalCalories = 0;
            for (WorkoutHistory workout : fullyCompletedWorkouts) {
                totalCalories += workout.getKcalBurned();
            }

            return totalCalories;
        }
    }
}