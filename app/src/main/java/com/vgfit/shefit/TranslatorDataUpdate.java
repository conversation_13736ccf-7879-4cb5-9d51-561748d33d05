package com.vgfit.shefit;

import static com.vgfit.shefit.util.Translate.listLocalization;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.vgfit.shefit.api.callback.SuccessOneTranslate;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.authorization.SecDevice;
import com.vgfit.shefit.authorization.SuccessToken;
import com.vgfit.shefit.realm.Localizations;
import com.vgfit.shefit.realm.LocalizationsFreeRealm;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.Translate;

import java.util.ArrayList;
import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class TranslatorDataUpdate {
    private final Context context;
    private final PrefsUtilsWtContext prefsUtilsWtContext;
    private final String langValid;

    public TranslatorDataUpdate(Context context) {
        this.context = context;
        prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        if (listLocalization == null)
            new Translate();

        langValid = getLanguageValid();
    }

    private String getLanguageValid() {
        String lang = Locale.getDefault().getLanguage();
        String langUser = prefsUtilsWtContext.getStringPreferenceProfile("langDevice");
        if (langUser != null) {
            lang = langUser;
            Log.e("LangUser", "langUser==>" + lang);
        }
        lang = getValidLang(lang);
        Log.e("LangUser", "lang simple==>" + lang);
        return lang;
    }

    private String getValidLang(String lang) {
        String langResult = "en";
        ArrayList<String> listLang = new ArrayList<>();
        listLang.add("ru");
        listLang.add("zh-Hans");
        listLang.add("zh-Hant");
        listLang.add("ja");
        listLang.add("sv");
        listLang.add("tr");
        listLang.add("fr");
        listLang.add("es");
        listLang.add("nl");
        listLang.add("de");
        listLang.add("ko");
        listLang.add("it");
        listLang.add("pt");
        listLang.add("ar");
        listLang.add("da");
        listLang.add("fi");
        listLang.add("ms");
        listLang.add("no");

        for (String langItem : listLang) {
            if (lang.contains(langItem)) return langItem;
        }
        return langResult;
    }

    public void getTranslateForOne(String key, SuccessOneTranslate successOneTranslate) {
        String language = langValid;
        String token = new PrefsUtilsWtContext(context).getStringPreference(Constant.ACCESS_TOKEN);
        BaseApplication.getApiFemale().getLocalizationServer(token, language, key).enqueue(new Callback<Localizations>() {
            @Override
            public void onResponse(@NonNull Call<Localizations> call, @NonNull Response<Localizations> response) {
                if (response.code() == 200) {
                    if (response.body() != null) {
                        Localizations localizations = response.body();
                        localizations.setLanguageName(language);

                        LocalizationsFreeRealm localizationsFreeRealm = new LocalizationsFreeRealm();
                        localizationsFreeRealm.setKey(localizations.getKey());
                        localizationsFreeRealm.setLanguageName(localizations.getLanguageName());
                        localizationsFreeRealm.setValue(localizations.getValue());
                        String value = response.body().getValue();
                        try {
                            listLocalization.put(key, localizationsFreeRealm);
                        } catch (Exception ignored) {
                        }
                        if (response.body().getValue() != null)
                            successOneTranslate.translatedOneKey(value);
                        else
                            successOneTranslate.translatedOneKey("");
                    }
                } else if (response.code() == 401) {
                    SuccessToken successToken = token -> getTranslateForOne(key, successOneTranslate);
                    SecDevice.getRefreshToken(new PrefsUtilsWtContext(context), successToken);
                }
            }

            @Override
            public void onFailure(@NonNull Call<Localizations> call, @NonNull Throwable t) {
                Log.e("Error Update", "TranslatorDataUpdate=>" + t.getMessage());
                successOneTranslate.translatedOneKey("error");
            }
        });

    }
}
