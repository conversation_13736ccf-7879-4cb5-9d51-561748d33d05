package com.vgfit.shefit;

import static com.vgfit.shefit.authorization.SecDevice.getUUID;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.multidex.MultiDex;
import androidx.multidex.MultiDexApplication;

import com.facebook.stetho.Stetho;
import com.google.android.exoplayer2.database.ExoDatabaseProvider;
import com.google.android.exoplayer2.database.StandaloneDatabaseProvider;
import com.google.android.exoplayer2.offline.Download;
import com.google.android.exoplayer2.offline.DownloadManager;
import com.google.android.exoplayer2.scheduler.Requirements;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultDataSource;
import com.google.android.exoplayer2.upstream.DefaultHttpDataSource;
import com.google.android.exoplayer2.upstream.cache.Cache;
import com.google.android.exoplayer2.upstream.cache.CacheDataSink;
import com.google.android.exoplayer2.upstream.cache.CacheDataSource;
import com.google.android.exoplayer2.upstream.cache.LeastRecentlyUsedCacheEvictor;
import com.google.android.exoplayer2.upstream.cache.SimpleCache;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.nostra13.universalimageloader.cache.disc.naming.Md5FileNameGenerator;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.ImageLoaderConfiguration;
import com.nostra13.universalimageloader.core.assist.QueueProcessingType;
import com.onesignal.OneSignal;
import com.revenuecat.purchases.Purchases;
import com.revenuecat.purchases.PurchasesAreCompletedBy;
import com.revenuecat.purchases.PurchasesConfiguration;
import com.uphyca.stetho_realm.RealmInspectorModulesProvider;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.callbacks.ApiFemale;
import com.vgfit.shefit.callbacks.ApiMetaEvents;
import com.vgfit.shefit.callbacks.ApiWeather;
import com.vgfit.shefit.fragment.more.service.SharedPreferencesData;
import com.vgfit.shefit.fragment.workouts.streaming.Utils;
import com.vgfit.shefit.notification.service.NotificationHelper;
import com.vgfit.shefit.realm.Achievement;
import com.vgfit.shefit.util.AchievementHelper;

import java.io.File;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import io.realm.Realm;
import io.realm.RealmConfiguration;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import zlc.season.rxdownload3.core.DownloadConfig;

public class BaseApplication extends MultiDexApplication {
    private static final List<Fragment> activeFragments = new ArrayList<>();
    public static final String BASE_URL = "https://shapy-api.vgfit.com/api/";
//    public static final String BASE_URL = "https://shapy-test2.vgfit.com/api/";
    public static final String URL_META_EVENTS = "https://shapy-meta-events.vgfit.com/api/";
    private static final String ONESIGNAL_APP_ID = "************************************";
    private static ApiFemale apiFemale;
    private static ApiWeather apiWeather;
    private static ApiMetaEvents apiMetaEvents;
    Retrofit retrofit;
    Retrofit retrofitWeather;
    Retrofit retrofitMetaEvent;
    RealmConfiguration realmConfig;
    private String appUserId;
    private Cache simpleCash;
    public DownloadManager downloadManager;
    private StandaloneDatabaseProvider databaseProvider;

    public static ApiFemale getApiFemale() {
        return apiFemale;
    }

    public static ApiWeather getApiWeather() {
        return apiWeather;
    }

    public static ApiMetaEvents getApiMetaEvents() {
        return apiMetaEvents;
    }

    public static Cache getCashExoplayer(Context context) {
        BaseApplication application = (BaseApplication) context.getApplicationContext();
        return application.simpleCash == null ? (application.simpleCash = application.createCache()) : application.simpleCash;
    }

    private Cache createCache() {
        File downloadContentDirectory = new File(getFilesDir(), "downloads");
        long maxCacheSize = 1000L * 1024L * 1024L; // 100 MB
        return new SimpleCache(
                downloadContentDirectory,
                new LeastRecentlyUsedCacheEvictor(maxCacheSize),
                new ExoDatabaseProvider(this)
        );
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
    }

    public static void initImageLoader(Context context) {
        ImageLoaderConfiguration config = new ImageLoaderConfiguration.Builder(context)
                .threadPriority(Thread.NORM_PRIORITY)
                .denyCacheImageMultipleSizesInMemory()
                .diskCacheFileNameGenerator(new Md5FileNameGenerator())
                .diskCacheSize(400 * 1024 * 1024) // 50 Mb
                .tasksProcessingOrder(QueueProcessingType.LIFO)
                .writeDebugLogs() // Remove for release app
                .build();
        // Initialize ImageLoader with configuration.
        ImageLoader.getInstance().init(config);

    }

    @Override
    public void onCreate() {
        super.onCreate();

        FirebaseAnalytics mFirebaseAnalytics = FirebaseAnalytics.getInstance(this);
        Map<FirebaseAnalytics.ConsentType, FirebaseAnalytics.ConsentStatus> consentMap = new EnumMap<>(FirebaseAnalytics.ConsentType.class);
        consentMap.put(FirebaseAnalytics.ConsentType.ANALYTICS_STORAGE, FirebaseAnalytics.ConsentStatus.GRANTED);
        consentMap.put(FirebaseAnalytics.ConsentType.AD_STORAGE, FirebaseAnalytics.ConsentStatus.GRANTED);
        consentMap.put(FirebaseAnalytics.ConsentType.AD_USER_DATA, FirebaseAnalytics.ConsentStatus.GRANTED);
        consentMap.put(FirebaseAnalytics.ConsentType.AD_PERSONALIZATION, FirebaseAnalytics.ConsentStatus.GRANTED);
        mFirebaseAnalytics.setConsent(consentMap);
        mFirebaseAnalytics.setAnalyticsCollectionEnabled(true);

        SharedPreferencesData sharedPreferencesData = new SharedPreferencesData(this);
        appUserId = getUUID(new PrefsUtilsWtContext(this));
        // Configure Purchases SDK
        initializeRevenueCat();

        Realm.init(this);

        realmConfig = new RealmConfiguration.Builder()
                .name("shefitTest.realm")
                .modules(new BundledRealmModule())
                .schemaVersion(15)
                .migration(new Migration())
                .allowWritesOnUiThread(true)
                .build();

        if (!sharedPreferencesData.getUpdatedRealm()) {
            Log.e("realmVersion", "REALM WASN'T UPDATED");
            Realm.deleteRealm(realmConfig);
            sharedPreferencesData.saveRealmAsUpdated();
        }
        Realm.setDefaultConfiguration(realmConfig);

        // Initialize achievements after ensuring Realm is ready
        initializeAchievementsWhenReady();

        DownloadConfig.Builder builder = DownloadConfig.Builder.Companion.create(this)
                .setDefaultPath(String.valueOf(Utils.getVideoCacheDir(this)))
                .enableNotification(false)
                .setDebug(true);

        DownloadConfig.INSTANCE.init(builder);
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(2, TimeUnit.SECONDS)
                .readTimeout(1, TimeUnit.SECONDS)
                .writeTimeout(1, TimeUnit.SECONDS)
                .build();

        retrofit = new Retrofit.Builder()
                .baseUrl(BASE_URL)
//                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        apiFemale = retrofit.create(ApiFemale.class);

        retrofitWeather = new Retrofit.Builder()
                .baseUrl("https://api.weatherstack.com/")
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        apiWeather = retrofitWeather.create(ApiWeather.class);

        retrofitMetaEvent = new Retrofit.Builder()
                .baseUrl(URL_META_EVENTS)
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        apiMetaEvents = retrofitMetaEvent.create(ApiMetaEvents.class);

        Stetho.initialize(
                Stetho.newInitializerBuilder(this)
                        .enableDumpapp(Stetho.defaultDumperPluginsProvider(this))
                        .enableWebKitInspector(RealmInspectorModulesProvider.builder(this).withLimit(100000).build())
                        .build());
        RealmInspectorModulesProvider.builder(this)
                .withFolder(getCacheDir())
//                .withEncryptionKey("default.realm", null)
                .withMetaTables()
                .withDescendingOrder()
                .withLimit(100000)
                .databaseNamePattern(Pattern.compile(".+\\.realm"))
                .build();
        initImageLoader(getApplicationContext());

        // OneSignal Initialization
        initOneSignal();
        databaseProvider = new ExoDatabaseProvider(this);
        simpleCash = createCache();

        CacheDataSource.Factory cacheDataSourceFactory = createCacheDataSourceFactory(getApplicationContext());
        downloadManager = new DownloadManager(
                this,
                databaseProvider,
                simpleCash,
                cacheDataSourceFactory,
                Executors.newFixedThreadPool(2)
        );
        downloadManager.setRequirements(new Requirements(Requirements.NETWORK_UNMETERED));
        downloadManager.setMaxParallelDownloads(10);
        if (BuildConfig.DEBUG)
            downloadManager.addListener(new DownloadManager.Listener() {
                @Override
                public void onInitialized(DownloadManager downloadManager) {
                    Log.d("DownloadManager", "Initialized");
                }

                @Override
                public void onDownloadChanged(DownloadManager downloadManager, Download download, Exception finalException) {
                    Log.d("DownloadManager", "Download changed: " + download.request.id + " State: " + download.state);
                }

                @Override
                public void onDownloadRemoved(DownloadManager downloadManager, Download download) {
                    Log.d("DownloadManager", "Download removed: " + download.request.id);
                }
            });

        downloadManager.resumeDownloads();
        initAmplitude();
        initActivityLifecycle();
        initNotification();
    }

    private void initNotification() {
        NotificationHelper.getInstance(this).onAppFirstLaunch();
    }

    private void initOneSignal() {
        try {
            OneSignal.initWithContext(this, ONESIGNAL_APP_ID);
        } catch (Exception e) {
            Log.e("BaseApplication", "Error initializing OneSignal: " + e.getMessage());
        }
    }

    private void initializeRevenueCat() {
        try {
            Purchases.configure(
                    new PurchasesConfiguration.Builder(this, "LXfKbEIyrpRQXOnUMIcAiRiUDlxRcDvv")
                            .appUserID(appUserId)
                            .purchasesAreCompletedBy(PurchasesAreCompletedBy.MY_APP)
                            .build()
            );

            Purchases.getSharedInstance().collectDeviceIdentifiers();
        } catch (Exception e) {
            Log.e("BaseApplication", "Error initializing RevenueCat: " + e.getMessage());
        }
    }

    private void initActivityLifecycle() {
        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                if (activity instanceof FragmentActivity) {
                    ((FragmentActivity) activity).getSupportFragmentManager().registerFragmentLifecycleCallbacks(
                            new FragmentManager.FragmentLifecycleCallbacks() {
                                @Override
                                public void onFragmentAttached(@NonNull FragmentManager fm, @NonNull Fragment f, @NonNull Context context) {
                                    activeFragments.add(f);
                                }

                                @Override
                                public void onFragmentDetached(@NonNull FragmentManager fm, @NonNull Fragment f) {
                                    activeFragments.remove(f);
                                }
                            }, true);
                }
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {
                //not used
            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {
                //not used
            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {
                //not used
            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {
                //not used
            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
                //not used
            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {
                //not used
            }
        });
    }


    public static List<Fragment> getOpenFragments() {
        return new ArrayList<>(activeFragments);
    }

    public DownloadManager getDownloadManager() {
        return downloadManager;
    }

    private void initAmplitude() {
        // TODO: 13.03.2025 uncomment this line to send GAID to Amplitude
//        Amplitude.getInstance().initialize(this, "3d7b2599c91915dd144f96ae8b79301b", appUserId).enableForegroundTracking(this).trackSessionEvents(true).setEventUploadThreshold(1);
    }

    public CacheDataSource.Factory createCacheDataSourceFactory(Context context) {
        Cache cache = BaseApplication.getCashExoplayer(context);
        DataSource.Factory upstreamFactory = buildHttpDataSourceFactory(context);
        return new CacheDataSource.Factory()
                .setCache(cache)
                .setUpstreamDataSourceFactory(upstreamFactory)
                .setCacheWriteDataSinkFactory(new CacheDataSink.Factory().setCache(cache))
                .setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR);
    }

    public static DataSource.Factory buildHttpDataSourceFactory(Context context) {
        return new DefaultDataSource.Factory(context, new DefaultHttpDataSource.Factory());
    }

    private void initializeAchievementsWhenReady() {
        android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());

        Runnable checkAndInit = new Runnable() {
            private int attempts = 0;
            private final int maxAttempts = 10; // Maximum 10 attempts (5 seconds total)

            @Override
            public void run() {
                attempts++;

                try (Realm realm = Realm.getDefaultInstance()) {
                    // Try to access Achievement table to verify it's ready
                    realm.where(Achievement.class).count();
                    // If we get here, the table is ready
                    AchievementHelper.initializeAchievements();
                } catch (Exception e) {
                    // Table not ready yet, retry if we haven't exceeded max attempts
                    if (attempts < maxAttempts) {
                        handler.postDelayed(this, 500); // Wait 500ms before next attempt
                    }
                    // If max attempts reached, silently give up
                }
            }
        };

        // Start the first check immediately
        handler.post(checkAndInit);
    }

}