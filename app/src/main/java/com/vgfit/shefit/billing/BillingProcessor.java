package com.vgfit.shefit.billing;

import static com.android.billingclient.api.BillingClient.SkuType.INAPP;
import static com.android.billingclient.api.BillingClient.SkuType.SUBS;

import android.app.Activity;
import android.content.SharedPreferences;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.android.billingclient.api.AcknowledgePurchaseParams;
import com.android.billingclient.api.BillingClient;
import com.android.billingclient.api.BillingClientStateListener;
import com.android.billingclient.api.BillingFlowParams;
import com.android.billingclient.api.BillingResult;
import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.Purchase;
import com.android.billingclient.api.PurchasesUpdatedListener;
import com.android.billingclient.api.QueryProductDetailsParams;
import com.android.billingclient.api.QueryPurchasesParams;
import com.google.common.collect.ImmutableList;
import com.vgfit.shefit.billing.callback.IBillingHandler;
import com.vgfit.shefit.billing.callback.TaskSkuDetail;
import com.vgfit.shefit.billing.model.SkuDetail;
import com.vgfit.shefit.billing.service.Security;
import com.vgfit.shefit.util.meta_events.MetaEventsSender;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class BillingProcessor implements PurchasesUpdatedListener {
    private final Activity context;
    private final String licenseKey;
    private final IBillingHandler iBillingHandler;
    private BillingClient billingClient;
    public static final String PREF_FILE = "PREF_BILLING";

    private final HashMap<String, Purchase> cachedProducts;
    private final HashMap<String, Purchase> cachedSubscriptions;
    private int countInitBilling = 0;

    public BillingProcessor(Activity context, String licenseKey, IBillingHandler iBillingHandler) {
        this.context = context;
        this.licenseKey = licenseKey;
        this.iBillingHandler = iBillingHandler;
        cachedProducts = new HashMap<>();
        cachedSubscriptions = new HashMap<>();
    }

    public void initBilling() {
        billingClient = BillingClient.newBuilder(context).enablePendingPurchases().setListener(this).build();
        billingClient.startConnection(new BillingClientStateListener() {
            @Override
            public void onBillingSetupFinished(@NonNull BillingResult billingResult) {
                if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                    getListPurchases(true);
                }
            }

            @Override
            public void onBillingServiceDisconnected() {
            }
        });
    }

    private void getListPurchases(boolean isBillingSetup) {
        countInitBilling = 0;
        billingClient.queryPurchasesAsync(
                QueryPurchasesParams.newBuilder().setProductType(INAPP).build(),
                (billingResult, purchases) -> {
                    if (!purchases.isEmpty())
                        handlePurchases(purchases, INAPP);
                    if (isBillingSetup)
                        finishCheckSetup();
                }
        );
        billingClient.queryPurchasesAsync(
                QueryPurchasesParams.newBuilder().setProductType(SUBS).build(),
                (billingResult, purchases) -> {
                    if (!purchases.isEmpty())
                        handlePurchases(purchases, SUBS);
                    if (isBillingSetup)
                        finishCheckSetup();
                }
        );
    }

    private void finishCheckSetup() {
        countInitBilling++;
        if (countInitBilling >= 2) {
            iBillingHandler.onBillingInitialized();
        }
    }

    public void purchase(Activity activity, String productId) {
        ImmutableList<QueryProductDetailsParams.Product> productList = ImmutableList.of(QueryProductDetailsParams.Product.newBuilder()
                .setProductId(productId)
                .setProductType(INAPP)
                .build());

        QueryProductDetailsParams params = QueryProductDetailsParams.newBuilder()
                .setProductList(productList)
                .build();

        billingClient.queryProductDetailsAsync(
                params,
                (billingResult, productDetailsList) -> {
                    if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                        if (!productDetailsList.isEmpty()) {
                            int selectedOfferIndex = 0;
                            ProductDetails productDetails = productDetailsList.get(selectedOfferIndex);

                            String offerToken = null;
                            if (productDetails.getSubscriptionOfferDetails() != null) {
                                offerToken = productDetails.getSubscriptionOfferDetails().get(0).getOfferToken();
                            }

                            ImmutableList<BillingFlowParams.ProductDetailsParams> productDetailsParamsList =
                                    null;
                            if (offerToken != null) {
                                productDetailsParamsList = ImmutableList.of(
                                        BillingFlowParams.ProductDetailsParams.newBuilder()
                                                .setProductDetails(productDetails)
                                                .setOfferToken(offerToken)
                                                .build()
                                );
                            }


                            BillingFlowParams flowParams = null;
                            if (productDetailsParamsList != null) {
                                flowParams = BillingFlowParams.newBuilder()
                                        .setProductDetailsParamsList(productDetailsParamsList)
                                        .build();
                            }
                            if (flowParams != null) {
                                billingClient.launchBillingFlow(activity, flowParams);
                            }
                        } else {
                            //try to add item/product id "purchase" inside managed product in google play console
                            sendToUserError("Purchase Item not Found");
                        }
                    } else {
                        sendToUserError("Error Purchase" + billingResult.getDebugMessage());
                    }
                });
    }

    public void subscribe(Activity activity, String productId) {
        ImmutableList<QueryProductDetailsParams.Product> productList = ImmutableList.of(QueryProductDetailsParams.Product.newBuilder()
                .setProductId(productId)
                .setProductType(SUBS)
                .build());

        QueryProductDetailsParams params = QueryProductDetailsParams.newBuilder()
                .setProductList(productList)
                .build();

        billingClient.queryProductDetailsAsync(
                params,
                (billingResult, productDetailsList) -> {
                    if (billingResult.getResponseCode() != BillingClient.BillingResponseCode.OK) {
                        sendToUserError("Error Subscribe" + billingResult.getDebugMessage());
                    } else {
                        if (!productDetailsList.isEmpty()) {
                            int selectedOfferIndex = 0;
                            ProductDetails productDetails = productDetailsList.get(selectedOfferIndex);

                            String offerToken = null;
                            if (productDetails.getSubscriptionOfferDetails() != null) {
                                offerToken = productDetails.getSubscriptionOfferDetails().get(0).getOfferToken();
                            }

                            ImmutableList<BillingFlowParams.ProductDetailsParams> productDetailsParamsList =
                                    null;
                            if (offerToken != null) {
                                productDetailsParamsList = ImmutableList.of(
                                        BillingFlowParams.ProductDetailsParams.newBuilder()
                                                .setProductDetails(productDetails)
                                                .setOfferToken(offerToken)
                                                .build()
                                );
                            }


                            BillingFlowParams flowParams = null;
                            if (productDetailsParamsList != null) {
                                flowParams = BillingFlowParams.newBuilder()
                                        .setProductDetailsParamsList(productDetailsParamsList)
                                        .build();
                            }
                            if (flowParams != null) {
                                billingClient.launchBillingFlow(activity, flowParams);
                            }
                        } else {
                            //try to add item/product id "purchase" inside managed product in google play console
                            sendToUserError("Purchase Item not Found");
                        }
                        new MetaEventsSender(activity).sendInitiatedCheckoutInfo(this, productId);
                    }
                });
    }

    public boolean isPurchased(String productId) {
        String key = INAPP + productId;
        if (cachedProducts.get(productId) == null)
            savePurchaseValueToPref(key, false);
        return getPurchaseValueFromPref(key);
    }

    public boolean isSubscribed(String productId) {
        String key = SUBS + productId;
        if (cachedSubscriptions.get(productId) == null)
            savePurchaseValueToPref(key, false);
        return getPurchaseValueFromPref(key);
    }

    public List<String> listOwnedProducts() {
        return new ArrayList<>(cachedProducts.keySet());
    }

    public List<String> listOwnedSubscriptions() {
        return new ArrayList<>(cachedSubscriptions.keySet());
    }

    /**
     * Gets the subscription start date for the first active subscription
     * @return Date when the subscription was purchased, or null if no active subscription
     */
    public Date getSubscriptionStartDate() {
        try {
            if (cachedSubscriptions == null) {
                Log.w("BillingProcessor", "cachedSubscriptions is null");
                return null;
            }

            if (cachedSubscriptions.isEmpty()) {
                Log.d("BillingProcessor", "No cached subscriptions found");
                return null;
            }

            // Get the first subscription (assuming user has only one active subscription)
            Purchase subscription = cachedSubscriptions.values().iterator().next();
            if (subscription != null) {
                long purchaseTimeMillis = subscription.getPurchaseTime();
                if (purchaseTimeMillis > 0) {
                    Date subscriptionDate = new Date(purchaseTimeMillis);
                    Log.d("BillingProcessor", "Subscription start date: " + subscriptionDate);
                    return subscriptionDate;
                } else {
                    Log.w("BillingProcessor", "Invalid purchase time: " + purchaseTimeMillis);
                }
            } else {
                Log.w("BillingProcessor", "Subscription object is null");
            }
        } catch (Exception e) {
            Log.e("BillingProcessor", "Error getting subscription start date: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * Gets the subscription start date for a specific product ID
     * @param productId The subscription product ID
     * @return Date when the subscription was purchased, or null if not found
     */
    public Date getSubscriptionStartDate(String productId) {
        try {
            Purchase subscription = cachedSubscriptions.get(productId);
            if (subscription != null) {
                long purchaseTimeMillis = subscription.getPurchaseTime();
                Date subscriptionDate = new Date(purchaseTimeMillis);
                Log.d("BillingProcessor", "Subscription start date for " + productId + ": " + subscriptionDate);
                return subscriptionDate;
            } else {
                Log.d("BillingProcessor", "No subscription found for product ID: " + productId);
            }
        } catch (Exception e) {
            Log.e("BillingProcessor", "Error getting subscription start date for " + productId + ": " + e.getMessage(), e);
        }
        return null;
    }


    @Override
    public void onPurchasesUpdated(@NonNull BillingResult billingResult, @Nullable List<Purchase> purchases) {
        //if item newly purchased
        if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK && purchases != null) {
            getListPurchases(false);
        }
        //if item already purchased then check and reflect changes
        else if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED) {
            getListPurchases(false);
        }
        //if purchase cancelled
        else if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.USER_CANCELED) {
            sendToUserError("Purchase Canceled");
        }
        // Handle any other error msgs
        else {
            sendToUserError("Error Message" + billingResult.getDebugMessage());
        }
    }

    void handlePurchases(List<Purchase> purchases, String type) {
        for (Purchase purchase : purchases) {

            //if item is purchased
            if (purchase.getPurchaseState() == Purchase.PurchaseState.PURCHASED) {
                if (!verifyValidSignature(purchase.getOriginalJson(), purchase.getSignature())) {
                    // Invalid purchase
                    // show error to user
                    sendToUserError("Error : Invalid Purchase");
                    return;
                }
                // else purchase is valid
                //if item is purchased and not acknowledged
                if (!purchase.isAcknowledged()) {
                    AcknowledgePurchaseParams acknowledgePurchaseParams =
                            AcknowledgePurchaseParams.newBuilder()
                                    .setPurchaseToken(purchase.getPurchaseToken())
                                    .build();
                    billingClient.acknowledgePurchase(acknowledgePurchaseParams, billingResult -> {
                        if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                            //if purchase is acknowledged
                            // Grant entitlement to the user. and restart activity
                            insertCashPurchase(type, purchase);
                            savePurchaseValueToPref(type + purchase.getProducts().get(0), true);
                            iBillingHandler.onProductPurchased(purchase.getProducts().get(0));
                            if (context != null)
                                new MetaEventsSender(context).sendTrialInfo(this, purchase.getProducts().get(0), purchase.getOrderId());
                        }
                    });
                }

                //else item is purchased and also acknowledged
                else {
                    // Grant entitlement to the user on item purchase
                    // restart activity
                    if (!getPurchaseValueFromPref(type + purchase.getProducts().get(0))) {
                        savePurchaseValueToPref(type + purchase.getProducts().get(0), true);
                    }
                }
                if (purchase.isAcknowledged()) {
                    insertCashPurchase(type, purchase);
                }
            }
            //if purchase is pending
            else if (purchase.getPurchaseState() == Purchase.PurchaseState.PENDING) {
                sendToUserError("Purchase is Pending. Please complete Transaction");
            }
            //if purchase is unknown
            else if (purchase.getPurchaseState() == Purchase.PurchaseState.UNSPECIFIED_STATE) {
                savePurchaseValueToPref(type + purchase.getProducts().get(0), false);
                sendToUserError("Purchase Status Unknown");
            }
        }
    }

    private void insertCashPurchase(String type, Purchase purchase) {
        if (type.equals(INAPP)) {
            cachedProducts.put(purchase.getProducts().get(0), purchase);
        } else {
            cachedSubscriptions.put(purchase.getProducts().get(0), purchase);
        }
    }

    public void getPurchaseListingDetails(String productID, TaskSkuDetail taskSkuDetails) {
        setParamsSku(productID, INAPP, taskSkuDetails);
    }

    public void getSubscriptionListingDetails(String productId, TaskSkuDetail taskSkuDetails) {
        setParamsSku(productId, SUBS, taskSkuDetails);
    }

    private void setParamsSku(String productId, String type, TaskSkuDetail taskSkuDetails) {
        ImmutableList<QueryProductDetailsParams.Product> productList = ImmutableList.of(QueryProductDetailsParams.Product.newBuilder()
                .setProductId(productId)
                .setProductType(type)
                .build());

        QueryProductDetailsParams params = QueryProductDetailsParams.newBuilder()
                .setProductList(productList)
                .build();
        getSkuDetails(params, taskSkuDetails);
    }

    private void getSkuDetails(QueryProductDetailsParams params, TaskSkuDetail taskSkuDetails) {
        billingClient.queryProductDetailsAsync(
                params,
                (billingResult, productDetailsList) -> {
                    if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK && (!productDetailsList.isEmpty())) {
                        try {
                            ProductDetails productDetail = productDetailsList.get(0);
                            String priceText = "";
                            long priceLong = 0L;
                            String subscriptionFreeTrialPeriod = "";
                            String currency = "";
                            String productType;
                            String title;
                            String productId;
                            String description;

                            if (productDetail.getSubscriptionOfferDetails() != null) {
                                productType = productDetailsList.get(0).getProductType();
                                title = productDetailsList.get(0).getTitle();
                                productId = productDetailsList.get(0).getProductId();
                                description = productDetailsList.get(0).getDescription();
                                for (ProductDetails.PricingPhase pricePhases : productDetail.getSubscriptionOfferDetails().get(0).getPricingPhases().getPricingPhaseList()) {
                                    priceText = pricePhases.getFormattedPrice();
                                    priceLong = pricePhases.getPriceAmountMicros();
                                    if (priceLong == 0)
                                        subscriptionFreeTrialPeriod = pricePhases.getBillingPeriod();
                                    currency = pricePhases.getPriceCurrencyCode();
                                }

                                JSONObject jsonObject = new JSONObject();

                                jsonObject.put("price", priceText);
                                jsonObject.put("freeTrialPeriod", subscriptionFreeTrialPeriod);
                                jsonObject.put("price_currency_code", currency);
                                jsonObject.put("price_amount_micros", priceLong);
                                jsonObject.put("type", productType);
                                jsonObject.put("title", title);
                                jsonObject.put("productId", productId);
                                jsonObject.put("description", description);

                                taskSkuDetails.TaskSkuDetails(new SkuDetail(jsonObject));
                            } else if (productDetail.getOneTimePurchaseOfferDetails() != null) {
                                ProductDetails.OneTimePurchaseOfferDetails pricePhaseOffer = productDetail.getOneTimePurchaseOfferDetails();
                                priceText = pricePhaseOffer.getFormattedPrice();
                                priceLong = pricePhaseOffer.getPriceAmountMicros();
                                currency = pricePhaseOffer.getPriceCurrencyCode();
                                productType = productDetailsList.get(0).getProductType();
                                title = productDetailsList.get(0).getTitle();
                                productId = productDetailsList.get(0).getProductId();
                                description = productDetailsList.get(0).getDescription();
                                Log.d("TestPrice", "price error product detail ");
                                JSONObject jsonObject = new JSONObject();

                                jsonObject.put("price", priceText);
                                jsonObject.put("price_currency_code", currency);
                                jsonObject.put("price_amount_micros", priceLong);
                                jsonObject.put("type", productType);
                                jsonObject.put("title", title);
                                jsonObject.put("productId", productId);
                                jsonObject.put("description", description);

                                taskSkuDetails.TaskSkuDetails(new SkuDetail(jsonObject));
                            }

                        } catch (JSONException e) {
                            Log.d("TestPrice", "price error--> " + e.getMessage());
                        }

                    }
                }
        );
    }


    private SharedPreferences getPreferenceObject() {
        return context.getSharedPreferences(PREF_FILE, 0); // NOSONAR
    }

    private SharedPreferences.Editor getPreferenceEditObject() {
        SharedPreferences pref = context.getSharedPreferences(PREF_FILE, 0); // NOSONAR
        return pref.edit();
    }

    private boolean getPurchaseValueFromPref(String idPurchase) {
        return getPreferenceObject().getBoolean(idPurchase, false);
    }

    private void savePurchaseValueToPref(String idPurchase, boolean value) {
        getPreferenceEditObject().putBoolean(idPurchase, value).commit();
    }

    public void release() {
        if (billingClient != null) {
            billingClient.endConnection();
        }
    }

    /**
     * Verifies that the purchase was signed correctly for this developer's public key.
     * <p>Note: It's strongly recommended to perform such check on your backend since hackers can
     * replace this method with "constant true" if they decompile/rebuild your app.
     * </p>
     */
    private boolean verifyValidSignature(String signedData, String signature) {
        try {
            // To get key go to Developer Console > Select your app > Development Tools > Services & APIs.
            return Security.verifyPurchase(licenseKey, signedData, signature);
        } catch (IOException e) {
            return false;
        }
    }

    private void sendToUserError(String message) {
        if (context != null)
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
        sendErrorAmplitude(message);
    }

    private void sendErrorAmplitude(String message) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("withEventProperties", message);
        } catch (JSONException ignored) {
        }
    }

}
