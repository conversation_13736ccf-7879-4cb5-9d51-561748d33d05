package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentAgeRedesignBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;


public class RulerAgeRedesignFragment extends Fragment {
    private FragmentAgeRedesignBinding binding;
    private static final String KEY_AGE_VALUE = "KEY_AGE_VALUE_";
    private boolean isLBS = false;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;
    private float lastValue = 0f;

    public static RulerAgeRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        RulerAgeRedesignFragment fragment = new RulerAgeRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View] Current Age View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentAgeRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();
        String keyIsLBS = "KEY_IS_LBS";
        isLBS = prefsUtilsWtContext.getBooleanPreferenceProfile(keyIsLBS, false);
        binding.rulerView.setOnValueChangedListener((value, isProgrammaticScroll) -> {
            binding.weightVal.setText(String.format(Integer.valueOf((int) value).toString()));
            if (lastValue != value) {
                setVibrate(getView());
                lastValue = value;
            }
        });

        binding.rlContinue.setOnClickListener(view1 -> {
            isValidPush = false;
            int currentValue = (int) (binding.rulerView.getCurrentValue());
            prefsUtilsWtContext.setIntegerPreference(KEY_AGE_VALUE, currentValue);
            MealRedesignFragment fragmentC = MealRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_").commitAllowingStateLoss();
            setVibrate(view1);
        });
        binding.backLayout.setOnClickListener(view12 -> toBack());
        binding.backTxt.getPaint().setUnderlineText(true);
        setParamsFragment();
    }

    public void setParamsFragment() {
        Log.d("TestWeight", "weight Accessed isLbs-->" + isLBS);
        int valueWeight = prefsUtilsWtContext.getIntegerPreference(KEY_AGE_VALUE, 20);
        binding.textView16.setText(Translate.getValue("how_old_are_you"));
        binding.btnContinue.setText(Translate.getValue("next"));
        binding.backTxt.setText(Translate.getValue("go_back"));
        binding.linearProgress.setProgress(54);
        binding.rulerView.setMaxValue(100);
        binding.rulerView.setCurrentValue(valueWeight);
        binding.weightVal.setText(String.valueOf((int) (binding.rulerView.getCurrentValue())));
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
                toBack();
                return true; // pretend we've processed it
            } else
                return false; // pass on to be processed as normal
        });
    }

    private void toBack() {
        isValidPush = false;
        FragmentManager fm = getParentFragmentManager();
        fm.popBackStack("frag_age", FragmentManager.POP_BACK_STACK_INCLUSIVE);
    }
}
