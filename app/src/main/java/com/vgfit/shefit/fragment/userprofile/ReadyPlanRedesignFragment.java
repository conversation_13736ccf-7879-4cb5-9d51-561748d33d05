package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;
import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Outline;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.ViewTreeObserver;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.nostra13.universalimageloader.core.ImageLoader;
import com.vgfit.shefit.StartScreen;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentReadyRedesignBinding;
import com.vgfit.shefit.util.FitnessCalculator;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.screen.ImageUtils;

import eightbitlab.com.blurview.BlurAlgorithm;
import eightbitlab.com.blurview.RenderEffectBlur;
import eightbitlab.com.blurview.RenderScriptBlur;

public class ReadyPlanRedesignFragment extends Fragment {
    private FragmentReadyRedesignBinding binding;
    private static final boolean IS_FT = false;
    private static final int STROKE_SIZE = 2;
    private static final String COLOR_START = "#E0FF21";
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;
    private int startPointStepsX = 0;
    private int startPointStepsY = 0;
    private int endPointStepsX = 0;
    private int endPointStepsY = 0;

    private int startPointWorkoutX = 0;
    private int startPointWorkoutY = 0;
    private int endPointWorkoutX = 0;
    private int endPointWorkoutY = 0;

    private int startPointCaloriesX = 0;
    private int startPointCaloriesY = 0;
    private int endPointCaloriesX = 0;
    private int endPointCaloriesY = 0;

    private int startPointWaterX = 0;
    private int startPointWaterY = 0;
    private int endPointWaterX = 0;
    private int endPointWaterY = 0;

    public static ReadyPlanRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        ReadyPlanRedesignFragment fragment = new ReadyPlanRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View]Ready personal plan View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentReadyRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();
        ViewTreeObserver vto = binding.stepsBt.getViewTreeObserver();
        vto.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                int[] location = new int[2];
                binding.stepsBt.getLocationOnScreen(location);
                int x = location[0];
                int y = location[1];
                endPointStepsX = x;
                endPointStepsY = y + binding.stepsBt.getHeight() - binding.stepsBt.getHeight() / 4;

                location = new int[2];
                binding.workoutBt.getLocationOnScreen(location);
                x = location[0];
                y = location[1];
                endPointWorkoutX = x + binding.workoutBt.getWidth() / 2;
                endPointWorkoutY = y + binding.workoutBt.getHeight();

                location = new int[2];
                binding.caloriesBt.getLocationOnScreen(location);
                x = location[0];
                y = location[1];
                endPointCaloriesX = x + binding.caloriesBt.getWidth();
                endPointCaloriesY = y + binding.caloriesBt.getHeight() / 2 - 10;

                location = new int[2];
                binding.waterBt.getLocationOnScreen(location);
                x = location[0];
                y = location[1];
                endPointWaterX = x + 5;
                endPointWaterY = y + binding.waterBt.getHeight() / 3;

                //end Point
                location = new int[2];
                binding.pointSteps.getLocationOnScreen(location);
                startPointStepsX = location[0];
                startPointStepsY = location[1];

                location = new int[2];
                binding.pointWater.getLocationOnScreen(location);
                startPointWorkoutX = location[0];
                startPointWorkoutY = location[1];

                location = new int[2];
                binding.pointCalories.getLocationOnScreen(location);
                startPointCaloriesX = location[0];
                startPointCaloriesY = location[1];

                location = new int[2];
                binding.pointWater.getLocationOnScreen(location);
                startPointWaterX = location[0];
                startPointWaterY = location[1];

                view.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                switchAreaTraining();
            }
        });
        ImageLoader.getInstance().displayImage("assets://onboardingImage/16.webp", binding.imageBackground, ImageUtils.getDefaultDisplayImageOptions(), null);
        ImageLoader.getInstance().displayImage("assets://onboardingImage/17.webp", binding.imageReady, ImageUtils.getDefaultDisplayImageOptions(), null);
        setParamsFragment();
        binding.startApp.setOnClickListener(v -> {
            isValidPush = false;
            setVibrate(v);
            Log.d("TestClick", "next activate");
            if (getActivity() != null)
                ((StartScreen) getActivity()).closeFragment();
        });
        // Rounded corners + casting elevation shadow with transparent background
        binding.workoutBtBlur.setClipToOutline(true);
        binding.workoutBtBlur.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                binding.workoutBtBlur.getBackground().getOutline(outline);
                outline.setAlpha(1f);
            }
        });
        binding.stepsBtBlur.setClipToOutline(true);
        binding.stepsBtBlur.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                binding.stepsBtBlur.getBackground().getOutline(outline);
                outline.setAlpha(1f);
            }
        });
        binding.caloriesBtBlur.setClipToOutline(true);
        binding.caloriesBtBlur.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                binding.caloriesBtBlur.getBackground().getOutline(outline);
                outline.setAlpha(1f);
            }
        });
        binding.waterBtBlur.setClipToOutline(true);
        binding.waterBtBlur.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                binding.waterBtBlur.getBackground().getOutline(outline);
                outline.setAlpha(1f);
            }
        });
        setupBlurView();

    }

    @NonNull
    private BlurAlgorithm getBlurAlgorithm() {

        BlurAlgorithm algorithm;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            algorithm = new RenderEffectBlur();
        } else {
            algorithm = new RenderScriptBlur(getContext());
        }
        return algorithm;
    }

    private void setupBlurView() {
        final float radius = 5f;
        //set background, if your root layout doesn't have one
        Drawable windowBackground = getActivity().getWindow().getDecorView().getBackground();
        BlurAlgorithm algorithm = getBlurAlgorithm();
        binding.workoutBtBlur.setupWith(binding.root, algorithm)
                .setFrameClearDrawable(windowBackground)
                .setBlurRadius(radius);
        windowBackground = getActivity().getWindow().getDecorView().getBackground();
        algorithm = getBlurAlgorithm();
        binding.caloriesBtBlur.setupWith(binding.root, algorithm)
                .setFrameClearDrawable(windowBackground)
                .setBlurRadius(radius);
        windowBackground = getActivity().getWindow().getDecorView().getBackground();
        algorithm = getBlurAlgorithm();
        binding.waterBtBlur.setupWith(binding.root, algorithm)
                .setFrameClearDrawable(windowBackground)
                .setBlurRadius(radius);
        windowBackground = getActivity().getWindow().getDecorView().getBackground();
        algorithm = getBlurAlgorithm();
        binding.stepsBtBlur.setupWith(binding.root, algorithm)
                .setFrameClearDrawable(windowBackground)
                .setBlurRadius(radius);

    }

    public void setParamsFragment() {
        Log.d("TestWeight", "weight Accessed isLbs-->" + IS_FT);
        String title = Translate.getValue("personal_plan!") + " ";
        binding.textView16.setText(title);
        binding.textView24.setText(Translate.getValue("here’s_a_quick_preview_of_your"));
        binding.textView25.setText(Translate.getValue("ready_to_start"));
        binding.startApp.setText(Translate.getValue("let’s_go"));

        binding.wkInfo.setText(Translate.getValue("workout_time"));
        binding.caloriesInfo.setText(Translate.getValue("calories"));
        binding.stepsInfo.setText(Translate.getValue("steps"));
        binding.waterInfo.setText(Translate.getValue("water"));
        getMuscleTypeList();
    }

    public void getMuscleTypeList() {

        int selected;
        int time = 30;
        String selTemp = prefsUtilsWtContext.getStringPreferenceProfile("active_daily_workout");
        if (selTemp == null)
            selected = 1;
        else selected = Integer.parseInt(selTemp);
        if (selected == 1) {
            time = 28;
        } else if (selected == 2) {
            time = 50;
        } else if (selected == 3) {
            time = 55;
        }
        String finalTime = time + " " + Translate.getValue("min");
        String finalCalories = FitnessCalculator.getDailyCalories(getContext()) + " " + Translate.getValue("cal").toLowerCase();
        String finalWater = FitnessCalculator.getDailyWater(getContext()) + " l";
        int finalSteps = FitnessCalculator.getDailySteps(getContext());
        binding.wkInfoValue.setText(finalTime);
        binding.stepsInfoValue.setText(String.valueOf(finalSteps));
        binding.caloriesInfoValue.setText(finalCalories);
        binding.waterInfoValue.setText(finalWater);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener((v, keyCode, event) -> {
            return keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN; // pretend we've processed it
        });
    }

    private void switchAreaTraining() {
        Context context = getContext();

        if (context != null) {
            binding.lineAnimate1.init(startPointStepsX, startPointStepsY, endPointStepsX, endPointStepsY, Color.parseColor(COLOR_START), dpToPx(context, STROKE_SIZE));
            binding.lineAnimate1.setFinishedListener(() -> {
                binding.stepsBt.setVisibility(View.VISIBLE);
                setAnimation(binding.stepsBt);
            });
            binding.lineAnimate2.init(startPointWorkoutX, startPointWorkoutY, endPointWorkoutX, endPointWorkoutY, Color.parseColor(COLOR_START), dpToPx(context, STROKE_SIZE));
            binding.lineAnimate2.setFinishedListener(() -> {
                binding.workoutBt.setVisibility(View.VISIBLE);
                setAnimation(binding.workoutBt);
            });
            binding.lineAnimate3.init(startPointCaloriesX, startPointCaloriesY, endPointCaloriesX, endPointCaloriesY, Color.parseColor(COLOR_START), dpToPx(context, STROKE_SIZE));
            binding.lineAnimate3.setFinishedListener(() -> {
                binding.caloriesBt.setVisibility(View.VISIBLE);
                setAnimation(binding.caloriesBt);
            });
            binding.lineAnimate4.init(startPointWaterX, startPointWaterY, endPointWaterX, endPointWaterY, Color.parseColor(COLOR_START), dpToPx(context, STROKE_SIZE));
            binding.lineAnimate4.setFinishedListener(() -> {
                binding.waterBt.setVisibility(View.VISIBLE);
                setAnimation(binding.waterBt);
            });
        }
//
    }

    public void setAnimation(View view) {
        Animation anim = new AlphaAnimation(0.0f, 1.0f);
        anim.setDuration(300); //You can manage the blinking time with this parameter
        anim.setStartOffset(20);
        anim.setRepeatMode(Animation.REVERSE);
        anim.setRepeatCount(2);
        view.startAnimation(anim);
    }
}
