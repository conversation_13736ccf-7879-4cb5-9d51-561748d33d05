package com.vgfit.shefit.fragment.workouts.widjetworkout;

import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;
import static com.vgfit.shefit.util.screen.PresentBottomBar.getHeightBottomBar;
import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.content.Context;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;
import androidx.transition.TransitionInflater;

import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.MenuBottom;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.LayoutWorkoutBeautyWidgetBinding;
import com.vgfit.shefit.fragment.workouts.widjetworkout.adapter.AdapterWidgetCarousel;
import com.vgfit.shefit.fragment.workouts.widjetworkout.callbacks.ItemWidgetWorkoutClicked;
import com.vgfit.shefit.realm.Superset;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.RCUtils;
import com.vgfit.shefit.util.Translate;
import com.yarolegovich.discretescrollview.DSVOrientation;
import com.yarolegovich.discretescrollview.DiscreteScrollView;
import com.yarolegovich.discretescrollview.transform.ScaleTransformer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;

public class WorkoutFragmentBeautyRecycler extends Fragment implements ItemWidgetWorkoutClicked, DiscreteScrollView.OnItemChangedListener {
    private LayoutWorkoutBeautyWidgetBinding binding;
    private ArrayList<Superset> listWorkout;
    private Context context;
    private int heightCell;
    private int widthCell;
    DisplayMetrics displaymetrics;
    private final List<Integer> newOrder = new ArrayList<>();


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.context = getContext();
        listWorkout = new ArrayList<>();
        newOrder.addAll(Arrays.asList(4, 5, 1, 3, 2, 6));
        List<Superset> supersetList;
        try (Realm realm = Realm.getDefaultInstance()) {
            RealmResults<Superset> supersetResultRealmList = realm.where(Superset.class).findAll();
            supersetResultRealmList = supersetResultRealmList.sort("order_");
            supersetList = realm.copyFromRealm(supersetResultRealmList);
        }
        for (int order : newOrder) {
            for (Superset superset : supersetList) {
                if (superset.getOrder() == order) {
                    listWorkout.add(superset);
                }
            }
        }

        displaymetrics = new DisplayMetrics();
        if (getActivity() != null)
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(displaymetrics);
        heightCell = displaymetrics.heightPixels - dpToPx(context, 77) - dpToPx(context, 50) - getHeightBottomBar(context); //55 dp top Layout - 40 dp Tablayout
        widthCell = displaymetrics.widthPixels - dpToPx(context, 60);


    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = LayoutWorkoutBeautyWidgetBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.e("FragmentTest", "frag1");
        setStatusBar(view);
        binding.top.menuButton.setOnClickListener(v ->
        {
            if ((MainActivity) getActivity() != null) ((MainActivity) getActivity()).openDrawer();
        });
        binding.top.menuButton.setVisibility(View.GONE);
        binding.top.nameFragment.setText(Translate.getValue("extra_workouts"));
        AdapterWidgetCarousel adapterWidgetCarousel = new AdapterWidgetCarousel(listWorkout, context, this);
        adapterWidgetCarousel.setHeightCell(heightCell, widthCell);
        binding.listHorizontal.setOrientation(DSVOrientation.HORIZONTAL);
        binding.listHorizontal.addOnItemChangedListener(this);
        binding.listHorizontal.setAdapter(adapterWidgetCarousel);
        binding.listHorizontal.setItemTransitionTimeMillis(100);
        binding.listHorizontal.setItemTransformer(new ScaleTransformer.Builder()
                .setMinScale(0.92f)
                .build());
        binding.listHorizontal.setOffscreenItems(2);
        binding.listHorizontal.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            public void onGlobalLayout() {
                Log.d("TestHeightCell", "heightCell before-->" + heightCell);
                heightCell = binding.listHorizontal.getHeight();
                Log.d("TestHeightCell", "heightCell after-->" + heightCell);

                binding.listHorizontal.getLayoutParams().height = heightCell;
                binding.listHorizontal.requestLayout();
                adapterWidgetCarousel.setHeightCell(heightCell, widthCell);
                binding.listHorizontal.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
        new MenuBottom((MainActivity) getActivity(), binding.containerMenu);
    }

    @Override
    public void thisWorkoutClicked(Superset itemCarousel, int position, ImageView imageView, TextView textView, TextView textViewSecond, RelativeLayout pagerContainer) {
        if (!Constant.premium)
            openMainSubscribe();
        else
            openWorkout(itemCarousel, position, imageView, textView, textViewSecond, pagerContainer);
    }

    private void openWorkout(Superset itemCarousel, int position, ImageView imageView, TextView textView, TextView textViewSecond, RelativeLayout pagerContainer) {
        WorkoutWithLevel simpleFragmentB = WorkoutWithLevel.newInstance(itemCarousel, ViewCompat.getTransitionName(imageView), ViewCompat.getTransitionName(textView), ViewCompat.getTransitionName(textViewSecond), ViewCompat.getTransitionName(pagerContainer), position);
        setSharedElementReturnTransition(TransitionInflater.from(
                getActivity()).inflateTransition(R.transition.simple_fragment_transition));
        setExitTransition(TransitionInflater.from(
                getActivity()).inflateTransition(android.R.transition.fade));

        simpleFragmentB.setSharedElementEnterTransition(TransitionInflater.from(
                getActivity()).inflateTransition(R.transition.simple_fragment_transition));
        simpleFragmentB.setEnterTransition(TransitionInflater.from(
                getActivity()).inflateTransition(android.R.transition.fade));

        getParentFragmentManager()
                .beginTransaction()
                .addSharedElement(imageView, ViewCompat.getTransitionName(imageView))
                .addSharedElement(textView, ViewCompat.getTransitionName(textView))
                .addSharedElement(textViewSecond, ViewCompat.getTransitionName(textViewSecond))
                .addSharedElement(pagerContainer, ViewCompat.getTransitionName(pagerContainer))
                .addToBackStack("roomWorkout")
                .replace(R.id.root_fragment, simpleFragmentB)
                .commit();
        Log.e("TestSuperset", "TestSuperset idWorkout==>" + itemCarousel.getWorkouts());
    }

    @Override
    public void onCurrentItemChanged(@Nullable RecyclerView.ViewHolder viewHolder, int i) {
        Log.e("TCurrentItemChanged", "i==>" + i);
    }

    private void openMainSubscribe() {
        RCUtils rcUtils = new RCUtils(getContext());
        rcUtils.getMainPaywallAndroidOffer(this);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
