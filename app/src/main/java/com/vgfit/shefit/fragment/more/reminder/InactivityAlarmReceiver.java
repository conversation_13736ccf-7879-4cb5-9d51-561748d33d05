package com.vgfit.shefit.fragment.more.reminder;

import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.media.RingtoneManager;
import android.net.Uri;

import androidx.core.app.NotificationCompat;

import com.vgfit.shefit.R;
import com.vgfit.shefit.fragment.more.MoreSettingsReminderFr;
import com.vgfit.shefit.fragment.more.service.SharedPreferencesData;

public class InactivityAlarmReceiver extends BroadcastReceiver {
    int MID = 1;
    SharedPreferencesData sharedPreferencesData;

    @Override
    public void onReceive(Context context, Intent intent) {
        sharedPreferencesData = new SharedPreferencesData(context);
        long currentDate = System.currentTimeMillis();
        long lastDate = sharedPreferencesData.getLastDate();
        int frequency = sharedPreferencesData.getFrequency();

        if (currentDate - lastDate > frequency * 24 * 60 * 60 * 1000 - 60000) {
            NotificationManager notificationManager = (NotificationManager) context
                    .getSystemService(Context.NOTIFICATION_SERVICE);

            Intent notificationIntent = new Intent(context, MoreSettingsReminderFr.class);
            notificationIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

            PendingIntent pendingIntent = null;
            pendingIntent = PendingIntent.getActivity(context, 0,
                    notificationIntent, PendingIntent.FLAG_IMMUTABLE);

            Uri alarmSound = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);

            NotificationCompat.Builder mNotifyBuilder = new NotificationCompat.Builder(
                    context, "notificationInactivityId")
                    .setSmallIcon(R.drawable.ic_instagram)
                    .setContentTitle("Inactivity Notification")
                    .setContentText("You haven't exercised a few days!")
                    .setSound(alarmSound)
                    .setAutoCancel(true)
                    .setWhen(currentDate)
                    .setContentIntent(pendingIntent)
                    .setVibrate(new long[]{1000, 1000, 1000, 1000, 1000});
            if (notificationManager != null)
                notificationManager.notify(MID, mNotifyBuilder.build());

            MID++;
        }
    }
}