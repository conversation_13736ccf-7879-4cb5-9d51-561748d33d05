package com.vgfit.shefit.fragment.workouts;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.FragmentWorkoutsBinding;
import com.vgfit.shefit.fragment.more.service.SharedPreferencesData;
import com.vgfit.shefit.fragment.workouts.adapter.workouts_adapters.redesign_1.CardFragmentPagerAdapter;
import com.vgfit.shefit.fragment.workouts.adapter.workouts_adapters.redesign_1.ShadowTransformer;
import com.vgfit.shefit.fragment.workouts.callbacks.ItemClickSupersetRealm;
import com.vgfit.shefit.realm.Superset;
import com.vgfit.shefit.realm.Workout;
import com.vgfit.shefit.util.Translate;

import java.util.ArrayList;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;

public class WorkoutsFr extends Fragment implements ItemClickSupersetRealm {
    private FragmentWorkoutsBinding binding;
    private View view;
    private List<Superset> supersetList;
    private ArrayList<String> listSavedInstances;
    private SharedPreferencesData sharedPreferencesData;
    private int levelsPosition;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        sharedPreferencesData = new SharedPreferencesData(getContext());
        try (Realm realm = Realm.getDefaultInstance()) {
            RealmResults<Superset> supersetResultRealmList = realm.where(Superset.class).findAll();
            supersetResultRealmList = supersetResultRealmList.sort("order_");
            supersetList = realm.copyFromRealm(supersetResultRealmList);
        }
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup parent, Bundle savedInstanceState) {
        binding=FragmentWorkoutsBinding.inflate(inflater,parent,false);
        view = view.getRootView();

        listSavedInstances = new ArrayList<>(sharedPreferencesData.retrieveScaleData(supersetList.size()));
        initRecyclerView();
        ((MainActivity) requireContext()).goToPrevious();
        return view;
    }

    private void initRecyclerView() {
        RecyclerView recyclerView = view.findViewById(R.id.recycler_view);
        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerView.setNestedScrollingEnabled(false);

        if (getContext() != null) {
            CardFragmentPagerAdapter pagerAdapter = new CardFragmentPagerAdapter(getParentFragmentManager(), dpToPixels(2, getContext()));
            ShadowTransformer fragmentCardShadowTransformer = new ShadowTransformer(binding.viewPager, pagerAdapter, getActivity());
            fragmentCardShadowTransformer.enableScaling(true);

            binding.viewPager.setAdapter(pagerAdapter);
            binding.viewPager.setPageTransformer(false, fragmentCardShadowTransformer);
            binding.viewPager.setOffscreenPageLimit(3);
        }
    }

    public static float dpToPixels(int dp, Context context) {
        return dp * (context.getResources().getDisplayMetrics().density);
    }

    @Override
    public void onItemClickSupersetRealm(int supersetsPosition) {
        levelsPosition = Integer.parseInt(listSavedInstances.get(supersetsPosition));
        String supersetName = Translate.getValue(supersetList.get(supersetsPosition).getName());
        String supersetImageId = supersetList.get(supersetsPosition).getName() + (levelsPosition + 1);
        changeFragmentNew(supersetsPosition, supersetList.get(supersetsPosition).getWorkouts().get(levelsPosition), supersetName, supersetImageId);
    }

    private void changeFragmentNew(int supersetsPosition, Workout workout, String supersetName, String supersetImageId) {
        FragmentTransaction fragmentTransaction;
        if (workout != null) {
            fragmentTransaction = getParentFragmentManager().beginTransaction();
            fragmentTransaction.addToBackStack(null);
            WorkoutsExercisesFr workoutsExercisesFr = new WorkoutsExercisesFr();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("workout", new ArrayList<>(workout.getWorkoutExercises()));
            bundle.putString("supersetName", supersetName);
            bundle.putString("supersetImageId", supersetImageId);
            bundle.putInt("supersetsPosition", supersetsPosition);
            bundle.putInt("levelsPosition", levelsPosition);
            String level;
            level = "";
            bundle.putString("level", level);
            workoutsExercisesFr.setArguments(bundle);
            fragmentTransaction.replace(R.id.root_fragment, workoutsExercisesFr);
            fragmentTransaction.commit();
        }
    }
}