package com.vgfit.shefit.fragment.premium.redesign;

import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.vgfit.shefit.databinding.Part3SubscribeBinding;
import com.vgfit.shefit.fragment.premium.redesign.adapter.AdapterWorkoutPreview;
import com.vgfit.shefit.util.Translate;
import com.yarolegovich.discretescrollview.DSVOrientation;
import com.yarolegovich.discretescrollview.DiscreteScrollView;
import com.yarolegovich.discretescrollview.transform.ScaleTransformer;

import java.util.Arrays;


public class Part3_Fragment extends Fragment implements DiscreteScrollView.OnItemChangedListener {
    private Part3SubscribeBinding binding;
    private int currentPosition = 0;
    private boolean isValidScroll = true;

    public static Part3_Fragment newInstance() {

        Bundle args = new Bundle();

        Part3_Fragment fragment = new Part3_Fragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = Part3SubscribeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        AdapterWorkoutPreview adapterPreview = new AdapterWorkoutPreview();
        binding.listHorizontal.setOrientation(DSVOrientation.HORIZONTAL);
        binding.listHorizontal.addOnItemChangedListener(this);
        binding.listHorizontal.setAdapter(adapterPreview);
        binding.listHorizontal.setItemTransitionTimeMillis(300);
        binding.listHorizontal.setItemTransformer(new ScaleTransformer.Builder()
                .setMinScale(0.90f)
                .build());
        binding.listHorizontal.setOffscreenItems(2);
        autoScroll();
        binding.shortDescription.setText(Translate.getValueLine("workouts_adapted_to_your_fitness_level,_goals_and_sсhedule", Arrays.asList(5), false));
        binding.topTitle.setText(Translate.getValueLine("personalized_daily_workouts", Arrays.asList(1), false));
        String countDayTxt = "28 " + Translate.getValue("daily_workouts");
        binding.countDay.setText(countDayTxt);
        binding.listHorizontal.addOnItemTouchListener(new RecyclerView.OnItemTouchListener() {
            @Override
            public boolean onInterceptTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {
                if (e.getAction() == MotionEvent.ACTION_DOWN) {
                    isValidScroll = false;
                    binding.listHorizontal.removeOnItemTouchListener(this);
                }
                return false;
            }

            @Override
            public void onTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {
            }

            @Override
            public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {
            }
        });
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onCurrentItemChanged(@Nullable RecyclerView.ViewHolder viewHolder, int i) {

    }

    private void autoScroll() {
        final int speedScroll = 1000; // delay between scrolls in milliseconds
        final Handler handler = new Handler();
        final Runnable runnable = new Runnable() {
            public void run() {
                if (binding.listHorizontal.getLayoutManager() != null) {
                    try {
                        if (currentPosition == binding.listHorizontal.getLayoutManager().getItemCount())
                            currentPosition = 0;
                        if (isValidScroll) {
                            binding.listHorizontal.smoothScrollToPosition(currentPosition++);
                            handler.postDelayed(this, speedScroll);
                        }
                    } catch (Exception ignored) {
                    }

                }
            }
        };
        if (isValidScroll)
            handler.postDelayed(runnable, speedScroll);
    }
}
