package com.vgfit.shefit.fragment.more.service;

import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Build;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.content.ContextCompat;

import com.vgfit.shefit.R;

public class AlertDialogMore {

    private Context context;

    public AlertDialogMore(Context context) {
        this.context = context;
    }

    public void createDialog(String message, String title, String typeDialog) {
        TextView titleView = new TextView(context);
        titleView.setText(title);
        titleView.setGravity(Gravity.CENTER);
        titleView.setPadding(20, 20, 20, 20);
        titleView.setTextSize(20F);
        titleView.setTypeface(Typeface.DEFAULT_BOLD);
        titleView.setBackgroundColor(ContextCompat.getColor(context, R.color.bootstrap_gray));
        titleView.setTextColor(ContextCompat.getColor(context, R.color.white));

        AlertDialog.Builder alert = new AlertDialog.Builder(context);
        final EditText editText = new EditText(context);
        editText.setPadding(20, 20, 20, 20);
        alert.setCustomTitle(titleView);
        alert.setMessage(message);
//        alert.setTitle(title);
        alert.setView(editText);

        alert.setPositiveButton("OK", (dialog, okButton) -> {
            final Intent emailIntent = new Intent(android.content.Intent.ACTION_SEND);
            emailIntent.setType("message/rfc822");

            String textMessage = editText.getText().toString();

            if (typeDialog.equalsIgnoreCase("to friend")) {
                emailIntent.putExtra(android.content.Intent.EXTRA_TEXT, textMessage);
                emailIntent.putExtra(android.content.Intent.EXTRA_SUBJECT, "Recommendation from your friend");
            } else {
                if (typeDialog.equalsIgnoreCase("feedback")) {
                    emailIntent.putExtra(android.content.Intent.EXTRA_TEXT, textMessage + "\n"
                            + "______________________________________" + "\n"
                            + "PROPERTY:" + System.getProperty("os.version") + "\n"
                            + "SDK: " + Build.VERSION.SDK_INT + "\n"
                            + "DEVICE: " + android.os.Build.DEVICE + "\n"
                            + "MODEL: " + android.os.Build.MODEL + "\n"
                            + "PRODUCT: " + android.os.Build.PRODUCT + "\n"
                            + "BRAND: " + Build.BRAND + "\n"
                            + "______________________________________" + "\n"
                    );
                    emailIntent.putExtra(android.content.Intent.EXTRA_SUBJECT, "She fit. User's feedback.");
                } else {
                    emailIntent.putExtra(android.content.Intent.EXTRA_TEXT, textMessage);
                    emailIntent.putExtra(android.content.Intent.EXTRA_SUBJECT, "User's message");
                }

                emailIntent.putExtra(android.content.Intent.EXTRA_EMAIL, new String[]{
                        "<EMAIL>"
                });
            }

            try {
                context.startActivity(Intent.createChooser(emailIntent, "Send email using..."));
            } catch (android.content.ActivityNotFoundException ex) {
                Toast.makeText(context, "No email clients installed.", Toast.LENGTH_SHORT).show();
            }
        });

        alert.setNegativeButton("Cancel", (dialog, cancelButton) -> dialog.dismiss());
        final AlertDialog dialog = alert.create();
        dialog.show();
        dialog.getButton(AlertDialog.BUTTON_POSITIVE).setEnabled(false);
        dialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(Color.parseColor("#000000"));
        dialog.getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(Color.parseColor("#000000"));

        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (TextUtils.isEmpty(s))
                    dialog.getButton(AlertDialog.BUTTON_POSITIVE).setEnabled(false);
                else
                    dialog.getButton(AlertDialog.BUTTON_POSITIVE).setEnabled(true);
            }
        });
    }
}