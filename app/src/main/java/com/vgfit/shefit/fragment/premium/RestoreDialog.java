package com.vgfit.shefit.fragment.premium;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Typeface;
import android.widget.TextView;

import com.vgfit.shefit.R;

import at.grabner.circleprogress.CircleProgressView;
import at.grabner.circleprogress.TextMode;


/**
 * Created by Nemo on 2/1/17.
 */

public class RestoreDialog {
    CircleProgressView mCircleView;
    Dialog d;

    public RestoreDialog() {

    }

    public void setDialog(Context context,String text){
        d = new Dialog(context, R.style.ActivityDialog);
        System.out.println("se dezarhiveaza");
        d.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        d.setContentView(R.layout.dialog_loading);
        d.setCanceledOnTouchOutside(false);
        d.setCancelable(false);
        TextView progress_info=(TextView)d.findViewById(R.id.progres_info);
        mCircleView = (CircleProgressView) d.findViewById(R.id.circleView);
        mCircleView.setOnProgressChangedListener(new CircleProgressView.OnProgressChangedListener() {
            @Override
            public void onProgressChanged(float value) {
                //  Log.d(TAG, "Progress Changed: " + value);
            }
        });
        mCircleView.setShowTextWhileSpinning(true); // Show/hide text in spinning mode
        mCircleView.setText("");
        mCircleView.spin();
        mCircleView.setSpinSpeed(5.9f);
        mCircleView.setShowTextWhileSpinning(true);
        mCircleView.setTextMode(TextMode.TEXT);
        mCircleView.setSpinningBarLength(250f);
        progress_info.setText(text);
//        mCircleView.setTextSize(18);
        d.show();
    }

    public void DestroyDialog(){
        try {
            d.dismiss();
        }catch (Exception e){}

    }
}