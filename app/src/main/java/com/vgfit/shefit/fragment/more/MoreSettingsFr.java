package com.vgfit.shefit.fragment.more;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.FragmentMoreSettingsBinding;


public class MoreSettingsFr extends Fragment {
    View view;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        com.vgfit.shefit.databinding.FragmentMoreSettingsBinding binding = FragmentMoreSettingsBinding.inflate(inflater, container, false);
        view = binding.getRoot();
        binding.cardViewReminder.setOnClickListener(v -> changeFragment());
        return view;
    }

    private void changeFragment() {
        FragmentTransaction fragmentTransaction = getParentFragmentManager().beginTransaction();
        fragmentTransaction.addToBackStack(null);
        fragmentTransaction.replace(R.id.root_fragment, new MoreSettingsReminderFr());
        fragmentTransaction.commit();
    }
}