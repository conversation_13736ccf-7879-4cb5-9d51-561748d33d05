package com.vgfit.shefit.fragment.profile;

import static com.vgfit.shefit.util.TextUtil.getLanguageName;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;
import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.aigestudio.wheelpicker.WheelPicker;
import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.MenuBottom;
import com.vgfit.shefit.R;
import com.vgfit.shefit.api.service.TranslatorService;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.billing.BillingProcessor;
import com.vgfit.shefit.databinding.FragmentProfileBinding;
import com.vgfit.shefit.fragment.composeui.UniversalElasticParallax;
import com.vgfit.shefit.fragment.more.service.AlertDialogMore;
import com.vgfit.shefit.fragment.premium.SubscribeTimer;
import com.vgfit.shefit.fragment.userprofile.progress.ProgressUserFragment;
import com.vgfit.shefit.fragment.userprofile.settings.SettingsProfileFragment;
import com.vgfit.shefit.realm.Languages;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.InfoDevice;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.dialog.DialogHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import io.realm.Realm;
import io.realm.RealmResults;

public class ProfileFr extends Fragment {
    private FragmentProfileBinding binding;
    AlertDialogMore alertDialogMore;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private Context context;
    private ArrayList<Languages> languagesArrayList;
    private static final String KEY_LANGUAGE = "langDevice";
    private UniversalElasticParallax elasticParallax;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        context = getContext();
        languagesArrayList = new ArrayList<>();
        alertDialogMore = new AlertDialogMore(context);
        prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        Realm realm = Realm.getDefaultInstance();
        RealmResults<Languages> languages = realm.where(Languages.class).findAll();
        if (!languages.isEmpty()) languagesArrayList.addAll(languages);
    }


    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentProfileBinding.inflate(inflater, container, false);
        View view = binding.getRoot();
        setStatusBar(binding.top.getRoot());
        binding.top.menuButton.setOnClickListener(v ->
        {
            setVibrate(v);
            if (getActivity() != null) ((MainActivity) getActivity()).openDrawer();
        });
        binding.top.menuButton.setVisibility(View.INVISIBLE);

        binding.rlInfo.setOnClickListener(l -> {
            setVibrate(l);
            ProfileSubscriptionFr profileSubscriptionFr = new ProfileSubscriptionFr();
            changeFragment(profileSubscriptionFr);
        });

        binding.rlRestore.setOnClickListener(v -> {
            setVibrate(v);
            restorePurchase();
        });

        binding.rlEmail.setOnClickListener(l -> {
            doMessage("", "Send Feedback");
            setVibrate(l);
        });
        binding.rlProgress.setOnClickListener(v -> {
            Log.d("TestProgress", "My Progress clicked");
            setVibrate(v);
            ProgressUserFragment settingsProfileFragment = new ProgressUserFragment();
            FragmentTransaction fragmentTransaction = getParentFragmentManager().beginTransaction();
            fragmentTransaction.addToBackStack(null);
            fragmentTransaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            fragmentTransaction.replace(R.id.root_fragment, settingsProfileFragment);
            fragmentTransaction.commit();
        });

        setTranslateView();
        binding.rlProfile.setOnClickListener(v -> {
            setVibrate(v);
            SettingsProfileFragment settingsProfileFragment = SettingsProfileFragment.newInstance();
            FragmentTransaction fragmentTransaction = getParentFragmentManager().beginTransaction();
            fragmentTransaction.addToBackStack(null);
            fragmentTransaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            fragmentTransaction.replace(R.id.root_fragment, settingsProfileFragment);
            fragmentTransaction.commit();

        });
        binding.rlSubscribe.setOnClickListener(v -> {
            setVibrate(v);
            SubscribeTimer fragmentC = new SubscribeTimer();
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_up, R.anim.slide_down, 0, R.anim.slide_down);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_upgrade").commit();
        });
        binding.rlSettings.setOnClickListener(v -> {
            setVibrate(v);
            doLanguageSelect(languagesArrayList);
        });
        new MenuBottom((MainActivity) getActivity(), binding.containerMenu);
        setViewLoginActive();
        setupUniversalElasticParallax(view);
        return view;
    }
    private void setupUniversalElasticParallax(View view) {
        // Găsește view-urile din layout
        ScrollView scrollView = view.findViewById(R.id.scrollView);
        RelativeLayout containerView = view.findViewById(R.id.parallax_container); // View-ul principal
        RelativeLayout secondaryView = view.findViewById(R.id.line);     // View-ul secundar

        if (scrollView != null && containerView != null && secondaryView != null) {
            // Creează instanța Universal Elastic Parallax cu dezactivare click-uri
            elasticParallax = new UniversalElasticParallax(scrollView, containerView, secondaryView)
                    .setElasticFactor(2.0f)                    // Factor de smoothness
                    .setPrimaryStretchLimits(2f, 0.0f)       // Primary: Max 200%, Min 0%
                    .setSecondaryStretchLimits(2f, 0.0f)    // Secondary: Max 200%, Min 0%
                    .setAnimationDurations(1000, 980)           // Durată animații
                    .setDecelerationFactors(3.0f, 2.0f);     // Factori de încetinire progresivă
        }
    }
    private void restorePurchase() {
        if (getActivity() != null && MainActivity.readyToPurchase && ((MainActivity) getActivity()).bp != null) {
            BillingProcessor bp = ((MainActivity) getActivity()).bp;
            List<String> listPurchase = bp.listOwnedProducts();
            List<String> listSubscriptions = bp.listOwnedSubscriptions();
            if (listPurchase.isEmpty() && listSubscriptions.isEmpty()) {
                new DialogHelper(getContext()).showRestorePurchaseFailed(null);
            }else {
                new DialogHelper(getContext()).showRestorePurchaseSuccess(isConfirmed -> Constant.setPremium(true));
            }
        }
    }

    private boolean isLoginActive() {
        String email = prefsUtilsWtContext.getStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB);
        return email != null && !email.isEmpty();
    }

    private void setViewLoginActive() {
        boolean isLoginActive = isLoginActive();
        binding.rlRestore.setVisibility((isLoginActive && Constant.premium) ? View.GONE : View.VISIBLE);
        binding.rlInfo.setVisibility(isLoginActive ? View.GONE : View.VISIBLE);
    }

    private void setTranslateView() {
        binding.yourSelf.setText(Translate.getValue("create_yourself"));
        binding.labelFullAccess.setText(Translate.getValue("get_full_access"));
        binding.labelSubscrInfo.setText(Translate.getValue("subscription_info"));
        binding.labelEmailSuport.setText(Translate.getValue("email_support"));
        binding.labelRestorePurch.setText(Translate.getValue("restore_purchase"));
        binding.labelMyProfile.setText(Translate.getValue("my_profile"));
        binding.labelMyProgress.setText(Translate.getValue("my_progress"));
        binding.labelSettings.setText(Translate.getValue("language"));
    }

    private void changeFragment(Fragment profileChildFragment) {
        FragmentTransaction fragmentTransaction = getParentFragmentManager().beginTransaction();
        fragmentTransaction.addToBackStack("general_profile");
        fragmentTransaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
        fragmentTransaction.replace(R.id.root_fragment, profileChildFragment);
        fragmentTransaction.commit();
    }

    public void doLanguageSelect(List<Languages> listLanguageData) {
        String lang = Locale.getDefault().getLanguage();
        String langUser = prefsUtilsWtContext.getStringPreferenceProfile(KEY_LANGUAGE);
        if (langUser != null) {
            lang = langUser;
            Log.e("LangUser", "langUser==>" + lang);
        }
        ArrayList<String> listLanguage = new ArrayList<>();
        int posLangSelected = 0;
        for (int i = 0; i < listLanguageData.size(); i++) {
            Languages languageData = listLanguageData.get(i);
            String language = getLanguageName(languageData.getIsoOneCode());
            listLanguage.add(language);
            if (languageData.getIsoOneCode().equals(lang)) posLangSelected = i;
        }

        final Dialog d = new Dialog(context, R.style.Theme_AppCompat_Light_Dialog_Alert);
        if (d.getWindow() != null) {
            d.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
            d.setContentView(R.layout.dialog_language_body);
            d.setCanceledOnTouchOutside(true);
            d.setCancelable(true);
            WheelPicker wheelLanguage = d.findViewById(R.id.whell1);
            wheelLanguage.setData(listLanguage);
            wheelLanguage.setSelectedItemPosition(posLangSelected);
            wheelLanguage.setCyclic(true);
            wheelLanguage.setItemSpace(60);
            TextView title = d.findViewById(R.id.footer_txt);
            title.setText(Translate.getValue("choose_language"));
            final Button btnOk = d.findViewById(R.id.btnDone);
            btnOk.setText(Translate.getValue("done"));
            String finalLang = lang;
            btnOk.setOnClickListener(arg0 -> {
                setVibrate(arg0);
                String isoCode = listLanguageData.get(wheelLanguage.getCurrentItemPosition()).getIsoOneCode();
                if (!isoCode.equals(finalLang)) {
                    prefsUtilsWtContext.setStringPreferenceProfile(KEY_LANGUAGE, isoCode);
                    switchLanguage(getActivity());
                }
                d.dismiss();
            });

            d.show();
        }
    }

    public void doMessage(final String text, final String nameAction) {

        final Dialog d = new Dialog(context, R.style.Theme_AppCompat_Light_Dialog_Alert);
        if (d.getWindow() != null) {
            d.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
            d.setContentView(R.layout.send_message_dialog);
            d.setCanceledOnTouchOutside(false);
            d.setCancelable(false);
            final InfoDevice infoDevice = new InfoDevice(getContext());

            final EditText message = d.findViewById(R.id.message);
            final Button btnOk = d.findViewById(R.id.ok_exit);
            btnOk.setOnClickListener(arg0 -> {
                setVibrate(arg0);
                if (!message.getText().toString().isEmpty()) {
                    String textFinal = text + message.getText().toString();
                    textFinal = textFinal + "\n" + infoDevice.getFullInfoDevice();
                    sendMessage(nameAction, textFinal);
                    d.dismiss();
                }


            });
            btnOk.setEnabled(false);
            btnOk.setText(Translate.getValue("done"));
            message.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                    //empty
                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    //empty
                }

                @Override
                public void afterTextChanged(Editable s) {
                    String input = message.getText().toString();
                    input = input.trim();
                    btnOk.setEnabled(!input.isEmpty());
                }
            });

            Button btnCancel = d.findViewById(R.id.cancel_exit);
            btnCancel.setText(Translate.getValue("cancel"));
            btnCancel.setOnClickListener(v -> {
                setVibrate(v);
                d.dismiss();
            });
            d.show();
        }
    }

    private void sendMessage(String subject, String text) {
        String[] addresses = {"<EMAIL>"};
        Intent intent = new Intent(Intent.ACTION_SENDTO);
        intent.setData(Uri.parse("mailto:")); // only email apps should handle this
        intent.putExtra(Intent.EXTRA_EMAIL, addresses);
        intent.putExtra(Intent.EXTRA_SUBJECT, subject);
        intent.putExtra(Intent.EXTRA_TEXT, text);
        if (getActivity() != null && intent.resolveActivity(getActivity().getPackageManager()) != null) {
            startActivity(intent);
        }else {
            try {
                String title = Translate.getValue("mail_unavailable");
                String textBody = Translate.getValue("contact_email").replace("%@", "\<EMAIL>");
                new DialogHelper(getContext()).showDialogUniversal(title, textBody, isConfirmed -> {

                }, true);
            } catch (Exception ignored) {
                //ignored
            }
        }
    }


    private void switchLanguage(Activity activity) {
        String lang = getLanguageValid();
        new TranslatorService(context, isFilled -> {
            Handler handler = new Handler(Looper.getMainLooper());
            handler.postDelayed(() -> setLocale(activity), 1000);
        }).fillTranslate(lang);
    }

    private String getLanguageValid() {
        String lang = Locale.getDefault().getLanguage();
        String langUser = prefsUtilsWtContext.getStringPreferenceProfile(KEY_LANGUAGE);
        if (langUser != null) {
            lang = langUser;
        }
        return lang;
    }

    public static void setLocale(Activity activity) {
        try {
            if (activity != null) {
                Context ctx = activity.getApplicationContext();
                PackageManager pm = ctx.getPackageManager();
                Intent intent = pm.getLaunchIntentForPackage(ctx.getPackageName());
                if (intent != null) {
                    Intent mainIntent = Intent.makeRestartActivityTask(intent.getComponent());
                    mainIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    ctx.startActivity(mainIntent);
                    Runtime.getRuntime().exit(0);
                }
            }
        } catch (Exception ignored) {
            //ignored
        }
    }
}
