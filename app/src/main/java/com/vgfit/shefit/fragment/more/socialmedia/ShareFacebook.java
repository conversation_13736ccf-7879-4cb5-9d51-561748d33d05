package com.vgfit.shefit.fragment.more.socialmedia;

import android.content.Context;
import android.content.Intent;

public class ShareFacebook {
    Context context;

    public ShareFacebook(Context context) {
        this.context = context;
    }

    public void shareAppOnFacebook() {
//        File filePath = getContext().getFileStreamPath("shareimage.jpg");  //optional //internal storage
//        String textToShare = "http://play.google.com/store/apps/details?id=" + context.getPackageName();
        String textToShare = "https://play.google.com/store/apps/details?id=softin.my.fast.fitness&hl=en";
        String application = "com.facebook.katana";
        String name_action = "Share application to facebook";
        Intent shareIntent = new Intent();
        shareIntent.setAction(Intent.ACTION_SEND);
        shareIntent.putExtra(Intent.EXTRA_TEXT, textToShare);
//        shareIntent.putExtra(Intent.EXTRA_STREAM, Uri.fromFile(new File(String.valueOf(filePath))));  //optional//use this when you want to send an image
        shareIntent.setType("text/plain");
        shareIntent.setPackage(application);
        shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        context.startActivity(Intent.createChooser(shareIntent, name_action));
    }
}