package com.vgfit.shefit.fragment.composeui;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.compose.ui.platform.ComposeView;
import androidx.fragment.app.Fragment;

import com.vgfit.shefit.R;

import kotlin.Unit;

public class HomeFragment extends Fragment {

    private UniversalElasticParallax elasticParallax;

    public HomeFragment() {
        super(R.layout.fragment_home);
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Setup Universal Elastic Parallax
        setupUniversalElasticParallax(view);
    }

    /**
     * Setup Universal Elastic Parallax pentru orice 2 view-uri/containere
     */
    private void setupUniversalElasticParallax(View view) {
        // Găsește view-urile din layout
        ScrollView scrollView = view.findViewById(R.id.scrollView);
        RelativeLayout containerView = view.findViewById(R.id.parallax_container); // View-ul principal
        RelativeLayout secondaryView = view.findViewById(R.id.rl_profile_menu);     // View-ul secundar

        if (scrollView != null && containerView != null && secondaryView != null) {
            // Creează instanța Universal Elastic Parallax
            elasticParallax = new UniversalElasticParallax(scrollView, containerView, secondaryView)
                .setElasticFactor(1.0f)                    // Factor de smoothness
                .setPrimaryStretchLimits(2f, 0.0f)       // Primary: Max 150%, Min 50%
                .setSecondaryStretchLimits(2f, 0.0f)    // Secondary: Max 133%, Min 50%
                .setAnimationDurations(1000, 980)           // Durată animații
                .setDecelerationFactors(3.0f, 2.0f);       // Factori de încetinire progresivă
        }
    }
}