package com.vgfit.shefit.fragment.nutrition;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;
import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TableLayout;
import android.widget.TableRow;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.Fragment;
import androidx.transition.TransitionInflater;

import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.assist.FailReason;
import com.nostra13.universalimageloader.core.listener.ImageLoadingListener;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.FragmentNutritionRecipeBinding;
import com.vgfit.shefit.realm.Meal;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.screen.ImageUtils;

import me.everything.android.ui.overscroll.OverScrollDecoratorHelper;

public class NutritionRecipeFr extends Fragment {
    private FragmentNutritionRecipeBinding binding;
    private static final String EXTRA_TRANSITION_NAME_IMAGEVIEW = "transition_image_name_meal";
    private static final String EXTRA_TRANSITION_SHORT_DESCRIPTION = "EXTRA_TRANSITION_SHORT_DESCRIPTION";
    private static final String EXTRA_TRANSITION_CALORIES = "EXTRA_TRANSITION_SHORT_DESCRIPTION";
    private static final String EXTRA_TRANSITION_TIME = "EXTRA_TRANSITION_SHORT_DESCRIPTION";
    private static final String EXTRA_TRANSITION_CONTAINER_MEAL = "EXTRA_TRANSITION_CONTAINER_MEAL";
    private static final String EXTRA_TRANSITION_CONTAINER_MEAL_2 = "EXTRA_TRANSITION_CONTAINER_MEAL_2";
    Meal item;
    View view;
    Context context;
    String[] ingredients;

    public static NutritionRecipeFr newInstance(Meal itemNutrition, String imageView, String shortDesc, String calories, String time, String containerMeal, String containerMeal2) {

        Bundle args = new Bundle();

        NutritionRecipeFr fragment = new NutritionRecipeFr();
        args.putParcelable("itemObject", itemNutrition);
        args.putString(EXTRA_TRANSITION_NAME_IMAGEVIEW, imageView);
        args.putString(EXTRA_TRANSITION_SHORT_DESCRIPTION, shortDesc);
        args.putString(EXTRA_TRANSITION_CALORIES, calories);
        args.putString(EXTRA_TRANSITION_TIME, time);
        args.putString(EXTRA_TRANSITION_CONTAINER_MEAL, containerMeal);
        args.putString(EXTRA_TRANSITION_CONTAINER_MEAL_2, containerMeal2);
//        args.putString();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        context = getContext();
        Bundle arg = getArguments();

        assert arg != null;
        if (arg.getParcelable("itemObject") != null)
            item = arg.getParcelable("itemObject");

        if (item != null) {
            String ingredientsStr = Translate.getValue(item.getIngredients());
            ingredients = ingredientsStr.split("\\n");
        }
        postponeEnterTransition();
        setSharedElementEnterTransition(TransitionInflater.from(getContext()).inflateTransition(R.transition.simple_fragment_transition));
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = FragmentNutritionRecipeBinding.inflate(inflater, container, false);
        view = binding.getRoot();
        if (getArguments() != null) {
            String transitionImage = getArguments().getString(EXTRA_TRANSITION_NAME_IMAGEVIEW);
            String shortDescription = getArguments().getString(EXTRA_TRANSITION_SHORT_DESCRIPTION);
            String caloriesMeal = getArguments().getString(EXTRA_TRANSITION_CALORIES);
            String timeMeal = getArguments().getString(EXTRA_TRANSITION_TIME);
            String containerMealTr = getArguments().getString(EXTRA_TRANSITION_CONTAINER_MEAL);
            String containerMeal2Tr = getArguments().getString(EXTRA_TRANSITION_CONTAINER_MEAL_2);

            binding.ivNutrMeal.setTransitionName(transitionImage);
            binding.tvNutrMealName.setTransitionName(shortDescription);
            binding.nutritionKcal.setTransitionName(caloriesMeal);
            binding.timeCook.setTransitionName(timeMeal);
            binding.containerInfoMeal.setTransitionName(containerMealTr);
            binding.view2.setTransitionName(containerMeal2Tr);
        }
        setStatusBar(binding.backContainer);
        binding.backContainer.setOnClickListener(v ->
        {
            setVibrate(v);
            if (getActivity() != null)
                getActivity().onBackPressed();
        });
        binding.tvNutrMealName.setText(Translate.getValue(item.getName()));
        binding.tvNutrMealSteps.setText(Translate.getValue(item.getSteps()));

        ScrollView scrollView = view.findViewById(R.id.scroll_view);
        OverScrollDecoratorHelper.setUpOverScroll(scrollView);
//        Glide.with(context)
//                .load(item.getVerticalImage())
//                .into(imageView);
        ImageLoader.getInstance().displayImage(item.getVerticalImage(), binding.ivNutrMeal, ImageUtils.getDefaultDisplayImageOptions(), new ImageLoadingListener() {
            @Override
            public void onLoadingStarted(String imageUri, View view) {
                ImageLoader.getInstance().displayImage(item.getVerticalImage(), binding.ivNutrMeal, ImageUtils.getDefaultDisplayImageOptions(), null);
                startPostponedEnterTransition();
            }

            @Override
            public void onLoadingFailed(String imageUri, View view, FailReason failReason) {
                startPostponedEnterTransition();
            }

            @Override
            public void onLoadingComplete(String imageUri, View view, Bitmap loadedImage) {

            }

            @Override
            public void onLoadingCancelled(String imageUri, View view) {

            }
        });
        init(view);
        setCaloriesNutrition();
        return view;
    }

    public void setCaloriesNutrition() {
        binding.nutritionKcal.setText(String.valueOf(item.getCalories()));
        binding.nutritionProteins.setText(String.valueOf(item.getProteins()));
        binding.nutritionFat.setText(String.valueOf(item.getFats()));
        binding.nutritionSugars.setText(String.valueOf(item.getCarbs()));
        String timeCookTxt = item.getTimeToCook() + " " + Translate.getValue("min");
        binding.timeCook.setText(timeCookTxt);
        binding.kcalUnit.setText(Translate.getValue("kcal").toLowerCase());
        binding.proteinsUnit.setText(Translate.getValue("protein").toLowerCase());
        binding.fatUnit.setText(Translate.getValue("fats").toLowerCase());
        binding.carbsUnit.setText(Translate.getValue("carbs").toLowerCase());
        binding.stepsTxt.setText(Translate.getValue("meal_steps"));
        binding.ingredientsTxt.setText(Translate.getValue("meal_ingr"));
    }

    public void init(View view) {
        TableLayout ll = view.findViewById(R.id.test_table);
        int count = 0;
        TypedValue typedValue = new TypedValue();
        if (getActivity() != null)
            getActivity().getTheme().resolveAttribute(R.attr.colorPrimaryDark, typedValue, true);
        if (ingredients.length != 0)
            for (String line : ingredients) {
                if (line.length() > 0) {
                    TableRow row = new TableRow(getContext());
                    TableRow.LayoutParams lp = new TableRow.LayoutParams(TableRow.LayoutParams.WRAP_CONTENT, TableRow.LayoutParams.WRAP_CONTENT);
                    row.setLayoutParams(lp);
                    TextView separatorUnicode = new TextView(getContext());
                    separatorUnicode.setText("⬤  ");

                    separatorUnicode.setTextColor(Color.parseColor("#f53903"));
                    Typeface typeface = ResourcesCompat.getFont(context, R.font.inter_semibold);
                    TableRow.LayoutParams params = new TableRow.LayoutParams(TableRow.LayoutParams.WRAP_CONTENT, TableRow.LayoutParams.WRAP_CONTENT);
                    params.setMargins(0, (int) dpToPx(context, 4), 0, (int) dpToPx(context, 4));
                    TextView textView = new TextView(getContext());
                    textView.setText(line);
                    textView.setMaxLines(10);
                    textView.setTextColor(Color.parseColor("#808080"));
                    textView.setLayoutParams(params);
                    textView.setTypeface(typeface);
                    separatorUnicode.setLayoutParams(params);

                    row.addView(separatorUnicode);
                    row.addView(textView);
                    ll.addView(row, count);
                    count++;
                }
            }
    }

    public float dpToPx(Context context, int dp) {
        Resources r = context.getResources();
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, r.getDisplayMetrics());
    }
}
