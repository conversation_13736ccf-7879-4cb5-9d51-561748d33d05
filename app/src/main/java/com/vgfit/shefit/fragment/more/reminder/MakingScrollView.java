package com.vgfit.shefit.fragment.more.reminder;

import android.app.Dialog;
import android.content.Context;
import androidx.appcompat.widget.SwitchCompat;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import com.vgfit.shefit.R;
import com.vgfit.shefit.fragment.more.service.SharedPreferencesData;

import java.util.ArrayList;

public class MakingScrollView {

    private Context context;
    private SharedPreferencesData sharedPreferencesData;
    private NotificationSpecialActions notificationSpecialActions;

    public MakingScrollView(Context context) {
        this.context = context;
    }

    public void setDaysScroll(TextView tvFrequency, TextView tvInactivityText, String FREQUENCY_KEY) {
        sharedPreferencesData = new SharedPreferencesData(context);

        ArrayList<String> listDays = new ArrayList<>();
        int positionDay = 0; //default position of scrolling

        final Dialog dialog = new Dialog(context);
        dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        dialog.setContentView(R.layout.dialog_frequency);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);

        final ScrollViewCustomized scrollCustomDays = dialog.findViewById(R.id.sv_c_days);
        scrollCustomDays.setOffset(1);

        for (int i = 0; i < 15; i++) {
            listDays.add("" + (i + 1));
            if (listDays.get(i).equals(tvFrequency.getText().toString())) positionDay = i;
        }
        scrollCustomDays.setItems(listDays);
        scrollCustomDays.setSelection(positionDay);

        Button btnDone = dialog.findViewById(R.id.ok_exit);
        btnDone.setOnClickListener(arg0 -> {
            dialog.dismiss();
//            save_my_days(scrollCustomDays.getSelectedItem(), valueToSave);
//            if (isChecked_frequency) {
//                alarm_frequency.CancelAlarm(context);
//                set_alarm_frequency();
//            }
            sharedPreferencesData.saveDataAsKeyValueString(FREQUENCY_KEY, scrollCustomDays.getSelectedItem());
            sharedPreferencesData.setNewDataToTextView(tvFrequency, FREQUENCY_KEY);
            sharedPreferencesData.setNewDataToTextView(tvInactivityText, FREQUENCY_KEY);
        });

        Button btnCancel = dialog.findViewById(R.id.cancel_exit);
        btnCancel.setOnClickListener(v -> dialog.dismiss());

        dialog.show();
    }

    public void setTimeScroll(SwitchCompat switchCompat, TextView textView, String key, boolean isDaily) {
        sharedPreferencesData = new SharedPreferencesData(context);
        notificationSpecialActions = new NotificationSpecialActions(context, sharedPreferencesData);

        ArrayList<String> listHours = new ArrayList<>();
        ArrayList<String> listMinutes = new ArrayList<>();
        String hours;
        String minute;
        int positionHours = 0, positionMinutes = 0; //default values when you open the scroll view
        String existingDate = textView.getText().toString();

        final Dialog d = new Dialog(context);
        d.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        d.setContentView(R.layout.dialog_reminder);
        d.setCanceledOnTouchOutside(false);
        d.setCancelable(false);

        final ScrollViewCustomized scrollCustomHours = d.findViewById(R.id.sv_c_hours);
        final ScrollViewCustomized scrollCustomMinutes = d.findViewById(R.id.sv_c_minutes);
        scrollCustomHours.setOffset(1);
        scrollCustomMinutes.setOffset(1);
        int pos = existingDate.indexOf(":");
        hours = existingDate.substring(0, pos);
        minute = existingDate.substring(pos + 1, existingDate.length());

        for (int i = 0; i < 24; i++) {
            if (i < 10) listHours.add("0" + i);
            else listHours.add("" + i);
            if (listHours.get(i).equals(hours)) positionHours = i;
        }
        scrollCustomHours.setItems(listHours);

        for (int i = 0; i < 60; i++) {
            if (i < 10) listMinutes.add("0" + i);
            else listMinutes.add("" + i);
            if (listMinutes.get(i).equals(minute)) positionMinutes = i;
        }
        scrollCustomMinutes.setItems(listMinutes);

        scrollCustomHours.setSelection(positionHours);
        scrollCustomMinutes.setSelection(positionMinutes);

        Button btnDone = d.findViewById(R.id.ok_exit);
        btnDone.setOnClickListener(arg0 -> {
            d.dismiss();

            if (switchCompat.isChecked()) {
//                Toast.makeText(context, "Is checked " + context.getResources().getResourceEntryName(switchCompat.getId()), Toast.LENGTH_SHORT).show();
                if (isDaily)
                    notificationSpecialActions.dailyReminderSpecialActions(key);
                else notificationSpecialActions.inactivitySpecialActions(key);
            } else {
                Toast.makeText(context, "Isn't checked " + context.getResources().getResourceEntryName(switchCompat.getId()), Toast.LENGTH_SHORT).show();
            }
            sharedPreferencesData.saveDataAsKeyValueString(key, scrollCustomHours.getSelectedItem() + ":" + scrollCustomMinutes.getSelectedItem());
            sharedPreferencesData.setNewDataToTextView(textView, key);
        });

        Button btnCancel = d.findViewById(R.id.cancel_exit);
        btnCancel.setOnClickListener(v -> d.dismiss());

        d.show();
    }
}