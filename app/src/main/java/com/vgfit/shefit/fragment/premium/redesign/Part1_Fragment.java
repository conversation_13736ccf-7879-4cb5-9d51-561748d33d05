package com.vgfit.shefit.fragment.premium.redesign;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.Outline;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.StartScreen;
import com.vgfit.shefit.billing.BillingProcessor;
import com.vgfit.shefit.databinding.Part1SubscribeBinding;
import com.vgfit.shefit.fragment.premium.BillingAndRemoteConfig;
import com.vgfit.shefit.fragment.premium.OnSingleClickListener;
import com.vgfit.shefit.fragment.premium.redesign.model.UpdatePart1;
import com.vgfit.shefit.fragment.premium.redesign.model.UpdatePart7;
import com.vgfit.shefit.fragment.premium.redesign.model.UpdatePart8;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.RCUtils;
import com.vgfit.shefit.util.Translate;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import eightbitlab.com.blurview.BlurAlgorithm;
import eightbitlab.com.blurview.RenderEffectBlur;
import eightbitlab.com.blurview.RenderScriptBlur;

public class Part1_Fragment extends Fragment {
    private Part1SubscribeBinding binding;
    private static final String KEY_CHECK_KMINIMUM_FEATURE = "key_checkKMinimumFeature";
    private String monthTrial = "";
    private String yearTrial = "";
    private String trialConstant = " free trial then\n";
    private boolean isChecked = false;
    private final String key_isChecked = "key_isChecked_part1";
    private boolean existTrialMonth = false;
    private boolean existTrialYear = false;
    private boolean checkKMinimumFeature;
    private BillingProcessor bp;
    private boolean readyToPurchase;
    private boolean isMainActivity;
    private int closeTimeButton = 1_000;
    private int typeSubscribe = 0;// month and yearly =0 (default)

    public static Part1_Fragment newInstance(boolean isChecked, boolean checkKMinimumFeature) {

        Bundle args = new Bundle();
        Part1_Fragment fragment = new Part1_Fragment();
        args.putBoolean(fragment.key_isChecked, isChecked);
        args.putBoolean(KEY_CHECK_KMINIMUM_FEATURE, checkKMinimumFeature);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arg = getArguments();
        initActivity();
        if (arg != null) {
            isChecked = arg.getBoolean(key_isChecked);
            checkKMinimumFeature = arg.getBoolean(KEY_CHECK_KMINIMUM_FEATURE, false);
        }
        trialConstant = " " + Translate.getValue("free_trial").toLowerCase() + " " + Translate.getValue("then") + " ";
        RCUtils rcUtils = new RCUtils(getContext());
        closeTimeButton = rcUtils.getShowCloseButtonAfter() * 1_000;
        typeSubscribe = rcUtils.getScrollingPaywall();
    }

    private void initActivity() {
        Activity activity = (Activity) getContext();
        if (activity instanceof BillingAndRemoteConfig) {
            if (getActivity() != null && ((BillingAndRemoteConfig) getActivity()).bp != null) {
                bp = ((BillingAndRemoteConfig) getActivity()).bp;
                readyToPurchase = BillingAndRemoteConfig.readyToPurchase;
            }
        } else {
            if (getActivity() != null && ((MainActivity) getActivity()).bp != null) {
                bp = ((MainActivity) getActivity()).bp;
                readyToPurchase = MainActivity.readyToPurchase;
                isMainActivity = true;
            }
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = Part1SubscribeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        binding.switchSubscribe.setOnCheckedChangeListener((buttonView, isChecked) -> {
            setVibrate(buttonView);
            Log.d("TTTTTT", "Activate part1");
            this.isChecked = isChecked;
            binding.infoPeriod1.setTextColor(isChecked ? Color.WHITE : Color.BLACK);
            binding.pricePeriod1.setTextColor(isChecked ? Color.WHITE : Color.BLACK);

            binding.infoPeriod2.setTextColor(!isChecked ? Color.WHITE : Color.BLACK);
            binding.pricePeriod2.setTextColor(!isChecked ? Color.WHITE : Color.BLACK);

            binding.startSubscribe.setText(Constant.getTextVariantStart(typeSubscribe));
            if (existTrialMonth && isChecked) {
                binding.startSubscribe.setText(Translate.getValue("start_free_trial"));
            }
            if (existTrialYear && !isChecked) {
                binding.startSubscribe.setText(Translate.getValue("start_free_trial"));
            }
            EventBus.getDefault().post(new UpdatePart7(isChecked ? existTrialMonth : existTrialYear));
            EventBus.getDefault().post(new UpdatePart8(isChecked));

        });
        binding.startSubscribe.setOnClickListener(new OnSingleClickListener() {
            @Override
            public void onSingleClick(View v) {
                setVibrate(v);
                if (readyToPurchase && getActivity() != null && bp != null) {
                    if (isChecked)
                        bp.subscribe(getActivity(), Constant.getFirstItemPurchase(typeSubscribe));
                    else
                        bp.subscribe(getActivity(), Constant.getSecondItemPurchase(typeSubscribe));
                }
            }
        });
        binding.back.setOnClickListener(v -> {
            setVibrate(v);
            closeOffer();
        });
        defaultStatus();
        setPrice();
        binding.relativeLayout.setClipToOutline(true);
        binding.relativeLayout.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                binding.relativeLayout.getBackground().getOutline(outline);
                outline.setAlpha(1f);
            }
        });
        setupBlurView();
    }

    private void setupBlurView() {
        final float radius = 5f;
        //set background, if your root layout doesn't have one
        if (getActivity() != null) {
            Drawable windowBackground = getActivity().getWindow().getDecorView().getBackground();
            BlurAlgorithm algorithm = getBlurAlgorithm();
            binding.relativeLayout.setupWith(binding.root, algorithm)
                    .setFrameClearDrawable(windowBackground)
                    .setBlurRadius(radius);
        }
    }

    @NonNull
    private BlurAlgorithm getBlurAlgorithm() {

        BlurAlgorithm algorithm;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            algorithm = new RenderEffectBlur();
        } else {
            algorithm = new RenderScriptBlur(getContext());
        }
        return algorithm;
    }

    private void closeOffer() {
        try {
            if (getActivity() != null) {
                if (isMainActivity)
                    getActivity().onBackPressed();
                else
                    ((StartScreen) getActivity()).closeOfferFromOnBoarding();

            }
        } catch (Exception ignored) {
        }

    }

    private void defaultStatus() {
        if (getActivity() != null)
            getActivity().runOnUiThread(() -> binding.switchSubscribe.setChecked(isChecked));
        binding.infoPeriod1.setTextColor(isChecked ? Color.WHITE : Color.BLACK);
        binding.pricePeriod1.setTextColor(isChecked ? Color.WHITE : Color.BLACK);

        binding.infoPeriod2.setTextColor(!isChecked ? Color.WHITE : Color.BLACK);
        binding.pricePeriod2.setTextColor(!isChecked ? Color.WHITE : Color.BLACK);
        binding.needInfo.setText(Translate.getValue("you_need_1_app_to_achieve_your"));
        binding.dreamBody.setText(Translate.getValueLine("dream_body", Arrays.asList(1), false));
        String unl = Translate.getValue("get_unlimited_access_to") + " ";
        binding.getUnlimited.setText(unl);
        String trainingTxt = Translate.getValue("80+_trainings") + " ";
        binding.eightTeen.setText(trainingTxt);
        binding.infoPeriod1.setText(Constant.getSecondPeriodTitleText(typeSubscribe));
        binding.infoPeriod2.setText(Constant.getFirstPeriodTitleText(typeSubscribe));
        binding.startSubscribe.setText(Constant.getTextVariantStart(typeSubscribe));
        if (existTrialMonth && isChecked) {
            binding.startSubscribe.setText(Translate.getValue("start_free_trial"));
        }
        if (existTrialYear && !isChecked) {
            binding.startSubscribe.setText(Translate.getValue("start_free_trial"));
        }
    }

    private void setPrice() {
        try {
            if (getActivity() != null) {
                if (bp != null) {
                    bp.getSubscriptionListingDetails(Constant.getFirstItemPurchase(typeSubscribe), purchaseInfoMonth -> {
                        try {

                            monthTrial = getFormattedTrial(purchaseInfoMonth.subscriptionFreeTrialPeriod);
                            if (!monthTrial.isEmpty()) {
                                existTrialMonth = true;

                                if (monthTrial.contains("7")) {
                                    monthTrial = "1 " + Translate.getValue("week");
                                } else
                                    monthTrial = monthTrial + " " + Translate.getValue("days");
                                monthTrial = monthTrial + trialConstant + purchaseInfoMonth.priceText + "/" + Constant.getFirstPeriodText(typeSubscribe);
                            } else
                                monthTrial = purchaseInfoMonth.priceText + "/" + Constant.getFirstPeriodText(typeSubscribe);

                            EventBus.getDefault().post(new UpdatePart7(existTrialMonth));
                            if (existTrialMonth && isChecked) {
                                String valueTxt = Translate.getValue("start_free_trial");
                                setTextThread(binding.startSubscribe, valueTxt);
                            }
                            setTextThread(binding.pricePeriod2, monthTrial);
                        } catch (Exception ignored) {
                        }
                    });
                    bp.getSubscriptionListingDetails(Constant.getSecondItemPurchase(typeSubscribe), purchaseInfoYear -> {
                        try {

                            yearTrial = getFormattedTrial(purchaseInfoYear.subscriptionFreeTrialPeriod);
                            if (!yearTrial.isEmpty()) {
                                existTrialYear = true;
                                if (yearTrial.contains("7")) {
                                    yearTrial = "1 " + Translate.getValue("week");
                                } else
                                    yearTrial = yearTrial + " " + Translate.getValue("days");
                                yearTrial = yearTrial + trialConstant + purchaseInfoYear.priceText + "/" + Constant.getSecondPeriodText(typeSubscribe);
                            } else
                                yearTrial = purchaseInfoYear.priceText + "/" + Constant.getSecondPeriodText(typeSubscribe);
                            EventBus.getDefault().post(new UpdatePart7(existTrialYear));
                            if (existTrialYear && !isChecked) {
                                String valueTxt = Translate.getValue("start_free_trial");
                                setTextThread(binding.startSubscribe, valueTxt);
                            }
                            setTextThread(binding.pricePeriod1, yearTrial);
                        } catch (Exception ignored) {
                        }
                    });
                }
            }
        } catch (
                Exception ignored) {
        }

    }


    private void isVisibleBack() {
        binding.back.setVisibility(View.INVISIBLE);
        Handler handler = new Handler();
        handler.postDelayed(() -> {
            try {
                if (binding.back != null) {
                    binding.back.setVisibility(View.VISIBLE);
                }
            } catch (Exception ignored) {
            }
        }, closeTimeButton);
    }

    private void setTextThread(final TextView text, final String value) {
        if (getActivity() != null)
            getActivity().runOnUiThread(() -> text.setText(value));
    }

    private String getFormattedTrial(String trialDefault) {
        String numb = extractOnlyNumber(trialDefault);
        int numbInt = -1;
        try {
            numbInt = Integer.parseInt(numb);
        } catch (Exception ignored) {
        }
        if (numbInt != -1 && trialDefault.contains("W")) {
            return String.valueOf(numbInt * 7);
        } else if (numbInt != -1 && trialDefault.contains("D"))
            return String.valueOf(numbInt);
        else return numb;
    }

    private String extractOnlyNumber(String text) {
        Pattern p = Pattern.compile("\\d+");
        Matcher m = p.matcher(text);
        if (m.find()) {
            return m.group();
        }
        return "";
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
//        view.setOnKeyListener((v, keyCode, event) -> {
//            // pass on to be processed as normal
//            return keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN; // pretend we've processed it
//        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        try {
            EventBus.getDefault().unregister(this);
        } catch (Exception ignored) {
        }
        if (!isMainActivity && getActivity() instanceof StartScreen) {
            ((StartScreen) getActivity()).offerFinished();
        }

    }

    @Override
    public void onStart() {
        super.onStart();
        try {
            EventBus.getDefault().register(this);
        } catch (Exception ignored) {
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageSecondOffer(UpdatePart1 event) {
        if (event != null) {
            isChecked = event.isChecked();
            defaultStatus();
        }

    }

}
