package com.vgfit.shefit.fragment.personal_plan.model;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

import com.vgfit.shefit.realm.CoverDayPlan;
import com.vgfit.shefit.realm.CoverVideoPlan;
import com.vgfit.shefit.realm.DayPlan;
import com.vgfit.shefit.realm.PhotoMeal;

public class OneDayData implements Parcelable {
    private DayOfWeek dayOfWeek;
    private DayPlan dayPlan;
    private PhotoMeal photoMeal;
    private CoverVideoPlan coverVideoPlan;
    private CoverDayPlan coverDayPlan;
    private boolean selectedDay;
    private boolean showDay;
    private String idWorkout;
    private long timeLong;

    public OneDayData() {
    }

    protected OneDayData(Parcel in) {
        dayPlan = in.readParcelable(DayPlan.class.getClassLoader());
        selectedDay = in.readByte() != 0;
        showDay = in.readByte() != 0;
        idWorkout = in.readString();
        timeLong = in.readLong();
    }

    public static final Creator<OneDayData> CREATOR = new Creator<OneDayData>() {
        @Override
        public OneDayData createFromParcel(Parcel in) {
            return new OneDayData(in);
        }

        @Override
        public OneDayData[] newArray(int size) {
            return new OneDayData[size];
        }
    };

    public DayOfWeek getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(DayOfWeek dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public DayPlan getDayPlan() {
        return dayPlan;
    }

    public void setDayPlan(DayPlan dayPlan) {
        this.dayPlan = dayPlan;
    }

    public PhotoMeal getPhotoMeal() {
        return photoMeal;
    }

    public void setPhotoMeal(PhotoMeal photoMeal) {
        this.photoMeal = photoMeal;
    }

    public CoverVideoPlan getCoverVideoPlan() {
        return coverVideoPlan;
    }

    public void setCoverVideoPlan(CoverVideoPlan coverVideoPlan) {
        this.coverVideoPlan = coverVideoPlan;
    }

    public CoverDayPlan getCoverDayPlan() {
        return coverDayPlan;
    }

    public void setCoverDayPlan(CoverDayPlan coverDayPlan) {
        this.coverDayPlan = coverDayPlan;
    }

    public boolean isSelectedDay() {
        return selectedDay;
    }

    public void setSelectedDay(boolean selectedDay) {
        this.selectedDay = selectedDay;
    }

    public boolean isShowDay() {
        return showDay;
    }

    public void setShowDay(boolean showDay) {
        this.showDay = showDay;
    }

    public String getIdWorkout() {
        return idWorkout;
    }

    public void setIdWorkout(String idWorkout) {
        this.idWorkout = idWorkout;
    }

    public long getTimeLong() {
        return timeLong;
    }

    public void setTimeLong(long timeLong) {
        this.timeLong = timeLong;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        dest.writeParcelable(dayPlan, flags);
        dest.writeByte((byte) (selectedDay ? 1 : 0));
        dest.writeByte((byte) (showDay ? 1 : 0));
        dest.writeString(idWorkout);
        dest.writeLong(timeLong);
    }
}
