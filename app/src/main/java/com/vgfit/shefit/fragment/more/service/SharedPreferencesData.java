package com.vgfit.shefit.fragment.more.service;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import androidx.appcompat.widget.SwitchCompat;
import android.text.TextUtils;
import android.widget.TextView;

import java.util.ArrayList;
import java.util.Arrays;

public class SharedPreferencesData {

    private static final String SCALE_DATA_KEY = "scale_data_key";
    private final String FREQUENCY_KEY = "frequency_key";
    private final String DAILY_REMINDER_KEY = "reminder_key";
    private final String INACTIVITY_KEY = "inactivity_key";
    private final String DAILY_REMINDER_SWITCH_KEY = "daily_reminder_switch_key";
    private final String INACTIVITY_SWITCH_KEY = "inactivity_switch_key";
    private final String DATE_KEY = "date_key";
    private final String AFTER_UPDATE_KEY = "after_update_key";

    private SharedPreferences sharedPreferences;
    private SharedPreferences.Editor editor;

    private Context context;

    public SharedPreferencesData(Context context) {
        this.context = context;
    }

    public String getFREQUENCY_KEY() {
        return FREQUENCY_KEY;
    }

    public String getDAILY_REMINDER_KEY() {
        return DAILY_REMINDER_KEY;
    }

    public String getDAILY_REMINDER_SWITCH_KEY() {
        return DAILY_REMINDER_SWITCH_KEY;
    }

    public String getINACTIVITY_SWITCH_KEY() {
        return INACTIVITY_SWITCH_KEY;
    }

    public String getINACTIVITY_KEY() {
        return INACTIVITY_KEY;
    }

    private String getDATE_KEY() {
        return DATE_KEY;
    }

    private void initializeSharedPreferences() {
        if (sharedPreferences == null) {
            sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context);// NOSONAR
            editor = sharedPreferences.edit();
        }
    }

    public void fillDataWithSavedInformation(TextView tvDailyReminder, TextView tvInactivityText, TextView tvInactivity, TextView tvFrequency, SwitchCompat scInactivity, SwitchCompat scDailyReminder) {
        initializeSharedPreferences();
        setNewDataToTextView(tvDailyReminder, DAILY_REMINDER_KEY);
        setNewDataToTextView(tvInactivity, INACTIVITY_KEY);
        setNewDataToTextView(tvInactivityText, FREQUENCY_KEY);
        setNewDataToTextView(tvFrequency, FREQUENCY_KEY);
        setNewDataToSwitchCompat(scInactivity, INACTIVITY_SWITCH_KEY);
        setNewDataToSwitchCompat(scDailyReminder, DAILY_REMINDER_SWITCH_KEY);
    }

    public void setNewDataToTextView(TextView textView, String key) {
        initializeSharedPreferences();
        String text = sharedPreferences.getString(key, textView.getText().toString());
        textView.setText(text);
    }

    public void saveRealmAsUpdated() {
        initializeSharedPreferences();
        editor.putBoolean(AFTER_UPDATE_KEY, true);
        editor.commit();
    }

    public boolean getUpdatedRealm() {
        initializeSharedPreferences();
        return sharedPreferences.getBoolean(AFTER_UPDATE_KEY, false);
    }

    private void setNewDataToSwitchCompat(SwitchCompat switchCompat, String key) {
        initializeSharedPreferences();
        boolean value = sharedPreferences.getBoolean(key, switchCompat.isChecked());
        switchCompat.setChecked(value);
    }

    public void saveDataAsKeyValueString(String key, String value) {
        initializeSharedPreferences();
        editor.putString(key, value);
        editor.commit();
    }

    public void saveDataAsKeyValueBoolean(String key, boolean value) {
        initializeSharedPreferences();
        editor.putBoolean(key, value);
        editor.commit();
    }

    public int getHour(String key) {
        int hours;
        initializeSharedPreferences();
        String formattedDate = sharedPreferences.getString(key, "7:30");
        int pos = formattedDate.indexOf(":");
        hours = Integer.parseInt(formattedDate.substring(0, pos));
        return hours;
    }

    public int getMinute(String key) {
        int minute;
        initializeSharedPreferences();
        String formattedDate = sharedPreferences.getString(key, "7:30");
        int pos = formattedDate.indexOf(":");
        minute = Integer.parseInt(formattedDate.substring(pos + 1));
        return minute;
    }

    public void setCurrentDate() {
        initializeSharedPreferences();
        Long date = System.currentTimeMillis();
        editor.putLong(getDATE_KEY(), date);
        editor.commit();
    }

    public Long getLastDate() {
        initializeSharedPreferences();
        return sharedPreferences.getLong(getDATE_KEY(), 0);
    }

    public int getFrequency() {
        initializeSharedPreferences();
        Long def = System.currentTimeMillis();
        return Integer.parseInt(sharedPreferences.getString(getFREQUENCY_KEY(), "" + def));
    }

    public void storeScaleData(ArrayList<String> list) {
        initializeSharedPreferences();
        String countriesString = TextUtils.join(";", list);
        editor.putString(SCALE_DATA_KEY, countriesString);
        editor.commit();
    }

    public ArrayList<String> retrieveScaleData(int size) {
        initializeSharedPreferences();
        StringBuilder defaultValue = new StringBuilder();
        for (int i = 0; i < size; i++) {
            defaultValue.append("0;");
        }

        final String indexesString = sharedPreferences.getString(SCALE_DATA_KEY, String.valueOf(defaultValue));
        ArrayList<String> indexesList = new ArrayList<>();
        if (indexesString != null && !indexesString.isEmpty()) {
            indexesList = new ArrayList<>(Arrays.asList(indexesString.split(";")));
        }

        return indexesList;
    }
}