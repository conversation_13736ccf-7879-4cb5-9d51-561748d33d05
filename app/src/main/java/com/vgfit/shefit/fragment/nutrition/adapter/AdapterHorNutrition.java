package com.vgfit.shefit.fragment.nutrition.adapter;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.vgfit.shefit.databinding.ItemHorizontalImageBinding;
import com.vgfit.shefit.fragment.nutrition.callbacks.NutrItemClick;
import com.vgfit.shefit.realm.Meal;
import com.vgfit.shefit.realm.MealByDay;
import com.vgfit.shefit.util.Translate;

import java.util.ArrayList;
import java.util.Collections;

import io.realm.RealmList;

public class AdapterHorNutrition extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final ArrayList<MealByDay> listItemNutrition;
    private final Context context;
    private final NutrItemClick listener;
    private final int layoutWidth;
    private final int layoutHeight;

    public AdapterHorNutrition(RealmList<MealByDay> listIdDay, Context context, NutrItemClick listener, int layoutWidth, int layoutHeight) {
        listItemNutrition = new ArrayList<>();
        this.listItemNutrition.addAll(listIdDay);
        Collections.sort(listItemNutrition, (o1, o2) -> o1.getOrder() - o2.getOrder());
        this.context = context;
        this.listener = listener;
        this.layoutWidth = layoutWidth;
        this.layoutHeight = layoutHeight;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ItemHorizontalImageBinding binding = ItemHorizontalImageBinding.inflate(inflater, parent, false);

        return new ItemViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder rawHolder, final int position) {
        int counter = position + 1;
        ItemViewHolder holder = (ItemViewHolder) rawHolder;
        Meal meal = listItemNutrition.get(position).getMeal();
        holder.linearLayout.getLayoutParams().width = layoutWidth;
        holder.linearLayout.getLayoutParams().height = layoutHeight;

        holder.imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        holder.linearLayout.requestLayout();
        String mealName = Translate.getValue("meal") + " " + counter;
        Log.e("TestMeal", "meal name-->" + mealName);
        holder.tvMealsCounter.setText(mealName);

        RequestOptions requestOptions = new RequestOptions();
        requestOptions = requestOptions.transforms(new CenterCrop(), new RoundedCorners(16));

        Glide.with(context)
                .load(meal.getImage())
                .apply(requestOptions)
                .into(((ItemViewHolder) rawHolder).imageView);

        holder.itemView.setTag(position);
        holder.textView.setText(Translate.getValue(listItemNutrition.get(position).getMeal().getName()));
        holder.imageView.setOnClickListener(v -> listener.onItemClick(listItemNutrition.get(position).getMeal()));

    }

    @Override
    public int getItemCount() {
        return listItemNutrition.size();
    }

    public static class ItemViewHolder extends RecyclerView.ViewHolder {

        ImageView imageView;
        TextView textView;
        RelativeLayout linearLayout;
        TextView tvMealsCounter;

        public ItemViewHolder(ItemHorizontalImageBinding binding) {
            super(binding.getRoot());
            imageView = binding.ivNutritionImage;
            textView = binding.tvNameImageNutrition;
            linearLayout = binding.rlNutritionMealItem;
            tvMealsCounter = binding.tvMealsCounter;
        }
    }
}