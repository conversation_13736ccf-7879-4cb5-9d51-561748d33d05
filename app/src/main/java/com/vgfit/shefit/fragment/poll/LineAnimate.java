package com.vgfit.shefit.fragment.poll;

import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathEffect;
import android.graphics.PathMeasure;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;


public class LineAnimate extends View {
    Path path;
    Paint paint;
    Paint paintCircle;
    Paint paintCircleStroke;
    float length;
    int radiusCircle;

    private int xCircle1;
    private int yCircle1;
    private int xCircle2;
    private int yCircle2;

    public LineAnimate(Context context) {
        super(context);
    }

    public LineAnimate(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public LineAnimate(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void init(int x1, int y1, int x2, int y2, int curveRadius, int starColor, int endColor, int lineWidth) {
        this.xCircle1 = x1;
        this.yCircle1 = y1;
        this.xCircle2 = x2;
        this.yCircle2 = y2;
        this.radiusCircle = dpToPx(getContext(), 6);
        Shader shader = new LinearGradient(x1, y1, x2, y2, starColor, endColor, Shader.TileMode.CLAMP);
        paint = new Paint();
        paint.setAntiAlias(true);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(lineWidth);
//        paint.setColor(color);
        paint.setShader(shader);

        path = new Path();
        int midX = x1 + ((x2 - x1) / 2);
        int midY = y1 + ((y2 - y1) / 2);
        float xDiff = (float) (midX - x1);
        float yDiff = (float) (midY - y1);
        double angle = (Math.atan2(yDiff, xDiff) * (180 / Math.PI)) + 90;

        double angleRadians = Math.toRadians(angle);
        float pointX = (float) (midX + curveRadius * Math.cos(angleRadians));
        float pointY = (float) (midY + curveRadius * Math.sin(angleRadians));
        path.moveTo(x1, y1);
        path.cubicTo(x1, y1, pointX, pointY, x2, y2);
//        canvas.drawPath(path, paint);

        // Measure the path

        PathMeasure measure = new PathMeasure(path, false);
        length = measure.getLength();

        float[] intervals = new float[]{length, length};
        paintCircle = new Paint();
        paintCircle.setColor(Color.WHITE);
        paintCircle.setStyle(Paint.Style.FILL);
        paintCircleStroke = new Paint(paint);


        ObjectAnimator animator = ObjectAnimator.ofFloat(LineAnimate.this, "phase", 1.0f, 0.0f);
        animator.setDuration(1000);
        animator.start();
    }

    //is called by animtor object
    public void setPhase(float phase) {
        paint.setPathEffect(createPathEffect(length, phase, 0.0f));
        invalidate();//will calll onDraw
    }

    private static PathEffect createPathEffect(float pathLength, float phase, float offset) {
        return new DashPathEffect(new float[]{pathLength, pathLength},
                Math.max(phase * pathLength, offset));
    }

    @Override
    public void onDraw(Canvas c) {
        super.onDraw(c);
        try {
            c.drawPath(path, paint);
            c.drawCircle(xCircle1, yCircle1, radiusCircle, paintCircle);
            c.drawCircle(xCircle2, yCircle2, radiusCircle, paintCircle);
            c.drawCircle(xCircle1, yCircle1, radiusCircle, paint);
            c.drawCircle(xCircle2, yCircle2, radiusCircle, paint);
        } catch (Exception ignored) {
        }
    }
}