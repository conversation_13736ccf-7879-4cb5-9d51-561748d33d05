package com.vgfit.shefit.fragment.userprofile.progress.adapter;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.ItemAchievementBinding;
import com.vgfit.shefit.realm.Achievement;
import com.vgfit.shefit.util.AchievementDialogHelper;
import com.vgfit.shefit.util.Translate;


import java.util.List;

import eightbitlab.com.blurview.BlurAlgorithm;
import eightbitlab.com.blurview.RenderEffectBlur;
import eightbitlab.com.blurview.RenderScriptBlur;

public class AchievementAdapter extends RecyclerView.Adapter<AchievementAdapter.ViewHolder> {

    private final Context context;
    private List<Achievement> achievementList;
    private  Fragment fragment;

    public AchievementAdapter(Context context, List<Achievement> achievementList, Fragment fragment) {
        this.context = context;
        this.achievementList = achievementList;
        this.fragment = fragment;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemAchievementBinding binding = ItemAchievementBinding.inflate(
                LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Achievement achievement = achievementList.get(position);

        // Get translated badge name and description
        String badgeName = Translate.getValue(achievement.getBadgeNameKey());
        String description = Translate.getValue(achievement.getDescriptionKey());

        // Fallback to key if translation not found
        if (badgeName.isEmpty()) {
            badgeName = achievement.getBadgeNameKey();
        }
        if (description.isEmpty()) {
            description = achievement.getDescriptionKey();
        }

        // Set achievement data
        holder.binding.achievementTitle.setText(badgeName);
        holder.binding.achievementDescription.setText(description);

        // Set achievement badge image with blur effect for locked achievements
        setAchievementBadge(holder, achievement.getId());

        holder.binding.blurringView.setVisibility(achievement.isUnlocked() ? GONE : VISIBLE);
        holder.itemView.setOnClickListener(v -> {
            if(achievement.isUnlocked()){
                AchievementDialogHelper.showAchievementDialog(context, fragment, achievement,
                        new AchievementDialogHelper.OnAchievementDialogListener() {
                            @Override
                            public void onAchievementDialogClosed() {
                                // Dialog closed, celebration layout remains visible
                            }

                            @Override
                            public void onShowCelebrationLayout(Achievement achievement) {
                                // Not used in this context
                            }
                        });
            }
        });

        final float radius = 5f;
        BlurAlgorithm algorithm = getBlurAlgorithm(context);
        Drawable windowBackground = holder.binding.getRoot().getBackground();
        holder.binding.blurringView.setupWith(holder.binding.achievementCard, algorithm)
                .setFrameClearDrawable(windowBackground)
                .setBlurRadius(radius);
    }

    private void setAchievementBadge(ViewHolder holder, int achievementId) {
        // Build the asset path for the badge
        String badgeFileName = "badge_" + achievementId + ".webp";
        String assetPath = "file:///android_asset/achievements/" + badgeFileName;

        // Load image with Glide (blur effect will be applied via ColorFilter)
        Glide.with(context)
                .load(assetPath)
                .placeholder(R.drawable.ic_fire)
                .error(android.R.drawable.star_on)
                .fitCenter()
                .into(holder.binding.achievementIcon);

        // Set transparent background to show the badge image properly
    }


    @Override
    public int getItemCount() {
        return achievementList.size();
    }

    public void updateData(List<Achievement> newAchievementList) {
        this.achievementList = newAchievementList;
        notifyDataSetChanged();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemAchievementBinding binding;

        public ViewHolder(ItemAchievementBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    @NonNull
    private BlurAlgorithm getBlurAlgorithm(Context context) {

        BlurAlgorithm algorithm;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            algorithm = new RenderEffectBlur();
        } else {
            algorithm = new RenderScriptBlur(context);
        }
        return algorithm;
    }
}
