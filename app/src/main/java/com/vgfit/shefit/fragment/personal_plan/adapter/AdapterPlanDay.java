package com.vgfit.shefit.fragment.personal_plan.adapter;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.annotation.SuppressLint;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.ContainerPlanBinding;
import com.vgfit.shefit.fragment.personal_plan.callbacks.CurrentPlan;
import com.vgfit.shefit.fragment.personal_plan.callbacks.PlanClicked;
import com.vgfit.shefit.fragment.personal_plan.model.OneDayData;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.text.AutoRTextView;
import com.vgfit.shefit.util.text.TextTwoRow;

import java.util.ArrayList;
import java.util.List;


public class AdapterPlanDay extends RecyclerView.Adapter<AdapterPlanDay.PlanViewHolder> {
    private final List<Integer> listDayBackground;
    private final List<OneDayData> listWeekPlan;
    private final CurrentPlan currentPlan;
    private final PlanClicked planClicked;

    public AdapterPlanDay(List<OneDayData> listWeekPlan, CurrentPlan currentPlan, PlanClicked planClicked) {
        this.listWeekPlan = listWeekPlan;
        this.currentPlan = currentPlan;
        this.planClicked = planClicked;
        listDayBackground = new ArrayList<>();
        fillListBackground();
    }

    private void fillListBackground() {
        listDayBackground.add(R.drawable.main_background_day1);
        listDayBackground.add(R.drawable.main_background_day2);
        listDayBackground.add(R.drawable.main_background_day3);
        listDayBackground.add(R.drawable.main_background_day4);
        listDayBackground.add(R.drawable.main_background_day5);
        listDayBackground.add(R.drawable.main_background_day6);
        listDayBackground.add(R.drawable.main_background_day7);
        listDayBackground.add(R.drawable.main_background_day8);
        listDayBackground.add(R.drawable.main_background_day9);
        listDayBackground.add(R.drawable.main_background_day10);
        listDayBackground.add(R.drawable.main_background_day11);
        listDayBackground.add(R.drawable.main_background_day12);
        listDayBackground.add(R.drawable.main_background_day13);
        listDayBackground.add(R.drawable.main_background_day14);
        listDayBackground.add(R.drawable.main_background_day15);
        listDayBackground.add(R.drawable.main_background_day16);
        listDayBackground.add(R.drawable.main_background_day17);
        listDayBackground.add(R.drawable.main_background_day18);
        listDayBackground.add(R.drawable.main_background_day19);
        listDayBackground.add(R.drawable.main_background_day20);
        listDayBackground.add(R.drawable.main_background_day21);
        listDayBackground.add(R.drawable.main_background_day22);
        listDayBackground.add(R.drawable.main_background_day23);
        listDayBackground.add(R.drawable.main_background_day24);
        listDayBackground.add(R.drawable.main_background_day25);
        listDayBackground.add(R.drawable.main_background_day26);
        listDayBackground.add(R.drawable.main_background_day27);
        listDayBackground.add(R.drawable.main_background_day28);
        listDayBackground.add(R.drawable.main_background_day29);
        listDayBackground.add(R.drawable.main_background_day30);
        listDayBackground.add(R.drawable.main_background_day31);
    }

    @NonNull
    @Override
    public PlanViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ContainerPlanBinding binding = ContainerPlanBinding.inflate(inflater, parent, false);
        return new PlanViewHolder(binding);
    }

    public void currentPlan(int position) {
        currentPlan.dayData(listWeekPlan.get(position), position);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void onBindViewHolder(@NonNull PlanViewHolder holder, int position) {
        OneDayData oneDayData = listWeekPlan.get(position);
        if (!oneDayData.isShowDay()) {
            ColorMatrix matrix = new ColorMatrix();
            matrix.setSaturation(0);
            holder.imagePlan.setColorFilter(new ColorMatrixColorFilter(matrix));
        } else {
            holder.imagePlan.clearColorFilter();
        }
        Glide.with(holder.itemView.getContext()).load(Uri.parse("file:///android_asset/imageDay/" + oneDayData.getCoverDayPlan().getOrder() + ".webp")).centerCrop().into(holder.imagePlan);
        holder.coverPhotoMeal.setBackgroundResource(getBackground(oneDayData));
        holder.arrowStart.setVisibility(oneDayData.isShowDay() ? View.VISIBLE : View.GONE);
        holder.containerWork.setVisibility(oneDayData.isShowDay() ? View.VISIBLE : View.GONE);
        holder.containerRest.setVisibility(oneDayData.isShowDay() ? View.GONE : View.VISIBLE);
        TextTwoRow.setText(holder.titleWorkout, holder.titleWorkoutSecond, oneDayData.getDayOfWeek().getLongName());
        TextTwoRow.setText(holder.titleRest, holder.titleRestSecond, oneDayData.getDayOfWeek().getLongName());
        holder.titleMeal.setText(oneDayData.getDayOfWeek().getNameMeal());
        String textCalInfo;
        if (!oneDayData.isShowDay()) {
            textCalInfo = "";
        } else {
            if (oneDayData.getDayPlan() != null) {
                textCalInfo = oneDayData.getDayPlan().getRecomandations();
            } else {
                textCalInfo = "";
            }
        }
        if (textCalInfo == null) {
            textCalInfo = "";
        }
        holder.calInfo.setText(translateDetail(textCalInfo));
        String textTimeInfo = !oneDayData.isShowDay() ? "" : oneDayData.getDayPlan().getWarmUpDescription();
        holder.timeInfo.setText(translateDetail(textTimeInfo));
        holder.calInfo.setVisibility(!oneDayData.isShowDay() ? View.INVISIBLE : View.VISIBLE);
        holder.timeInfo.setVisibility(!oneDayData.isShowDay() ? View.INVISIBLE : View.VISIBLE);
        holder.itemMeal.setOnClickListener(v -> {
            setVibrate(v);
            planClicked.mealAccessed(oneDayData, oneDayData.getDayOfWeek().getNameMeal(), position);
        });
        holder.btnContinue.setText(Translate.getValue("start_workout"));
        holder.btnContinue.setOnTouchListener(
                createBothPressedListener(holder.btnContinue, holder.arrowStart, oneDayData, position)
        );
        holder.arrowStart.setOnTouchListener(
                createBothPressedListener(holder.arrowStart, holder.btnContinue, oneDayData, position)
        );
    }

    private View.OnTouchListener createBothPressedListener(
            View primaryView,
            View secondaryView,
            OneDayData oneDayData,
            int position
    ) {
        return (v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    primaryView.setPressed(true);
                    secondaryView.setPressed(true);
                    break;

                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    setVibrate(v);
                    primaryView.setPressed(false);
                    secondaryView.setPressed(false);
                    planClicked.planAccessed(oneDayData, position);
                    v.performClick();
                    break;
                default:
                    // do nothing
                    break;
            }
            return true;
        };
    }

    public String translateDetail(String text) {
        String textFinal = text;
        if (!text.isEmpty()) {
            if (textFinal.contains("kcal"))
                textFinal = textFinal.replace("kcal", Translate.getValue("kcal"));
            else if (textFinal.contains("min"))
                textFinal = textFinal.replace("min", Translate.getValue("min"));
        }
        return textFinal;
    }

    public int getBackground(OneDayData oneDayData) {
        int positionBackground = oneDayData.getCoverDayPlan().getOrder() - 1;
        if (oneDayData.isShowDay() && positionBackground < listDayBackground.size())
            return listDayBackground.get(positionBackground);
        else
            return R.drawable.main_background_day0;

    }

    @Override
    public int getItemCount() {
        return listWeekPlan.size();
    }

    public static class PlanViewHolder extends RecyclerView.ViewHolder {
        ImageView imagePlan;
        ImageButton arrowStart;
        RelativeLayout containerWork;
        RelativeLayout containerRest;
        TextView titleRest;
        ImageView coverPhotoMeal;
        TextView titleWorkout;
        TextView titleWorkoutSecond;
        TextView titleRestSecond;
        TextView titleMeal;
        TextView calInfo;
        TextView timeInfo;
        RelativeLayout itemWorkout;
        RelativeLayout itemMeal;
        AutoRTextView btnContinue;

        public PlanViewHolder(@NonNull ContainerPlanBinding binding) {
            super(binding.getRoot());
            imagePlan = binding.coverPlan.imagePlan;
            arrowStart = binding.coverPlan.arrowStart;
            containerWork = binding.coverPlan.containerWork;
            containerRest = binding.coverPlan.containerRest;
            titleRest = binding.coverPlan.titleRest;
            coverPhotoMeal = binding.coverMeal.coverPhotoMeal;
            titleWorkout = binding.coverPlan.titleWorkout;
            titleWorkoutSecond = binding.coverPlan.titleWorkoutSecond;
            titleRestSecond = binding.coverPlan.titleRestSecond;
            titleMeal = binding.coverMeal.titleMeal;
            calInfo = binding.coverPlan.calInfo;
            timeInfo = binding.coverPlan.timeInfo;
            itemWorkout = binding.coverPlan.itemWorkout;
            itemMeal = binding.coverMeal.itemMeal;
            btnContinue = binding.coverPlan.btnContinue;
        }
    }
}
