package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentMealRedesignBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;

public class MealRedesignFragment extends Fragment {
    private FragmentMealRedesignBinding binding;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;

    public static MealRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        MealRedesignFragment fragment = new MealRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View] Current favorite food View appeared");

    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentMealRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();
        binding.fishMeal.setOnClickListener(v -> {
            saveStatusMeal("fishMeal", binding.fishMeal);
            getStatusMeal("fishMeal", binding.fishMeal, binding.fishMealTxt);
            setVibrate(v);
        });
        binding.vegetablesMeal.setOnClickListener(v -> {
            saveStatusMeal("vegetablesMeal", binding.vegetablesMeal);
            getStatusMeal("vegetablesMeal", binding.vegetablesMeal, binding.vegetablesMealTxt);
            setVibrate(v);
        });
        binding.soupsMeal.setOnClickListener(v -> {
            saveStatusMeal("soupsMeal", binding.soupsMeal);
            getStatusMeal("soupsMeal", binding.soupsMeal, binding.soupsMealTxt);
            setVibrate(v);
        });
        binding.meatMeal.setOnClickListener(v -> {
            saveStatusMeal("meatMeal", binding.meatMeal);
            getStatusMeal("meatMeal", binding.meatMeal, binding.meatMealTxt);
            setVibrate(v);
        });
        binding.saladsMeal.setOnClickListener(v -> {
            saveStatusMeal("saladsMeal", binding.saladsMeal);
            getStatusMeal("saladsMeal", binding.saladsMeal, binding.saladsMealTxt);
            setVibrate(v);
        });
        getStatusMeal("fishMeal", binding.fishMeal, binding.fishMealTxt);
        getStatusMeal("vegetablesMeal", binding.vegetablesMeal, binding.vegetablesMealTxt);
        getStatusMeal("soupsMeal", binding.soupsMeal, binding.soupsMealTxt);
        getStatusMeal("meatMeal", binding.meatMeal, binding.meatMealTxt);
        getStatusMeal("saladsMeal", binding.saladsMeal, binding.saladsMealTxt);

        binding.rlContinue.setOnClickListener(view1 -> {
            isValidPush = false;
            SleepRedesignFragment fragmentC = SleepRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_").commitAllowingStateLoss();
            setVibrate(view1);
        });
        binding.backLayout.setOnClickListener(view12 -> toBack());
        binding.backTxt.getPaint().setUnderlineText(true);
        setParamsFragment();
    }


    private void saveStatusMeal(String keyMeal, RelativeLayout layout) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyMeal, layout == binding.vegetablesMeal || layout == binding.meatMeal);
        prefsUtilsWtContext.setBooleanPreference(keyMeal, !isSelected);
    }

    private void getStatusMeal(String keyMeal, RelativeLayout layout, TextView textView) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyMeal, layout == binding.vegetablesMeal || layout == binding.meatMeal);
        layout.setSelected(isSelected);
        textView.setSelected(isSelected);
    }

    public void setParamsFragment() {
        binding.textView16.setText(Translate.getValue("what’s_your_favorite_food"));
        binding.textView20.setText(Translate.getValue("we’ll_provide_you_with_recipes_for_healthy_and_delicious_meals"));
        binding.btnContinue.setText(Translate.getValue("next"));
        binding.backTxt.setText(Translate.getValue("go_back"));
        binding.fishMealTxt.setText(Translate.getValue("fish"));
        binding.vegetablesMealTxt.setText(Translate.getValue("vegetables"));
        binding.soupsMealTxt.setText(Translate.getValue("soups"));
        binding.meatMealTxt.setText(Translate.getValue("meat"));
        binding.saladsMealTxt.setText(Translate.getValue("salads"));
        binding.linearProgress.setProgress(60);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
                toBack();
                return true; // pretend we've processed it
            } else return false; // pass on to be processed as normal
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void toBack() {
        isValidPush = false;
        if (getActivity() != null) getActivity().onBackPressed();
    }
}
