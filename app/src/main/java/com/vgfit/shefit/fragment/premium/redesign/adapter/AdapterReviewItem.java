package com.vgfit.shefit.fragment.premium.redesign.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.vgfit.shefit.databinding.ItemReviewOfferBinding;
import com.vgfit.shefit.fragment.premium.redesign.model.ReviewItemOffer;

import java.util.ArrayList;

public class AdapterReviewItem extends RecyclerView.Adapter<AdapterReviewItem.ReviewItemViewHolder> {
    private final ArrayList<ReviewItemOffer> listItem;


    public AdapterReviewItem(ArrayList<ReviewItemOffer> listItem) {
        this.listItem = new ArrayList<>(listItem);
    }

    @NonNull
    @Override
    public ReviewItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ItemReviewOfferBinding binding = ItemReviewOfferBinding.inflate(inflater, parent, false);
        return new ReviewItemViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull ReviewItemViewHolder holder, int position) {
        ReviewItemOffer reviewItemOffer = listItem.get(position);
        holder.titleReview.setText(reviewItemOffer.getTitle());
        holder.reviewApp.setText(reviewItemOffer.getMessage());
        holder.authorReview.setText(reviewItemOffer.getAuthor());
        holder.dateReview.setText(reviewItemOffer.getDate());
    }

    @Override
    public int getItemCount() {
        return listItem.size();
    }

    public static class ReviewItemViewHolder extends RecyclerView.ViewHolder {
        TextView titleReview;
        TextView reviewApp;
        TextView authorReview;
        TextView dateReview;

        public ReviewItemViewHolder(@NonNull ItemReviewOfferBinding binding) {
            super(binding.getRoot());
            titleReview = binding.titleReview;
            reviewApp = binding.reviewApp;
            authorReview = binding.authorReview;
            dateReview = binding.dateReview;
        }
    }
}
