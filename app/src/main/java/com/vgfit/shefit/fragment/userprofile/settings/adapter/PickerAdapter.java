package com.vgfit.shefit.fragment.userprofile.settings.adapter;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.vgfit.shefit.R;
import com.vgfit.shefit.fragment.userprofile.settings.model.PickerModel;

import java.util.List;

/**
 * Created by adityagohad on 06/06/17.
 */

public class PickerAdapter extends RecyclerView.Adapter<PickerAdapter.TextVH> {

    private final Context context;
    private List<PickerModel> dataList;
    private final RecyclerView recyclerView;

    public PickerAdapter(Context context, List<PickerModel> dataList, RecyclerView recyclerView) {
        this.context = context;
        this.dataList = dataList;
        this.recyclerView = recyclerView;
    }

    public void swapList(List<PickerModel> dataList) {
        this.dataList = dataList;
        notifyDataSetChanged();
    }

    public int getPositionSelect(String item, boolean isFt) {
        Log.d("TestPosition", "item-->" + item + " isFt-->" + isFt);
        if (!dataList.isEmpty()) {
            if (isFt) {
                String valueFt;
                valueFt = item;
                Log.d("TestPosition", "valueFt-->" + valueFt);
                for (int i = 0; i < dataList.size(); i++) {
                    PickerModel pickerModel = dataList.get(i);
                    if (pickerModel.getValue().equals(valueFt)) return pickerModel.getPosValue();
                }
            } else {
                for (int i = 0; i < dataList.size(); i++) {
                    PickerModel pickerModel = dataList.get(i);
                    if (pickerModel.getValue().equals(item)) return i;
                }
            }
        }
        return 0;
    }

    @Override
    public TextVH onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view;
        LayoutInflater inflater = LayoutInflater.from(context);
        view = inflater.inflate(R.layout.item_hor_settings, parent, false);
        return new TextVH(view);
    }

    @Override
    public void onBindViewHolder(TextVH holder, final int position) {
        String textValue = dataList.get(position).getValue();
        holder.pickerTxt.setText(textValue);
        holder.pickerTxt.setOnClickListener(v -> {
            if (recyclerView != null) {
                recyclerView.smoothScrollToPosition(position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    static class TextVH extends RecyclerView.ViewHolder {
        TextView pickerTxt;

        public TextVH(View itemView) {
            super(itemView);
            pickerTxt = (TextView) itemView.findViewById(R.id.picker_item);
        }
    }
}
