package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.library.rullerview.slider.ISlideListener;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentStartShapyBinding;
import com.vgfit.shefit.fragment.loginweb.LoginWebFragment;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.screen.ImageUtils;

import java.util.Collections;

public class StartShapyRedesignFragment extends Fragment {
    private FragmentStartShapyBinding binding;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;
    private boolean isValidAnimation = true;
    private static final String TAG_FRAG = "frag_";
    private PrefsUtilsWtContext prefsUtilsWtContext;

    public static StartShapyRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        StartShapyRedesignFragment fragment = new StartShapyRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    private static boolean onKey(View v, int keyCode, KeyEvent event) {
        return keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN; // pretend we've processed it
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        sendAmplitude("[View]  Shapy Let's go View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentStartShapyBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();

        binding.slideToConfirm.setSlideListener(new ISlideListener() {
            @Override
            public void onSlideStart() {
                binding.slideToConfirm.setElevation(10);
            }

            @Override
            public void onSlideMove(float percent) {
                Log.d("TestSlide", "Slide percent-->" + percent);
                float alpha = 1 - percent / 2;
                binding.clickedSlider.setAlpha(alpha);
            }

            @Override
            public void onSlideCancel() {
                binding.clickedSlider.setAlpha(1);
                binding.slideToConfirm.setElevation(0);
            }

            @Override
            public void onSlideDone() {
                isValidPush = false;
                binding.clickedSlider.setPressed(true);
                Log.d("TestClick", "next activate");
                KnowBetterRedesignFragment fragmentC = KnowBetterRedesignFragment.newInstance(lunchFirstTime);
                FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
                transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
                transaction.replace(R.id.root_fragment, fragmentC).addToBackStack(TAG_FRAG).commitAllowingStateLoss();
            }
        });
        binding.startOnboarding.setOnClickListener(v -> {
            isValidPush = false;
            KnowBetterRedesignFragment fragmentC = KnowBetterRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack(TAG_FRAG).commitAllowingStateLoss();
        });

        binding.clickedSlider.setText(Translate.getValue("let’s_go"));
        binding.startOnboarding.setText(Translate.getValue("let’s_go"));
        binding.slideToConfirm.setCompletedText(" ");

        binding.slideToConfirm.setOnClickListener(v -> isValidAnimation = false);
        binding.clickedSlider.setOnClickListener(v -> {
            isValidAnimation = false;
            binding.slideToConfirm.slideToComplet();
            setVibrate(v);
        });
        binding.textView23.setText(Translate.getValueLine("make_sports_a_daily_habit_with", Collections.singletonList(4), true));
        ImageLoader.getInstance().displayImage("assets://onboardingImage/10.webp", binding.imageView, ImageUtils.getDefaultDisplayImageOptions(), null);
        String textFirst = Translate.getValue("already_have_an_account");
        String textSelect = " " + Translate.getValue("sign_in");


        Spannable word = new SpannableString(textFirst);
        word.setSpan(new ForegroundColorSpan(Color.WHITE), 0, word.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        binding.loginStart.setText(word);
        Spannable wordTwo = new SpannableString(textSelect);
        if (getContext() != null)
            wordTwo.setSpan(new ForegroundColorSpan(ContextCompat.getColor(getContext(), R.color.text_color_lime)), 0, wordTwo.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        binding.loginStart.append(wordTwo);
        binding.loginStart.setOnClickListener(v -> {
            isValidPush = false;
            LoginWebFragment fragmentC = LoginWebFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_login_web").commitAllowingStateLoss();
        });
//        startAnimation();
    }

    private void startAnimation() {
        HandlerThread handlerThread = new HandlerThread("MyHandlerThread");
        handlerThread.start();
        Handler handler = new Handler(handlerThread.getLooper());
        handler.postDelayed(() -> {
            try {
                if (isValidAnimation) {
                    binding.slideToConfirm.moveSlide(100);
                    startAnimation();
                }
            } catch (Exception ignored) {
                Log.d("ErrorHandler", "handler error");
            }

        }, 1500);

    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener(StartShapyRedesignFragment::onKey);
        try {
            String email = prefsUtilsWtContext.getStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB);
            Log.d("TestEmail", "Start Shapy email-->" + email);
            if (email != null && !email.isEmpty() && getActivity() != null) {
                isValidPush = false;
                NotificationTurnFragment fragmentC = NotificationTurnFragment.newInstance(true);
                FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
                transaction.setCustomAnimations(R.anim.animation_fade_in, R.anim.animation_fade_out, R.anim.slide_in_left, R.anim.slide_out_right);
                transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_crushing").commitAllowingStateLoss();
            }
        } catch (Exception ignored) {
            //ignore exception
        }
    }

}
