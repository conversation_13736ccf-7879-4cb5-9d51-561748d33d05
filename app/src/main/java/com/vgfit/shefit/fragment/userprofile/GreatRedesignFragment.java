package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;
import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.text.style.ImageSpan;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.nostra13.universalimageloader.core.ImageLoader;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.FragmentGreatCustomizeBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.screen.ImageUtils;

import java.util.Arrays;


public class GreatRedesignFragment extends Fragment {
    private FragmentGreatCustomizeBinding binding;


    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;
    private Context context;

    public static GreatRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        GreatRedesignFragment fragment = new GreatRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        context = getContext();
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View]GREAT View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentGreatCustomizeBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();

        binding.startOnboarding.setOnClickListener(view1 -> {
            isValidPush = false;
            RulerHeightRedesignFragment fragmentC = RulerHeightRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(0, R.anim.slide_down, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_").commitAllowingStateLoss();
            setVibrate(view1);
        });
        if (getContext() != null)
            ImageLoader.getInstance().displayImage("assets://onboardingImage/6_.webp", binding.imageView, ImageUtils.getRoundDisplayImageOptions(dpToPx(getContext(), 30)), null);
        String textFirst = Translate.getValueLine("we’ll_customize_your_plan_according_to_your_personal_goals", Arrays.asList(3, 6), false);
        Spannable word = new SpannableString(textFirst);
        word.setSpan(new ForegroundColorSpan(Color.WHITE), 0, word.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        ImageSpan is = new ImageSpan(context, R.drawable.chevron_right);
        SpannableString text = new SpannableString(textFirst + "  ");
        text.setSpan(is, text.length() - 2, text.length(), 0);
        binding.shortDescription.setText(text);
        binding.textView21.setText(Translate.getValue("great!"));
    }


    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener((v, keyCode, event) -> {
            return keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN; // pretend we've processed it
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

}
