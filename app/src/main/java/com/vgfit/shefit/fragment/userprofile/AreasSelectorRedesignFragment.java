package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;
import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.nostra13.universalimageloader.core.ImageLoader;
import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentAreaRedesignBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.screen.ImageUtils;

import java.util.Collections;

public class AreasSelectorRedesignFragment extends Fragment {
    private FragmentAreaRedesignBinding binding;
    private static final String COLOR_START = "#000000";
    private static final String COLOR_END = "#000000";
    private static final String COLOR_CIRCLE = "#E0FF21";
    private static final String EMPTY_COLOR = "#ecebe6";
    private static final int CONST_CURVE_RADIUS = 0;
    private static final int STROKE_SIZE = 1;
    private int startPointArmsX = 0;
    private int startPointArmsY = 0;
    private int endPointArmsX = 0;
    private int endPointArmsY = 0;

    private int startPointBackX = 0;
    private int startPointBackY = 0;
    private int endPointBackX = 0;
    private int endPointBackY = 0;

    private int startPointAbsX = 0;
    private int startPointAbsY = 0;
    private int endPointAbsX = 0;
    private int endPointAbsY = 0;

    private int startPointButtX = 0;
    private int startPointButtY = 0;
    private int endPointButtX = 0;
    private int endPointButtY = 0;

    private int startPointLegsX = 0;
    private int startPointLegsY = 0;
    private int endPointLegsX = 0;
    private int endPointLegsY = 0;

    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View mainView;
    private Context context;

    private static final int TYPE_ARMS = 0;
    private static final int TYPE_BACK = 1;
    private static final int TYPE_ABS = 2;
    private static final int TYPE_BUTT = 3;
    private static final int TYPE_LEGS = 4;

    private static final String ARMS_AREA = "armsArea";
    private static final String BACK_AREA = "backArea";
    private static final String ABS_AREA = "absArea";
    private static final String BUTT_AREA = "buttArea";
    private static final String LEGS_AREA = "legsArea";

    public static AreasSelectorRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        AreasSelectorRedesignFragment fragment = new AreasSelectorRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View] Areas most attention View appeared");
        context = getContext();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentAreaRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.mainView = view;
        this.mainView.setFocusableInTouchMode(true);
        this.mainView.requestFocus();

        ViewTreeObserver vto = binding.armsArea.getViewTreeObserver();
        vto.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                    int[] location = new int[2];
                    binding.armsArea.getLocationOnScreen(location);
                    int x = location[0];
                    int y = location[1];
                    startPointArmsX = x + binding.armsArea.getWidth() / 5;
                    startPointArmsY = y + binding.armsArea.getHeight();

                    location = new int[2];
                    binding.backArea.getLocationOnScreen(location);
                    x = location[0];
                    y = location[1];
                    startPointBackX = x + binding.backArea.getWidth();
                    startPointBackY = y + binding.backArea.getHeight() / 2;

                    location = new int[2];
                    binding.absArea.getLocationOnScreen(location);
                    x = location[0];
                    y = location[1];
                    startPointAbsX = x;
                    startPointAbsY = y + binding.absArea.getHeight() / 2;

                    location = new int[2];
                    binding.buttArea.getLocationOnScreen(location);
                    x = location[0];
                    y = location[1];
                    startPointButtX = x + binding.buttArea.getWidth() / 5;
                    startPointButtY = y + 20;

                    location = new int[2];
                    binding.legsArea.getLocationOnScreen(location);
                    x = location[0];
                    y = location[1];
                    startPointLegsX = x + binding.legsArea.getWidth() / 2;
                    startPointLegsY = y + 20;

                    //end Point
                    location = new int[2];
                    binding.pointArms.getLocationOnScreen(location);
                    endPointArmsX = location[0];
                    endPointArmsY = location[1];

                    location = new int[2];
                    binding.pointBack.getLocationOnScreen(location);
                    endPointBackX = location[0];
                    endPointBackY = location[1];

                    location = new int[2];
                    binding.pointAbs.getLocationOnScreen(location);
                    endPointAbsX = location[0];
                    endPointAbsY = location[1];

                    location = new int[2];
                    binding.pointButt.getLocationOnScreen(location);
                    endPointButtX = location[0];
                    endPointButtY = location[1];

                    location = new int[2];
                    binding.pointLegs.getLocationOnScreen(location);
                    endPointLegsX = location[0];
                    endPointLegsY = location[1];

                    view.getViewTreeObserver().removeGlobalOnLayoutListener(this);

                    boolean isSelected = getStatusArea(ARMS_AREA, binding.armsArea, binding.armsAreaTxt);
                    switchAreaTraining(TYPE_ARMS, isSelected);

                    isSelected = getStatusArea(BACK_AREA, binding.backArea, binding.backAreaTxt);
                    switchAreaTraining(TYPE_BACK, isSelected);

                    isSelected = getStatusArea(ABS_AREA, binding.absArea, binding.absAreaTxt);
                    switchAreaTraining(TYPE_ABS, isSelected);

                    isSelected = getStatusArea(BUTT_AREA, binding.buttArea, binding.buttAreaTxt);
                    switchAreaTraining(TYPE_BUTT, isSelected);

                    isSelected = getStatusArea(LEGS_AREA, binding.legsArea, binding.legsAreaTxt);
                    switchAreaTraining(TYPE_LEGS, isSelected);

            }
        });
        binding.armsArea.setOnClickListener(v -> {
            saveStatusArea(ARMS_AREA, binding.armsArea);
            boolean isSelected = getStatusArea(ARMS_AREA, binding.armsArea, binding.armsAreaTxt);
            switchAreaTraining(TYPE_ARMS, isSelected);
            setVibrate(v);
        });
        binding.backArea.setOnClickListener(v -> {
            saveStatusArea(BACK_AREA, binding.backArea);
            boolean isSelected = getStatusArea(BACK_AREA, binding.backArea, binding.backAreaTxt);
            switchAreaTraining(TYPE_BACK, isSelected);
            setVibrate(v);
        });
        binding.absArea.setOnClickListener(v -> {
            saveStatusArea(ABS_AREA, binding.absArea);
            boolean isSelected = getStatusArea(ABS_AREA, binding.absArea, binding.absAreaTxt);
            switchAreaTraining(TYPE_ABS, isSelected);
            setVibrate(v);
        });
        binding.buttArea.setOnClickListener(v -> {
            saveStatusArea(BUTT_AREA, binding.buttArea);
            boolean isSelected = getStatusArea(BUTT_AREA, binding.buttArea, binding.buttAreaTxt);
            switchAreaTraining(TYPE_BUTT, isSelected);
            setVibrate(v);
        });
        binding.legsArea.setOnClickListener(v -> {
            saveStatusArea(LEGS_AREA, binding.legsArea);
            boolean isSelected = getStatusArea(LEGS_AREA, binding.legsArea, binding.legsAreaTxt);
            switchAreaTraining(TYPE_LEGS, isSelected);
            setVibrate(v);
        });


        binding.rlContinue.setOnClickListener(view1 -> {
            isValidPush = false;
            SelectorWorksRedesignFragment fragmentC = SelectorWorksRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_").commitAllowingStateLoss();
            setVibrate(view1);
        });
        ImageLoader.getInstance().displayImage("assets://onboardingImage/4.jpg", binding.imageArea, ImageUtils.getRoundDisplayImageOptions(50), null);
        binding.backLayout.setOnClickListener(view12 -> toBack());
        binding.backTxt.getPaint().setUnderlineText(true);
        setParamsFragment();
    }


    private void saveStatusArea(String keyArea, RelativeLayout layout) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyArea, binding.absArea == layout);
        prefsUtilsWtContext.setBooleanPreference(keyArea, !isSelected);
    }

    private boolean getStatusArea(String keyArea, RelativeLayout layout, TextView textView) {
        boolean isSelected = false;
        if (getContext() != null) {
            isSelected = prefsUtilsWtContext.getBooleanPreference(keyArea, binding.absArea == layout);
            layout.setBackgroundResource(isSelected ? R.drawable.background_meal_ : R.drawable.background_meal);
            textView.setTextColor(isSelected ? ContextCompat.getColor(getContext(), R.color.text_color_lime) : ContextCompat.getColor(getContext(), R.color.black));
        }
        return isSelected;
    }

    public void setParamsFragment() {
        binding.textView16.setText(Translate.getValueLine("which_areas_need_the_most_attention", Collections.singletonList(4), false));
        binding.armsAreaTxt.setText(Translate.getValue("armss"));
        binding.backAreaTxt.setText(Translate.getValue("backk"));
        binding.absAreaTxt.setText(Translate.getValue("abss"));
        binding.buttAreaTxt.setText(Translate.getValue("butt"));
        binding.legsAreaTxt.setText(Translate.getValue("legss"));
        binding.btnContinue.setText(Translate.getValue("next"));
        binding.backTxt.setText(Translate.getValue("go_back"));
        binding.linearProgress.setProgress(16);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        mainView.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
                toBack();
                return true; // pretend we've processed it
            } else return false; // pass on to be processed as normal
        });
    }

    private void toBack() {
        isValidPush = false;
        if (getActivity() != null) getActivity().onBackPressed();
    }

    private void switchAreaTraining(int type, boolean isSelected) {
        switch (type) {
            case TYPE_ARMS:
                if (!isSelected)
                    binding.lineAnimate1.init(startPointArmsX, startPointArmsY, endPointArmsX, endPointArmsY, dpToPx(context, CONST_CURVE_RADIUS), Color.parseColor(EMPTY_COLOR), Color.parseColor(EMPTY_COLOR), Color.parseColor(EMPTY_COLOR), dpToPx(context, STROKE_SIZE));
                else
                    binding.lineAnimate1.init(startPointArmsX, startPointArmsY, endPointArmsX, endPointArmsY, dpToPx(context, CONST_CURVE_RADIUS), Color.parseColor(COLOR_START), Color.parseColor(COLOR_END), Color.parseColor(COLOR_CIRCLE), dpToPx(context, STROKE_SIZE));

                break;
            case TYPE_BACK:
                if (!isSelected)
                    binding.lineAnimate2.init(startPointBackX, startPointBackY, endPointBackX, endPointBackY, dpToPx(context, CONST_CURVE_RADIUS), Color.parseColor(EMPTY_COLOR), Color.parseColor(EMPTY_COLOR), Color.parseColor(EMPTY_COLOR), dpToPx(context, STROKE_SIZE));
                else
                    binding.lineAnimate2.init(startPointBackX, startPointBackY, endPointBackX, endPointBackY, dpToPx(context, CONST_CURVE_RADIUS), Color.parseColor(COLOR_START), Color.parseColor(COLOR_END), Color.parseColor(COLOR_CIRCLE), dpToPx(context, STROKE_SIZE));

                break;
            case TYPE_ABS:
                if (!isSelected)
                    binding.lineAnimate3.init(startPointAbsX, startPointAbsY, endPointAbsX, endPointAbsY, dpToPx(context, CONST_CURVE_RADIUS), Color.parseColor(EMPTY_COLOR), Color.parseColor(EMPTY_COLOR), Color.parseColor(EMPTY_COLOR), dpToPx(context, STROKE_SIZE));
                else
                    binding.lineAnimate3.init(startPointAbsX, startPointAbsY, endPointAbsX, endPointAbsY, dpToPx(context, CONST_CURVE_RADIUS), Color.parseColor(COLOR_START), Color.parseColor(COLOR_END), Color.parseColor(COLOR_CIRCLE), dpToPx(context, STROKE_SIZE));

                break;
            case TYPE_BUTT:
                if (!isSelected)
                    binding.lineAnimate4.init(startPointButtX, startPointButtY, endPointButtX, endPointButtY, dpToPx(context, CONST_CURVE_RADIUS), Color.parseColor(EMPTY_COLOR), Color.parseColor(EMPTY_COLOR), Color.parseColor(EMPTY_COLOR), dpToPx(context, STROKE_SIZE));
                else
                    binding.lineAnimate4.init(startPointButtX, startPointButtY, endPointButtX, endPointButtY, dpToPx(context, CONST_CURVE_RADIUS), Color.parseColor(COLOR_START), Color.parseColor(COLOR_END), Color.parseColor(COLOR_CIRCLE), dpToPx(context, STROKE_SIZE));

                break;
            case TYPE_LEGS:
                if (!isSelected)
                    binding.lineAnimate5.init(startPointLegsX, startPointLegsY, endPointLegsX, endPointLegsY, dpToPx(context, CONST_CURVE_RADIUS), Color.parseColor(EMPTY_COLOR), Color.parseColor(EMPTY_COLOR), Color.parseColor(EMPTY_COLOR), dpToPx(context, STROKE_SIZE));
                else
                    binding.lineAnimate5.init(startPointLegsX, startPointLegsY, endPointLegsX, endPointLegsY, dpToPx(context, CONST_CURVE_RADIUS), Color.parseColor(COLOR_START), Color.parseColor(COLOR_END), Color.parseColor(COLOR_CIRCLE), dpToPx(context, STROKE_SIZE));

                break;
            default: break;
        }
    }

}
