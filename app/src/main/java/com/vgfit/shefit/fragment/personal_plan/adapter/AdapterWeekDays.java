package com.vgfit.shefit.fragment.personal_plan.adapter;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.ItemDayWeekNewBinding;
import com.vgfit.shefit.fragment.personal_plan.callbacks.Dayclicked;
import com.vgfit.shefit.fragment.personal_plan.model.OneDayData;

import java.util.ArrayList;
import java.util.List;

public class AdapterWeekDays extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private final ArrayList<OneDayData> listWeekData;
    private int posSelected = 0;
    private final Dayclicked dayclicked;
    private boolean isActiveAnimSelected;
    private boolean isActiveAnimUnSelected;
    private final List<Integer> listDayBackground;

    public AdapterWeekDays(ArrayList<OneDayData> listWeekData, Dayclicked dayclicked) {
        this.listWeekData = new ArrayList<>(listWeekData);
        this.dayclicked = dayclicked;
        listDayBackground = new ArrayList<>();
        fillListBackground();
        isActiveAnimSelected = true;
        isActiveAnimUnSelected = true;
    }

    private void fillListBackground() {
        listDayBackground.add(R.drawable.main_background_day1);
        listDayBackground.add(R.drawable.main_background_day2);
        listDayBackground.add(R.drawable.main_background_day3);
        listDayBackground.add(R.drawable.main_background_day4);
        listDayBackground.add(R.drawable.main_background_day5);
        listDayBackground.add(R.drawable.main_background_day6);
        listDayBackground.add(R.drawable.main_background_day7);
        listDayBackground.add(R.drawable.main_background_day8);
        listDayBackground.add(R.drawable.main_background_day9);
        listDayBackground.add(R.drawable.main_background_day10);
        listDayBackground.add(R.drawable.main_background_day11);
        listDayBackground.add(R.drawable.main_background_day12);
        listDayBackground.add(R.drawable.main_background_day13);
        listDayBackground.add(R.drawable.main_background_day14);
        listDayBackground.add(R.drawable.main_background_day15);
        listDayBackground.add(R.drawable.main_background_day16);
        listDayBackground.add(R.drawable.main_background_day17);
        listDayBackground.add(R.drawable.main_background_day18);
        listDayBackground.add(R.drawable.main_background_day19);
        listDayBackground.add(R.drawable.main_background_day20);
        listDayBackground.add(R.drawable.main_background_day21);
        listDayBackground.add(R.drawable.main_background_day22);
        listDayBackground.add(R.drawable.main_background_day23);
        listDayBackground.add(R.drawable.main_background_day24);
        listDayBackground.add(R.drawable.main_background_day25);
        listDayBackground.add(R.drawable.main_background_day26);
        listDayBackground.add(R.drawable.main_background_day27);
        listDayBackground.add(R.drawable.main_background_day28);
        listDayBackground.add(R.drawable.main_background_day29);
        listDayBackground.add(R.drawable.main_background_day30);
        listDayBackground.add(R.drawable.main_background_day31);
    }

    public int getBackground(int pos) {
        if (pos < listDayBackground.size() && pos >= 0) {
            OneDayData oneDayData = listWeekData.get(pos);
            int positionBackground = oneDayData.getCoverDayPlan().getOrder() - 1;
            Log.d("TestPosition", "positionBackground-->" + positionBackground);
            if (oneDayData.isShowDay() && positionBackground < listDayBackground.size())
                return listDayBackground.get(positionBackground);
            else
                return R.drawable.main_background_day0;
        } else return 0;
    }

    public void setListWeekData(ArrayList<OneDayData> listWeekData, int currentDay) {
        this.listWeekData.clear();
        this.listWeekData.addAll(listWeekData);
        posSelected = currentDay;
        if (listWeekData.size() > 0)
            for (int i = 0; i < listWeekData.size(); i++)
                listWeekData.get(i).setSelectedDay(i == posSelected);
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return listWeekData.size();
    }

    public void selectDay(int position) {
        isActiveAnimUnSelected = true;
        listWeekData.get(posSelected).setSelectedDay(false);
        notifyItemChanged(posSelected);

        posSelected = position;
        isActiveAnimSelected = true;
        listWeekData.get(position).setSelectedDay(true);
        notifyItemChanged(position);
    }

    public OneDayData getOneDayData() {
        if (listWeekData.size() > 0)
            return listWeekData.get(posSelected);
        return new OneDayData();
    }

    public int getPosSelected() {
        return posSelected;
    }

    public boolean isWorkout() {
        return listWeekData.get(posSelected).isShowDay();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ItemDayWeekNewBinding binding = ItemDayWeekNewBinding.inflate(inflater, parent, false);
        return new DayViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        OneDayData oneDayData = listWeekData.get(position);
        DayViewHolder dayViewHolder = (DayViewHolder) holder;

        dayViewHolder.nameDay.setText(oneDayData.getDayOfWeek().getNameShort());
        dayViewHolder.itemView.setOnClickListener(v -> {
            if (position != posSelected) {
                selectDay(position);
                dayclicked.dayData(getOneDayData(), position, getBackground(position));
            }
        });
//        dayViewHolder.squareSelector.setVisibility(oneDayData.isSelectedDay() ? View.VISIBLE : View.INVISIBLE);
//        dayViewHolder.cellBackground.setBackgroundResource(listDayBackground.get(position));
        dayViewHolder.dayDate.setText(oneDayData.getDayOfWeek().getDate());
//        selectedItemAnimation(dayViewHolder.itemView, oneDayData.isSelectedDay(), position);
    }

    private void selectedItemAnimation(View view, boolean isSelectedDay, int position) {
        if (isActiveAnimUnSelected && !isSelectedDay) {
            isActiveAnimUnSelected = false;
            animateView(false, view);
        } else if (isActiveAnimSelected && isSelectedDay) {
            isActiveAnimSelected = false;
            animateView(true, view);
        }
        if (isSelectedDay) {
            posSelected = position;
        }

    }

    public static class DayViewHolder extends RecyclerView.ViewHolder {

        TextView nameDay;

        TextView dayDate;

        public DayViewHolder(@NonNull ItemDayWeekNewBinding binding) {
            super(binding.getRoot());
            nameDay = binding.nameDay;
            dayDate = binding.dayDate;
        }
    }

    private void animateView(boolean isSelectedItem, View view) {
        float pointStart = isSelectedItem ? 1.0f : 1.1f;
        float pointEnd = isSelectedItem ? 1.1f : 1.0f;
        AnimatorSet animationSet = new AnimatorSet();

        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", pointStart, pointEnd);
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", pointStart, pointEnd);

        animationSet.playTogether(scaleX, scaleY);
        animationSet.start();
    }
}
