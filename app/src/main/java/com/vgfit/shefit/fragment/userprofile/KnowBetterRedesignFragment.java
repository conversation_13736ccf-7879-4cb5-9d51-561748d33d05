package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.nostra13.universalimageloader.core.ImageLoader;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.FragmentKnowBetterBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.screen.ImageUtils;

import java.util.Arrays;


public class KnowBetterRedesignFragment extends Fragment {
    private FragmentKnowBetterBinding binding;

    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;

    public static KnowBetterRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        KnowBetterRedesignFragment fragment = new KnowBetterRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View] Know You Better View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentKnowBetterBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();

        binding.rlContinue.setOnClickListener(view1 -> {
            isValidPush = false;
            SportLatelyRedesignFragment fragmentC = SportLatelyRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_").commitAllowingStateLoss();
            setVibrate(view1);
        });
        binding.backLayout.setOnClickListener(view12 -> {
            toBack();
        });
        ImageLoader.getInstance().displayImage("assets://imageDay/3.webp", binding.imageView, ImageUtils.getDefaultDisplayImageOptions(), null);
        TextView knowBetter = (TextView) binding.textView21;
        knowBetter.setText(Translate.getValueLine("let’s_get_to_know_you_better", Arrays.asList(1, 3, 4, 5), false));
        String textFirst = Translate.getValueLine("get_your_personalized_workout_and_meal_plan", Arrays.asList(2, 3, 6, 11, 15), false);
        if (getContext() != null)
            binding.shortDescription.setText(Translate.colorizeSubString(textFirst, ContextCompat.getColor(getContext(), R.color.text_color_lime)));
        binding.btnContinue.setText(Translate.getValue("start"));
    }


    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
    }

    private void toBack() {
        isValidPush = false;
        if (getActivity() != null)
            getActivity().onBackPressed();
    }

}
