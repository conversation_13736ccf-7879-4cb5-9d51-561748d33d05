package com.vgfit.shefit.fragment.premium.redesign.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.ItemLocationReviewBinding;
import com.vgfit.shefit.fragment.premium.redesign.callback.SelectedLocation;
import com.vgfit.shefit.fragment.premium.redesign.model.ReviewOfferModel;

import java.util.ArrayList;

public class AdapterReviewLocation extends RecyclerView.Adapter<AdapterReviewLocation.ItemViewHolder> {
    private final ArrayList<ReviewOfferModel> listLocation;
    private int positionSelected = 0;
    private final SelectedLocation selectedLocation;

    public AdapterReviewLocation(ArrayList<ReviewOfferModel> listLocation, SelectedLocation selectedLocation) {
        this.listLocation = new ArrayList<>(listLocation);
        this.selectedLocation = selectedLocation;
        if (listLocation.size() > 0) {
            selectedLocation.selectedPosition(positionSelected);
        }
    }

    public void select(int positionSelected) {
        this.positionSelected = positionSelected;
        selectedLocation.selectedPosition(positionSelected);
        notifyDataSetChanged();
    }


    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ItemLocationReviewBinding binding = ItemLocationReviewBinding.inflate(inflater, parent, false);
        return new ItemViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull ItemViewHolder holder, int position) {
        ReviewOfferModel reviewOfferModel = listLocation.get(position);
        holder.nameManifest.setText(reviewOfferModel.getNameLocation());
        holder.itemView.setOnClickListener(v -> select(position));
        holder.containerLocation.setBackgroundResource(position == positionSelected ? R.drawable.container_body_involved : R.drawable.oval_subscribe_lime);
        holder.nameManifest.setTextColor(position == positionSelected ? holder.itemView.getResources().getColor(R.color.black) : holder.itemView.getResources().getColor(R.color.text_color_lime));
    }


    @Override
    public int getItemCount() {
        return listLocation.size();
    }

    public static class ItemViewHolder extends RecyclerView.ViewHolder {
        TextView nameManifest;
        LinearLayout containerLocation;

        public ItemViewHolder(ItemLocationReviewBinding binding) {
            super(binding.getRoot());
            nameManifest = binding.nameManifest;
            containerLocation = binding.containerLocation;
        }
    }
}