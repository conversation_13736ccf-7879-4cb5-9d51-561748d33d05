package com.vgfit.shefit.fragment.premium.redesign;

import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.google.android.exoplayer2.C;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultDataSource;
import com.google.android.exoplayer2.upstream.cache.Cache;
import com.google.android.exoplayer2.upstream.cache.CacheDataSink;
import com.google.android.exoplayer2.upstream.cache.CacheDataSource;
import com.vgfit.shefit.BaseApplication;
import com.vgfit.shefit.databinding.Part4SubscribeBinding;
import com.vgfit.shefit.util.Translate;

import java.util.Arrays;

public class Part4_Fragment extends Fragment {
    private Part4SubscribeBinding binding;
    private DataSource.Factory dataSourceFactory;
    private SimpleExoPlayer player1, player2, player3;

    public static Part4_Fragment newInstance() {

        Bundle args = new Bundle();

        Part4_Fragment fragment = new Part4_Fragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        dataSourceFactory = createDataSource(getContext());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = Part4SubscribeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Context context = getContext();
        if (context != null) {
            player1 = new SimpleExoPlayer.Builder(context).build();
            player2 = new SimpleExoPlayer.Builder(context).build();
            player3 = new SimpleExoPlayer.Builder(context).build();

            binding.playerView1.setPlayer(player1);
            binding.playerView2.setPlayer(player2);
            binding.playerView3.setPlayer(player3);

            // Initialize players with MediaItems (e.g., video URIs)
            MediaSource videoSource1 = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(Uri.parse("file:///android_asset/offerVideo/abs.mp4")));
            MediaSource videoSource2 = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(Uri.parse("file:///android_asset/offerVideo/butt.mp4")));
            MediaSource videoSource3 = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(Uri.parse("file:///android_asset/offerVideo/leag.mp4")));

            player1.setMediaSource(videoSource1);
            player2.setMediaSource(videoSource2);
            player3.setMediaSource(videoSource3);

            // Prepare and start playback
            player1.prepare();
            player2.prepare();
            player3.prepare();

            player1.setRepeatMode(Player.REPEAT_MODE_ONE);
            player2.setRepeatMode(Player.REPEAT_MODE_ONE);
            player3.setRepeatMode(Player.REPEAT_MODE_ONE);

            player1.play();
            player2.play();
            player3.play();
            binding.topTitle.setText(Translate.getValueLine("programs_for_specific_areas", Arrays.asList(1), false));
            binding.shortDescription.setText(Translate.getValue("80+_step_by_step_instructive_videos_for_every_muscle_group"));
            binding.fitButt.setText(Translate.getValue("better_butt"));
            binding.leanLegs.setText(Translate.getValue("toned_legs"));
            binding.tonedAbs.setText(Translate.getValue("toned_abs"));
        }
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private DataSource.Factory createDataSource(Context context) {
        Cache cache = BaseApplication.getCashExoplayer(context);
        DataSource.Factory upstreamFactory = new DefaultDataSource.Factory(context);
        CacheDataSink.Factory cacheWriteDataSinkFactory = new CacheDataSink.Factory().setCache(cache).setFragmentSize(C.LENGTH_UNSET);
        return new CacheDataSource.Factory().setCache(cache).setUpstreamDataSourceFactory(upstreamFactory).setCacheWriteDataSinkFactory(cacheWriteDataSinkFactory).setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // Don't forget to release the players when they are not needed
        if (player1 != null) {
            player1.release();
            player1 = null;
        }
        if (player2 != null) {
            player2.release();
            player2 = null;
        }
        if (player3 != null) {
            player3.release();
            player3 = null;
        }
    }
}
