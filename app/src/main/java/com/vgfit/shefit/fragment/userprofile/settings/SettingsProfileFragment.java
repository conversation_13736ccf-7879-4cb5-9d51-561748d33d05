package com.vgfit.shefit.fragment.userprofile.settings;

import static com.vgfit.shefit.fragment.profile.ProfileFr.setLocale;
import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getCurrentMetricHeight;
import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getCurrentMetricWeight;
import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getImperialHeight;
import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getImperialWeight;
import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getMetricHeight;
import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getMetricWeight;
import static com.vgfit.shefit.fragment.userprofile.settings.CurrentMeasure.AGE;
import static com.vgfit.shefit.fragment.userprofile.settings.CurrentMeasure.GOAL_WEIGHT;
import static com.vgfit.shefit.fragment.userprofile.settings.CurrentMeasure.HEIGHT;
import static com.vgfit.shefit.fragment.userprofile.settings.CurrentMeasure.WEIGHT;
import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.app.Dialog;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.HapticFeedbackConstants;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSnapHelper;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SnapHelper;

import com.androidbolts.topsheet.TopSheetBehavior;
import com.vgfit.shefit.R;
import com.vgfit.shefit.api.service.FillWorkoutPlan;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.authorization.Users;
import com.vgfit.shefit.databinding.FragmentSettingsProfileRBinding;
import com.vgfit.shefit.fragment.personal_plan.structure.PersonalPlanPresenter;
import com.vgfit.shefit.fragment.premium.OnSingleClickListener;
import com.vgfit.shefit.fragment.userprofile.SaveProfileServer;
import com.vgfit.shefit.fragment.userprofile.callback.UpdatedServerProfile;
import com.vgfit.shefit.fragment.userprofile.settings.adapter.PickerAdapter;
import com.vgfit.shefit.fragment.userprofile.settings.model.PickerModel;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.FitnessCalculator;
import com.vgfit.shefit.util.PickerLayoutManager;
import com.vgfit.shefit.util.Translate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class SettingsProfileFragment extends Fragment implements UpdatedServerProfile {
    private FragmentSettingsProfileRBinding binding;
    private SaveProfileServer saveProfileServer;
    private static final String KEY_IS_LBS = "KEY_IS_LBS";
    private static final String KEY_WEIGHT_VALUE = "KEY_WEIGHT_VALUE_";
    private static final String KEY_WEIGHT_VALUE_F = "KEY_WEIGHT_VALUE_F";
    private static final String KEY_HEIGHT_VALUE_F = "KEY_HEIGHT_VALUE_F";
    private static final String KEY_AGE_VALUE = "KEY_AGE_VALUE_";
    private static final String KEY_IS_IN = "KEY_IS_IN";
    private static final String ACTIVE_DAILY_WORKOUT = "active_daily_workout";
    private static final String MONDAY = "mondayDay";
    private static final String TUESDAY = "tuesdayDay";
    private static final String WEDNESDAY = "wednesdayDay";
    private static final String THURSDAY = "thursdayDay";
    private static final String FRIDAY = "fridayDay";
    private static final String SATURDAY = "satDay";
    private static final String SUNDAY = "sunDay";
    private boolean isLbs;
    private boolean isIn;
    private static final int MIN_AGE = 18;
    private static final int MAX_AGE = 120;
    private View view;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private Users users;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        saveProfileServer = new SaveProfileServer(prefsUtilsWtContext, this);
        isLbs = prefsUtilsWtContext.getBooleanPreferenceProfile(KEY_IS_LBS, false);
        isIn = prefsUtilsWtContext.getBooleanPreferenceProfile(KEY_IS_IN, false);
        users = PersonalPlanPresenter.getUserProfile(prefsUtilsWtContext);
    }

    public static SettingsProfileFragment newInstance() {
        Bundle args = new Bundle();
        SettingsProfileFragment fragment = new SettingsProfileFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentSettingsProfileRBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();
        setStatusBar(binding.top.topView);
        //PART 1
        binding.part1.heightBt.setOnClickListener(v -> {
            setVibrate(v);
            setDialogSettings(getContext(), Translate.getValue("height"), HEIGHT);
        });
        binding.part1.weightBt.setOnClickListener(v -> {
            setVibrate(v);
            setDialogSettings(getContext(), Translate.getValue("weight"), WEIGHT);
        });
        binding.part1.ageBt.setOnClickListener(v -> {
            setVibrate(v);
            setDialogSettings(getContext(), Translate.getValue("age"), AGE);
        });
        binding.part1.weightGoalBt.setOnClickListener(v -> {
            setVibrate(v);
            setDialogSettings(getContext(), Translate.getValue("target_weight"), GOAL_WEIGHT);
        });
        defaultProfile(HEIGHT, 0.0f);
        // END PART1x

        //PART 3
        binding.part3.mondayDay.setOnClickListener(v -> {
            setVibrate(v);
            saveStatusDay(MONDAY);
            getStatusDay(MONDAY, binding.part3.mondayDay, binding.part3.mondayDayTxt);
        });
        binding.part3.tuesdayDay.setOnClickListener(v -> {
            setVibrate(v);
            saveStatusDay(TUESDAY);
            getStatusDay(TUESDAY, binding.part3.tuesdayDay, binding.part3.tuesdayDayTxt);
        });
        binding.part3.wednesdayDay.setOnClickListener(v -> {
            setVibrate(v);
            saveStatusDay(WEDNESDAY);
            getStatusDay(WEDNESDAY, binding.part3.wednesdayDay, binding.part3.wednesdayDayTxt);
        });
        binding.part3.thursdayDay.setOnClickListener(v -> {
            setVibrate(v);
            saveStatusDay(THURSDAY);
            getStatusDay(THURSDAY, binding.part3.thursdayDay, binding.part3.thursdayDayTxt);
        });
        binding.part3.fridayDay.setOnClickListener(v -> {
            setVibrate(v);
            saveStatusDay(FRIDAY);
            getStatusDay(FRIDAY, binding.part3.fridayDay, binding.part3.fridayDayTxt);
        });
        binding.part3.satDay.setOnClickListener(v -> {
            setVibrate(v);
            saveStatusDay(SATURDAY);
            getStatusDay(SATURDAY, binding.part3.satDay, binding.part3.satDayTxt);
        });
        binding.part3.sunDay.setOnClickListener(v -> {
            setVibrate(v);
            saveStatusDay(SUNDAY);
            getStatusDay(SUNDAY, binding.part3.sunDay, binding.part3.sunDayTxt);
        });
        getStatusDay(MONDAY, binding.part3.mondayDay, binding.part3.mondayDayTxt);
        getStatusDay(TUESDAY, binding.part3.tuesdayDay, binding.part3.tuesdayDayTxt);
        getStatusDay(WEDNESDAY, binding.part3.wednesdayDay, binding.part3.wednesdayDayTxt);
        getStatusDay(THURSDAY, binding.part3.thursdayDay, binding.part3.thursdayDayTxt);
        getStatusDay(FRIDAY, binding.part3.fridayDay, binding.part3.fridayDayTxt);
        getStatusDay(SATURDAY, binding.part3.satDay, binding.part3.satDayTxt);
        getStatusDay(SUNDAY, binding.part3.sunDay, binding.part3.sunDayTxt);
        //END PART 3

        // PART 4
        binding.part4.bt1.setOnClickListener(view12 -> {
            setVibrate(view12);
            selectorWorkout(2);
            prefsUtilsWtContext.setStringPreferenceProfile(ACTIVE_DAILY_WORKOUT, "2");
            setVibrate(view12);
            getMuscleTypeList();
            setSaveStatus();
        });
        binding.part4.bt2.setOnClickListener(view13 -> {
            setVibrate(view13);
            selectorWorkout(3);
            prefsUtilsWtContext.setStringPreferenceProfile(ACTIVE_DAILY_WORKOUT, "3");
            setVibrate(view13);
            getMuscleTypeList();
            setSaveStatus();
        });
        binding.part4.bt3.setOnClickListener(view14 -> {
            setVibrate(view14);
            selectorWorkout(4);
            prefsUtilsWtContext.setStringPreferenceProfile(ACTIVE_DAILY_WORKOUT, "4");
            setVibrate(view14);
            getMuscleTypeList();
            setSaveStatus();
        });
        binding.part4.bt4.setOnClickListener(view15 -> {
            setVibrate(view15);
            selectorWorkout(1);
            prefsUtilsWtContext.setStringPreferenceProfile(ACTIVE_DAILY_WORKOUT, "1");
            setVibrate(view15);
            getMuscleTypeList();
            setSaveStatus();
        });
        defaultSelected();
        binding.top.backProfile.setOnClickListener(v -> {
            setVibrate(v);
            PersonalPlanPresenter.resetUserProfile(prefsUtilsWtContext, users);
            backToProfile();
        });
        binding.top.saveProfile.setOnClickListener(view1 -> {
            setVibrate(view1);
            saveDaySelected(true);
        });
        binding.nestedScrollView.post(() -> {
            binding.nestedScrollView.fling(0);
            binding.nestedScrollView.fullScroll(View.FOCUS_UP);
            binding.nestedScrollView.scrollTo(0, 0);
        });
        binding.signIn.goSign.setOnClickListener(new OnSingleClickListener() {
            @Override
            public void onSingleClick(View v) {
                setVibrate(v);
                OnlyAccountFragment settingsProfileFragment = OnlyAccountFragment.newInstance();
                FragmentTransaction fragmentTransaction = getParentFragmentManager().beginTransaction();
                fragmentTransaction.addToBackStack("account_only");
                fragmentTransaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
                fragmentTransaction.replace(R.id.root_fragment, settingsProfileFragment);
                fragmentTransaction.commit();
            }
        });
        getMuscleTypeList();
        translateWordsFragment();
        setSaveStatus();
    }

    private void translateWordsFragment() {
        binding.signIn.account.setText(Translate.getValue("account"));
        String email = prefsUtilsWtContext.getStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB);
        binding.signIn.signedWith.setText(Translate.getValue("not_logged_in"));
        if (email != null && !email.isEmpty())
            binding.signIn.signedWith.setText(email);
        binding.signIn.premiumActive.setVisibility(Constant.premium ? View.VISIBLE : View.INVISIBLE);
        //Top
        binding.top.textView16.setText(Translate.getValue("profile"));
        binding.top.saveProfile.setText(Translate.getValue("save"));

        //part 1
        binding.part1.heightInfo.setText(Translate.getValue("height"));
        binding.part1.weightInfo.setText(Translate.getValue("weight"));
        binding.part1.ageInfo.setText(Translate.getValue("age"));
        binding.part1.weightGoalInfo.setText(Translate.getValue("target_weight"));
        binding.part1.bodyParams.setText(Translate.getValue("body_parameters"));

        //part 2
        binding.part2.titleYourPlan.setText(Translate.getValue("your_plan"));
        String wkTime = Translate.getValueLine("workout_time", Collections.singletonList(1), true) + "(" + Translate.getValue("min") + ")";
        binding.part2.workPlanInfo.setText(wkTime);
        binding.part2.stepPlanInfo.setText(Translate.getValue("steps"));
        String waterWk = Translate.getValueLine("water", Collections.singletonList(1), true) + "(LITER)";
        binding.part2.waterPlanInfo.setText(waterWk);
        binding.part2.caloriesPlanInfo.setText(Translate.getValue("calories"));

        //part 3
        binding.part3.workDayPlan.setText(Translate.getValue("your_workout_days"));
        binding.part3.mondayDayTxt.setText(Translate.getValue("monday"));
        binding.part3.tuesdayDayTxt.setText(Translate.getValue("tuesday"));
        binding.part3.wednesdayDayTxt.setText(Translate.getValue("wednesday"));
        binding.part3.thursdayDayTxt.setText(Translate.getValue("thursday"));
        binding.part3.fridayDayTxt.setText(Translate.getValue("friday"));
        String satTxt = Translate.getValue("saturday");
        if (satTxt.length() >= 3) {
            satTxt = satTxt.substring(0, 3);
        }
        binding.part3.satDayTxt.setText(satTxt);
        String sunTxt = Translate.getValue("sunday");
        if (sunTxt.length() >= 3) {
            sunTxt = sunTxt.substring(0, 3);
        }
        binding.part3.sunDayTxt.setText(sunTxt);

        //part 4
        binding.part4.goalTitlePlan.setText(Translate.getValue("fitness_goal"));
        binding.part4.bt1Txt.setText(Translate.getValue("tone_my_body"));
        binding.part4.bt2Txt.setText(Translate.getValue("gain_muscles"));
        binding.part4.bt3Txt.setText(Translate.getValue("be_more_active"));
        binding.part4.bt4Txt.setText(Translate.getValue("lose_weightt"));
    }

    public void setDialogSettings(Context context, String title, CurrentMeasure currentMeasure) {
        final String[] currentValue = {""};
        final float[] tempValueMetric = {0.0f};
        final float[] tempValueImperial = {0.0f};
        final Dialog dialog = new Dialog(context);
        if (dialog.getWindow() != null) {
            if (buildIsAtLeastS()) {
                dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_BLUR_BEHIND);
            }
            dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
            dialog.setContentView(R.layout.dialog_settings);
            RecyclerView rv = dialog.findViewById(R.id.rv);
            TextView titleDialog = dialog.findViewById(R.id.titleDialog);
            titleDialog.setText(title);
            RelativeLayout saveProfile = dialog.findViewById(R.id.saveProfile);
            saveProfile.setOnClickListener(v -> {
                setVibrate(v);
                handleSaveProfile(currentValue, currentMeasure, tempValueMetric, tempValueImperial);
                setSaveStatus();
                dialog.dismiss();
            });

            TextView switchTextOn = dialog.findViewById(R.id.switchTextOn);
            TextView switchTextOff = dialog.findViewById(R.id.switchTextOff);

            ConstraintLayout switchContainer = dialog.findViewById(R.id.constraintLayout2);
            switchContainer.setVisibility(currentMeasure == AGE ? View.GONE : View.VISIBLE);

            PickerAdapter adapter = new PickerAdapter(context, getValue(currentMeasure), rv);
            SnapHelper snapHelper = new LinearSnapHelper();
            snapHelper.attachToRecyclerView(rv);
            PickerLayoutManager pickerLayoutManager = new PickerLayoutManager(context, LinearLayoutManager.HORIZONTAL, false);
            pickerLayoutManager.setChangeAlpha(true);
            rv.setLayoutManager(pickerLayoutManager);
            rv.setAdapter(adapter);
            rv.setHasFixedSize(true);
            SwitchCompat switchProfile = dialog.findViewById(R.id.switchProfile);
            boolean checked = getCheckedStatus(currentMeasure);
            switchProfile.setChecked(checked);
            switchProfile.setOnCheckedChangeListener((buttonView, isChecked) -> {
                setMeasure(currentMeasure, isChecked);
                setVibrate(switchProfile);
                updateSwitchTextColors(isChecked, switchTextOn, switchTextOff);
                adapter.swapList(getValue(currentMeasure));
                defaultProfile(currentMeasure, isImperial(currentMeasure) ? tempValueImperial[0] : tempValueMetric[0]);
                int positionAdapter = adapter.getPositionSelect(switchValue(currentMeasure), isIn && currentMeasure == HEIGHT);
                pickerLayoutManager.scrollToPosition(positionAdapter - 1);
                rv.smoothScrollToPosition(positionAdapter);
                saveUnitsMeasure(currentMeasure, isChecked);
            });
            int positionAdapter = adapter.getPositionSelect(switchValue(currentMeasure), isIn && currentMeasure == HEIGHT);
            pickerLayoutManager.scrollToPosition(positionAdapter - 1);
            rv.smoothScrollToPosition(positionAdapter);

            pickerLayoutManager.setOnScrollStopListener((view1, isUserDragging) -> {
                if (isUserDragging) {
                    handleScrollStop(adapter, currentMeasure, currentValue, tempValueMetric, tempValueImperial, view1);
                }
            });
            TextView textView = dialog.findViewById(R.id.ok_exit);
            textView.setText(Translate.getValue("save"));
            setDefaultSwitch(currentMeasure == HEIGHT, switchTextOn, switchTextOff);
            dialog.show();
        }
    }

    private void handleSaveProfile(String[] currentValue, CurrentMeasure currentMeasure,
                                   float[] tempValueMetric, float[] tempValueImperial) {
        float valueUnit = isImperial(currentMeasure) ? tempValueImperial[0] : tempValueMetric[0];
        String valueSave = valueUnit == 0.0f ? currentValue[0] : String.valueOf(valueUnit);
        saveValueToLabel(valueSave, currentMeasure);
        defaultProfile(currentMeasure, valueUnit);
    }

    // Helper method to handle what happens when scrolling stops
    private void handleScrollStop(@NonNull PickerAdapter adapter, CurrentMeasure currentMeasure, String[] currentValue,
                                  float[] tempValueMetric, float[] tempValueImperial, View view1) {
        tempValueMetric[0] = 0.0f;
        tempValueImperial[0] = 0.0f;
        currentValue[0] = ((TextView) view1).getText().toString();
        try {
            tempValueMetric[0] = Integer.parseInt(currentValue[0]);
        } catch (Exception e) {
            tempValueMetric[0] = 0.0f;
        }
        if (currentMeasure == HEIGHT && isIn) {
            tempValueImperial[0] = adapter.getPositionSelect(currentValue[0], true);
            currentValue[0] = String.valueOf(adapter.getPositionSelect(currentValue[0], true));
        }
        if ((currentMeasure == WEIGHT || currentMeasure == GOAL_WEIGHT) && isLbs) {
            tempValueImperial[0] = adapter.getPositionSelect(currentValue[0], true);
            tempValueMetric[0] = getMetricWeight(tempValueImperial[0]);
            currentValue[0] = String.valueOf(adapter.getPositionSelect(currentValue[0], true));
        }
        adjustMetricImperialValues(currentMeasure, tempValueMetric, tempValueImperial);
    }

    private void adjustMetricImperialValues(CurrentMeasure currentMeasure, float[] tempValueMetric, float[] tempValueImperial) {
        if (tempValueMetric[0] == 0.0f && currentMeasure == HEIGHT) {
            tempValueMetric[0] = getMetricHeight(tempValueImperial[0]);
        }
        if (tempValueImperial[0] == 0.0f && currentMeasure == HEIGHT) {
            tempValueImperial[0] = getImperialHeight(tempValueMetric[0]);
        }
        if (tempValueImperial[0] == 0.0f && (currentMeasure == WEIGHT || currentMeasure == GOAL_WEIGHT)) {
            tempValueImperial[0] = getImperialWeight(tempValueMetric[0]);
        }
    }

    private boolean getCheckedStatus(CurrentMeasure currentMeasure) {
        return (currentMeasure == HEIGHT && isIn)
                || (currentMeasure == WEIGHT && isLbs)
                || (currentMeasure == GOAL_WEIGHT && isLbs);
    }

    private void saveUnitsMeasure(CurrentMeasure currentMeasure, boolean value) {
        if (currentMeasure == WEIGHT || currentMeasure == GOAL_WEIGHT)
            prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_IS_LBS, value);
        else if (currentMeasure == HEIGHT)
            prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_IS_IN, value);
    }

    private void setMeasure(CurrentMeasure currentMeasure, boolean value) {
        if (currentMeasure == HEIGHT)
            isIn = value;
        else if (currentMeasure == WEIGHT || currentMeasure == GOAL_WEIGHT)
            isLbs = value;
    }

    private boolean isImperial(CurrentMeasure currentMeasure) {
        if (currentMeasure == WEIGHT || currentMeasure == GOAL_WEIGHT)
            return prefsUtilsWtContext.getBooleanPreferenceProfile(KEY_IS_LBS, false);
        else if (currentMeasure == HEIGHT)
            return prefsUtilsWtContext.getBooleanPreferenceProfile(KEY_IS_IN, false);
        else
            return false;
    }

    private String switchValue(CurrentMeasure currentMeasure) {
        String valueSelect;
        switch (currentMeasure) {
            case HEIGHT:
                valueSelect = binding.part1.heightTxt.getText().toString();
                break;
            case WEIGHT:
                valueSelect = binding.part1.weightTxt.getText().toString();
                break;
            case GOAL_WEIGHT:
                valueSelect = binding.part1.weightGoalTxt.getText().toString();
                break;
            default:
                valueSelect = binding.part1.ageTxt.getText().toString();
                break;
        }
        return valueSelect;
    }

    private static boolean buildIsAtLeastS() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.S;
    }

    private void saveValueToLabel(String currentValue, CurrentMeasure currentMeasure) {
        if (!currentValue.isEmpty()) {
            float currentValueSave = Float.parseFloat(currentValue);
            boolean isImperial = isImperial(currentMeasure);
            switch (currentMeasure) {
                case HEIGHT: {
                    currentValueSave = getCurrentMetricHeight(Float.parseFloat(currentValue), isImperial);
                    prefsUtilsWtContext.setFloatPreference(KEY_HEIGHT_VALUE_F, currentValueSave);
                    binding.part1.heightTxt.setText(currentValue);
                }
                break;
                case WEIGHT: {
                    currentValueSave = getCurrentMetricWeight(currentValueSave, isImperial);
                    binding.part1.weightTxt.setText(currentValue);
                    prefsUtilsWtContext.setFloatPreference(KEY_WEIGHT_VALUE_F + "1", currentValueSave);
                }
                break;
                case GOAL_WEIGHT: {
                    currentValueSave = getCurrentMetricWeight(currentValueSave, isImperial);
                    binding.part1.weightGoalTxt.setText(currentValue);
                    prefsUtilsWtContext.setFloatPreference(KEY_WEIGHT_VALUE_F + "2", currentValueSave);
                }
                break;
                case AGE: {
                    int age = (int) Float.parseFloat(currentValue);
                    binding.part1.ageTxt.setText(currentValue);
                    prefsUtilsWtContext.setIntegerPreference(KEY_AGE_VALUE, age);
                }
                break;

            }
        }
    }

    private void defaultProfile(CurrentMeasure currentMeasure, float value) {
        float valueHeight = prefsUtilsWtContext.getFloatPreference(KEY_HEIGHT_VALUE_F, 150f);
        int valueAge = prefsUtilsWtContext.getIntegerPreference(KEY_AGE_VALUE, 25);

        float currentWeight = prefsUtilsWtContext.getFloatPreference(KEY_WEIGHT_VALUE_F + "1", 60f);
        float goalWeight = prefsUtilsWtContext.getFloatPreference(KEY_WEIGHT_VALUE_F + "2", 50f);

        int currentWeightInt = prefsUtilsWtContext.getIntegerPreference(KEY_WEIGHT_VALUE + "1", 0);
        int goalWeightInt = prefsUtilsWtContext.getIntegerPreference(KEY_WEIGHT_VALUE + "2", 0);

        //this for old user who use INT value for weight/goal_weight
        // and want reset this and never use this
        if (currentWeightInt != 0 && goalWeightInt != 0) {
            currentWeight = currentWeightInt;
            goalWeight = goalWeightInt;
            resetIntToFloat(currentWeightInt, goalWeightInt);
        }
        boolean isImperial = isImperial(currentMeasure);
        if (value != 0.0)
            switch (currentMeasure) {
                case HEIGHT:
                    valueHeight = getCurrentMetricHeight(value, isImperial);
                    break;
                case WEIGHT:
                    currentWeight = getCurrentMetricWeight(value, isImperial);
                    break;
                case GOAL_WEIGHT:
                    goalWeight = getCurrentMetricWeight(value, isImperial);
                    break;
                default:
                    break;
            }
        String valueAgeTxt = String.valueOf(valueAge);
        String currentWeightTxt = String.valueOf(Math.round(currentWeight));
        String goalWeightTxt = String.valueOf(Math.round(goalWeight));
        String valueHeightTxt = String.valueOf(Math.round(valueHeight));
        if (isIn) {
            valueHeight = Math.round(getImperialHeight(valueHeight));
            int ft = (int) valueHeight / 12;
            int inch = (int) (valueHeight - (ft * 12));
            valueHeightTxt = ft + "'" + inch + "\"";
        }
        if (isLbs) {
            currentWeightTxt = String.valueOf(Math.round(getImperialWeight(currentWeight)));
            goalWeightTxt = String.valueOf(Math.round(getImperialWeight(goalWeight)));
        }
        binding.part1.heightTxt.setText(valueHeightTxt);
        binding.part1.ageTxt.setText(valueAgeTxt);
        binding.part1.weightTxt.setText(currentWeightTxt);
        binding.part1.weightGoalTxt.setText(goalWeightTxt);
    }

    private void resetIntToFloat(int currentWeightInt, int goalWeightInt) {
        prefsUtilsWtContext.setIntegerPreference(KEY_WEIGHT_VALUE + "1", 0);
        prefsUtilsWtContext.setIntegerPreference(KEY_WEIGHT_VALUE + "2", 0);
        prefsUtilsWtContext.setFloatPreference(KEY_HEIGHT_VALUE_F + "1", currentWeightInt);
        prefsUtilsWtContext.setFloatPreference(KEY_HEIGHT_VALUE_F + "2", goalWeightInt);
    }

    private void setVibrate(View view) {
        view.setHapticFeedbackEnabled(true);
        view.performHapticFeedback(HapticFeedbackConstants.KEYBOARD_TAP);
    }

    private void updateSwitchTextColors(boolean isConditionTrue, TextView switchTextOn, TextView switchTextOff) {
        if (getContext() != null) {
            int colorOn = ContextCompat.getColor(getContext(), isConditionTrue ? R.color.black : R.color.text_color_lime);
            int colorOff = ContextCompat.getColor(getContext(), isConditionTrue ? R.color.text_color_lime : R.color.black);

            switchTextOn.setTextColor(colorOn);
            switchTextOff.setTextColor(colorOff);
        }
    }

    private void setDefaultSwitch(boolean isHeight, TextView switchTextOn, TextView switchTextOff) {
        if (isHeight) {
            switchTextOn.setText(Translate.getValue("cm"));
        } else {
            switchTextOn.setText(Translate.getValue("kg"));
            switchTextOff.setText(Translate.getValue("lbs"));
        }
        if (isHeight) {
            updateSwitchTextColors(isIn, switchTextOn, switchTextOff);
        } else {
            updateSwitchTextColors(isLbs, switchTextOn, switchTextOff);
        }
    }

    private List<PickerModel> getValue(CurrentMeasure currentMeasure) {
        switch (currentMeasure) {
            case HEIGHT:
                return getDataHeightValue();
            case WEIGHT:
            case GOAL_WEIGHT:
                return getDataWeightValue();
            default:
                return getDataAge();
        }
    }

    private List<PickerModel> getDataAge() {
        List<PickerModel> data = new ArrayList<>();
        for (int i = MIN_AGE; i <= MAX_AGE; i++) {
            data.add(new PickerModel(String.valueOf(i), i));
        }
        return data;
    }

    private List<PickerModel> getDataHeightValue() {
        if (isIn)
            return getDataFt();
        else
            return getData(300);
    }

    private List<PickerModel> getDataWeightValue() {
        if (isLbs)
            return getData(441);
        else
            return getData(200);
    }

    private List<PickerModel> getData(int count) {
        List<PickerModel> data = new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            data.add(new PickerModel(String.valueOf(i), i));
        }
        return data;
    }

    private List<PickerModel> getDataFt() {
        List<PickerModel> data = new ArrayList<>();
        for (int i = 0; i <= 120; i++) {
            int ft = i / 12;
            int inch = (i - (ft * 12));
            String valueFt = ft + "'" + inch + "\"";
            data.add(new PickerModel(valueFt, i));
        }
        return data;
    }

    private void saveStatusDay(String keyMeal) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyMeal, false);
        prefsUtilsWtContext.setBooleanPreference(keyMeal, !isSelected);
        saveDaySelected(false);
        setSaveStatus();
    }

    private boolean getStatusDay(String keyMeal, RelativeLayout layout, TextView textView) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyMeal, layout == binding.part3.mondayDay || layout == binding.part3.wednesdayDay || layout == binding.part3.fridayDay);
        if (getContext() != null) {
            layout.setBackgroundResource(isSelected ? R.drawable.background_meal_ : R.drawable.background_meal);
            textView.setTextColor(isSelected ? ContextCompat.getColor(getContext(), R.color.text_color_lime) : ContextCompat.getColor(getContext(), R.color.black));
        }
        return isSelected;
    }

    public void saveDaySelected(boolean saveProfile) {
        ArrayList<Boolean> listDay = new ArrayList<>();
        listDay.add(getStatusDay(MONDAY, binding.part3.mondayDay, binding.part3.mondayDayTxt));
        listDay.add(getStatusDay(TUESDAY, binding.part3.tuesdayDay, binding.part3.tuesdayDayTxt));
        listDay.add(getStatusDay(WEDNESDAY, binding.part3.wednesdayDay, binding.part3.wednesdayDayTxt));
        listDay.add(getStatusDay(THURSDAY, binding.part3.thursdayDay, binding.part3.thursdayDayTxt));
        listDay.add(getStatusDay(FRIDAY, binding.part3.fridayDay, binding.part3.fridayDayTxt));
        listDay.add(getStatusDay(SATURDAY, binding.part3.satDay, binding.part3.satDayTxt));
        listDay.add(getStatusDay(SUNDAY, binding.part3.sunDay, binding.part3.sunDayTxt));

        int countDay = 0;
        StringBuilder daySelected = new StringBuilder();
        for (int i = 0; i < listDay.size(); i++) {
            boolean isSelectedDay = listDay.get(i);
            if (isSelectedDay) {
                if (daySelected.length() > 0) daySelected.append(",");
                daySelected.append(i);
                countDay++;
            }
        }
        prefsUtilsWtContext.setStringPreferenceProfile("workout_days", daySelected.toString());
        if (countDay < 2) {
            topSheet(Translate.getValue("you_must_select_minimum_2_and_maximum_6_days"));
        } else if (countDay > 6) {
            topSheet(Translate.getValue("you_must_select_minimum_2_and_maximum_6_days"));
        } else if (saveProfile) {
            saveProfileServer.saveFullProfileToServer(PersonalPlanPresenter.getUserProfile(prefsUtilsWtContext));
        }
    }

    private void backToProfile() {
        FragmentManager fm = getParentFragmentManager();
        fm.popBackStack(null, FragmentManager.POP_BACK_STACK_INCLUSIVE);
    }

    public void selectorWorkout(int numb) {
        binding.part4.bt1.setSelected(numb == 2);
        binding.part4.bt2.setSelected(numb == 3);
        binding.part4.bt3.setSelected(numb == 4);
        binding.part4.bt4.setSelected(numb == 1);
        if (getContext() != null) {
            binding.part4.bt1.setBackgroundResource(R.drawable.background_meal);
            binding.part4.bt1Txt.setTextColor(ContextCompat.getColor(getContext(), R.color.black));
            binding.part4.bt2.setBackgroundResource(R.drawable.background_meal);
            binding.part4.bt2Txt.setTextColor(ContextCompat.getColor(getContext(), R.color.black));
            binding.part4.bt3.setBackgroundResource(R.drawable.background_meal);
            binding.part4.bt3Txt.setTextColor(ContextCompat.getColor(getContext(), R.color.black));
            binding.part4.bt4.setBackgroundResource(R.drawable.background_meal);
            binding.part4.bt4Txt.setTextColor(ContextCompat.getColor(getContext(), R.color.black));
            switch (numb) {
                case 2:
                    binding.part4.bt1.setBackgroundResource(R.drawable.background_meal_);
                    binding.part4.bt1Txt.setTextColor(ContextCompat.getColor(getContext(), R.color.text_color_lime));
                    break;
                case 3:
                    binding.part4.bt2.setBackgroundResource(R.drawable.background_meal_);
                    binding.part4.bt2Txt.setTextColor(ContextCompat.getColor(getContext(), R.color.text_color_lime));
                    break;
                case 4:
                    binding.part4.bt3.setBackgroundResource(R.drawable.background_meal_);
                    binding.part4.bt3Txt.setTextColor(ContextCompat.getColor(getContext(), R.color.text_color_lime));
                    break;
                case 1:
                    binding.part4.bt4.setBackgroundResource(R.drawable.background_meal_);
                    binding.part4.bt4Txt.setTextColor(ContextCompat.getColor(getContext(), R.color.text_color_lime));
                    break;
                default:
                    break;
            }
        }
    }

    public void defaultSelected() {
        String selected = prefsUtilsWtContext.getStringPreferenceProfile(ACTIVE_DAILY_WORKOUT);
        selectorWorkout(selected != null ? Integer.parseInt(selected) : 1);
    }

    public void getMuscleTypeList() {

        int selected;
        int time = 30;
        String selTemp = prefsUtilsWtContext.getStringPreferenceProfile(ACTIVE_DAILY_WORKOUT);
        if (selTemp == null)
            selected = 1;
        else selected = Integer.parseInt(selTemp);
        if (selected == 1) {
            time = 28;
        } else if (selected == 2) {
            time = 50;
        } else if (selected == 3) {
            time = 55;
        }
        String finalTime = time + "";
        String finalCalories = String.valueOf(FitnessCalculator.getDailyCalories(getContext()));
        String finalWater = String.valueOf(FitnessCalculator.getDailyWater(getContext()));
        int finalSteps = FitnessCalculator.getDailySteps(getContext());
        binding.part2.wkTimeTxt.setText(finalTime);
        binding.part2.wkStepsTxt.setText(String.valueOf(finalSteps));
        binding.part2.caloriesWaterTxt.setText(finalCalories);
        binding.part2.wkWaterTxt.setText(finalWater);
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    private void topSheet(String message) {
        binding.messageAlert.setText(message);
        binding.messageAlert.setSelected(true);
        final Handler handler = new Handler(Looper.getMainLooper());
        TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_EXPANDED);
        binding.topSheet.setOnClickListener(v -> TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_COLLAPSED));
        handler.postDelayed(() -> TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_COLLAPSED), 3000);
    }

    private void setSaveStatus() {
        boolean isModifiedUser = PersonalPlanPresenter.isModifiedUserProfile(users, prefsUtilsWtContext);
        binding.top.saveProfile.setVisibility(isModifiedUser ? View.VISIBLE : View.INVISIBLE);
        binding.top.backProfile.setVisibility(isModifiedUser ? View.INVISIBLE : View.VISIBLE);
    }

    @Override
    public void onResume() {
        super.onResume();
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
                PersonalPlanPresenter.resetUserProfile(prefsUtilsWtContext, users);
                backToProfile();
                return true; // pretend we've processed it
            } else
                return false; // pass on to be processed as normal
        });
    }

    @Override
    public void updatedServerProfile(boolean valid) {
        if (getContext() != null && valid) {
            new FillWorkoutPlan(getContext(), isFilled -> {
                backToProfile();
                setLocale(getActivity());
            }, true).startFillData();
        }
    }
}
