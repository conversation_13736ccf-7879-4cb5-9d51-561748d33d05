package com.vgfit.shefit.fragment.nutrition.adapter;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.ItemOneMealInDayRBinding;
import com.vgfit.shefit.fragment.nutrition.callbacks.NutritionItemAccessed;
import com.vgfit.shefit.fragment.nutrition.model.NutritionDaySelected;
import com.vgfit.shefit.realm.MealByDay;
import com.vgfit.shefit.util.Translate;

import java.util.ArrayList;

public class AdapterNutritionOneDayR extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final ArrayList<MealByDay> listItemNutrition;
    private final NutritionItemAccessed listener;
    private ArrayList<String> listMealManifest;
    private final ArrayList<NutritionDaySelected> listSelectedMeal;

    private int posSelected = -1;

    public AdapterNutritionOneDayR(ArrayList<MealByDay> listItemNutrition, NutritionItemAccessed listener) {
        this.listItemNutrition = new ArrayList<>();
        this.listItemNutrition.addAll(listItemNutrition);
        this.listSelectedMeal = new ArrayList<>();

        for (MealByDay item : listItemNutrition) {
            listSelectedMeal.add(new NutritionDaySelected(false, item));
        }
        this.listener = listener;
        setListMealManifest();

    }

    public void selectItemNutrition(int position) {
        if (posSelected != -1)
            listSelectedMeal.get(posSelected).setSelected(false);
        posSelected = position;
        listSelectedMeal.get(position).setSelected(true);
        String nameMeal = "";
        if (position < listMealManifest.size())
            nameMeal = listMealManifest.get(position);
        listener.onItemClick(listItemNutrition.get(position).getMeal(), nameMeal, position);
        notifyDataSetChanged();
    }

    private void setListMealManifest() {
        listMealManifest = new ArrayList<>();
        listMealManifest.add(Translate.getValue("breakfast").replaceAll(" ", ""));
        listMealManifest.add(Translate.getValue("morning_snack").replaceAll(" ", ""));
        listMealManifest.add(Translate.getValue("lunch").replaceAll(" ", ""));
        listMealManifest.add(Translate.getValue("afternoon_snack").replaceAll(" ", ""));
        listMealManifest.add(Translate.getValue("dinner").replaceAll(" ", ""));
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ItemOneMealInDayRBinding binding = ItemOneMealInDayRBinding.inflate(inflater, parent, false);
        return new ItemViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder rawHolder, final int position) {
        ItemViewHolder holder = (ItemViewHolder) rawHolder;
        boolean isSelected = listSelectedMeal.get(position).isSelected;
        holder.itemView.setTag(position);
        if (position < listMealManifest.size())
            holder.nameManifest.setText(listMealManifest.get(position));
        holder.itemView.setOnClickListener(v -> {
            setVibrate(v);
            selectItemNutrition(position);
        });
        if (holder.itemView.getContext() != null)
            if (isSelected) {
                holder.itemBackground.setBackground(holder.itemView.getContext().getDrawable(R.drawable.oval_item_nutrition));
                holder.nameManifest.setTextColor(holder.itemView.getContext().getResources().getColor(R.color.text_color_lime));
                holder.nameManifest.getPaint().setUnderlineText(false);
            } else {
                holder.itemBackground.setBackground(null);
                holder.nameManifest.setTextColor(holder.itemView.getContext().getResources().getColor(R.color.black));
                holder.nameManifest.getPaint().setUnderlineText(true);
            }

    }

    @Override
    public int getItemCount() {
        return listItemNutrition.size();
    }

    public static class ItemViewHolder extends RecyclerView.ViewHolder {
        TextView nameManifest;
        LinearLayout itemBackground;

        public ItemViewHolder(ItemOneMealInDayRBinding binding) {
            super(binding.getRoot());
            nameManifest = binding.nameManifest;
            itemBackground = binding.rlNutritionMealItem;
        }
    }
}