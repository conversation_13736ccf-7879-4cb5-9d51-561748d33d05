package com.vgfit.shefit.fragment.more.reminder;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;

import com.vgfit.shefit.fragment.more.service.SharedPreferencesData;

public class BootReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        SharedPreferencesData sharedPreferencesData = new SharedPreferencesData(context);
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context); // NOSONAR

        String keyDaily = sharedPreferencesData.getDAILY_REMINDER_KEY();
        String keyInactivity = sharedPreferencesData.getINACTIVITY_KEY();
        boolean isCheckedDaily = sharedPreferences.getBoolean(sharedPreferencesData.getDAILY_REMINDER_SWITCH_KEY(), false);
        boolean isCheckedInactivity = sharedPreferences.getBoolean(sharedPreferencesData.getINACTIVITY_SWITCH_KEY(), false);

//        DailyAlarmReceiver dailyAlarmReceiver = new DailyAlarmReceiver();
        NotificationSpecialActions notificationSpecialActions = new NotificationSpecialActions(context, sharedPreferencesData);

        if (isCheckedDaily) notificationSpecialActions.dailyReminderSpecialActions(keyDaily);
        if (isCheckedInactivity) notificationSpecialActions.inactivitySpecialActions(keyInactivity);
    }
}