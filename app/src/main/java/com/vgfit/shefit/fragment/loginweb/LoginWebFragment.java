package com.vgfit.shefit.fragment.loginweb;


import static com.vgfit.shefit.api.loginweb.LoginWebService.getUserInfo;
import static com.vgfit.shefit.fragment.profile.ProfileFr.setLocale;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Rect;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.TextWatcher;
import android.text.method.LinkMovementMethod;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.credentials.CreateCredentialResponse;
import androidx.credentials.CreatePasswordRequest;
import androidx.credentials.Credential;
import androidx.credentials.CredentialManager;
import androidx.credentials.CredentialManagerCallback;
import androidx.credentials.GetCredentialRequest;
import androidx.credentials.GetCredentialResponse;
import androidx.credentials.GetPasswordOption;
import androidx.credentials.PasswordCredential;
import androidx.credentials.exceptions.CreateCredentialException;
import androidx.credentials.exceptions.GetCredentialException;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.androidbolts.topsheet.TopSheetBehavior;
import com.vgfit.shefit.R;
import com.vgfit.shefit.api.loginweb.LoginWebService;
import com.vgfit.shefit.api.loginweb.callback.ForgotPasswordResponse;
import com.vgfit.shefit.api.loginweb.callback.LoginUserInfoResponse;
import com.vgfit.shefit.api.loginweb.callback.LoginWebResponse;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.LoginWebProfileRBinding;
import com.vgfit.shefit.fragment.loginweb.model.TimeResendElapsed;
import com.vgfit.shefit.fragment.workouts.downloader.CheckInternetConnection;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.ElapsedTimerUtil;
import com.vgfit.shefit.util.TextUtil;
import com.vgfit.shefit.util.Translate;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.concurrent.Executor;

public class LoginWebFragment extends Fragment implements LoginWebResponse, LoginUserInfoResponse, ForgotPasswordResponse {
    private LoginWebProfileRBinding binding;
    boolean isValidMail = false;
    private ElapsedTimerUtil elapsedTimerUtil;
    private static final String INVALID_EMAIL_ADDRESS = "invalid_email_address";
    private boolean lunchFirstTime = false;
    private boolean isKeyboardCurrentlyVisible = false;
    private CredentialManager credentialManager;
    private Executor mainExecutor;
    private String emailDefault = "<EMAIL>";
    private String passwordDefault = "pswd";

    public static LoginWebFragment newInstance(boolean lunchFirstTime) {
        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        LoginWebFragment fragment = new LoginWebFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        elapsedTimerUtil = new ElapsedTimerUtil();
        Bundle arg = getArguments();
        if (arg != null)
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        initTheme();
    }

    private void initTheme() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && getActivity() != null) {
            getActivity().getWindow().setDecorFitsSystemWindows(false);
        } else {
            setThemeOld();
        }
    }

    private void setThemeOld() {
        if (getActivity() != null) {
            getActivity().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
            getActivity().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN);
            getActivity().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
            getActivity().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            getActivity().getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = LoginWebProfileRBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        binding.mailEdit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                //empty
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                //empty
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (binding.mailEdit.getText().toString().matches("[a-zA-Z0-9._-]+@[a-z]+\\.+[a-z]+") && s.length() > 0) {
                    binding.mailEdit.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_mail_login, 0, R.drawable.ic_checkmail, 0);
                    isValidMail = true;
                } else {
                    binding.mailEdit.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_mail_login, 0, 0, 0);
                    isValidMail = false;
                }
            }
        });
        InputFilter noSpaceFilter = (source, start, end, dest, dstart, dend) -> {
            if (source != null && source.toString().contains(" ")) {
                return source.toString().replace(" ", "");
            }
            return null;
        };
        binding.passwordEdit.setFilters(new InputFilter[] { noSpaceFilter });
        binding.passwordEdit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                //empty
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                //empty
            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (editable.length() > 0) {
                    binding.passwordEdit.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_lock_password, 0, R.drawable.ic_showpassword, 0);
                } else {
                    binding.passwordEdit.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_lock_password, 0, 0, 0);
                }
            }
        });
        binding.passwordEdit.setOnTouchListener((v, event) -> passwordEvent(event));
        binding.forgotPassword.setOnClickListener(view13 -> {
            setVibrate(view13);
            logicForgot();
        });
        binding.closeButton.setOnClickListener(view12 -> {
            setVibrate(view12);
            if (getContext() != null)
                hideKeyboard(getContext(), view12);
            toBack();
        });
        binding.signIn.setOnClickListener(view1 -> {
            CheckInternetConnection ch = new CheckInternetConnection(getContext());
            if(ch.isConnectingToInternet()) {
                setVibrate(view1);
                logicSignIn();
            }else {
                Toast.makeText(getContext(), "No internet connection", Toast.LENGTH_SHORT).show();
            }
        });
        defaultValue();
        translateString();
        setupKeyboardListener();
        requestSavedCredentials();
    }

    private void requestSavedCredentials() {
        credentialManager = CredentialManager.create(requireContext());
        mainExecutor = ContextCompat.getMainExecutor(requireContext());

        GetPasswordOption passwordOption = new GetPasswordOption();
        GetCredentialRequest request = new GetCredentialRequest.Builder()
                .addCredentialOption(passwordOption)
                .build();

        credentialManager.getCredentialAsync(
                requireActivity(), // activity-based context
                request,
                null,
                mainExecutor,
                new CredentialManagerCallback<GetCredentialResponse, GetCredentialException>() {
                    @Override
                    public void onResult(GetCredentialResponse result) {
                        Credential credential = result.getCredential();
                        if (credential instanceof PasswordCredential) {
                            PasswordCredential passwordCred = (PasswordCredential) credential;
                            String savedEmail = passwordCred.getId();
                            String savedPass = passwordCred.getPassword();

                            binding.mailEdit.setText(savedEmail);
                            binding.passwordEdit.setText(savedPass);
                            emailDefault = savedEmail;
                            passwordDefault = savedPass;
                        }
                    }

                    @Override
                    public void onError(@NonNull GetCredentialException e) {
                        //not used
                    }
                }
        );
    }

    private void setupKeyboardListener() {
        if (getContext() != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // API >= 30
                ViewCompat.setOnApplyWindowInsetsListener(binding.mainContainer, (v, insets) -> {
                    boolean isKeyboardVisible = insets.isVisible(WindowInsetsCompat.Type.ime());
                    int containerPx = binding.mainContainer.getHeight() - insets.getInsets(WindowInsetsCompat.Type.ime()).bottom;
                    if (containerPx > 0 && getContext() != null) {
                        int containerDp = pxToDp(getContext(), containerPx);
                        adjustMainContainer(isKeyboardVisible, insets.getInsets(WindowInsetsCompat.Type.ime()).bottom, containerDp);
                    }
                    return WindowInsetsCompat.CONSUMED;
                });

            } else {
                // API < 30
                binding.mainContainer.getViewTreeObserver().addOnGlobalLayoutListener(() -> {
                    Rect r = new Rect();
                    binding.mainContainer.getWindowVisibleDisplayFrame(r);
                    int screenHeight = binding.mainContainer.getRootView().getHeight();
                    int keypadHeight = screenHeight - r.bottom;

                    int containerPx = screenHeight - keypadHeight;
                    if (containerPx > 0 && getContext() != null) {
                        int containerDp = pxToDp(getContext(), containerPx);
                        boolean isKeyboardVisible = keypadHeight > screenHeight * 0.15;
                        adjustMainContainer(isKeyboardVisible, keypadHeight, containerDp);
                    }
                });
            }
        }
    }

    public static int pxToDp(Context context, int px) {
        float density = context.getResources().getDisplayMetrics().density;
        return Math.round(px / density);
    }

    private void adjustMainContainer(boolean isKeyboardVisible, int keyboardHeight, int containerDp) {
        int lineTitle = binding.infoLogin.getLineCount();
        int minContainerHeightDP = 481;
        if (lineTitle > 2) minContainerHeightDP = 505;
        float perGuideInitialHeader = 0.25f;
        float perGuideFinalHeader = perGuideInitialHeader;
        float constDpPercent = 0.00375f;
        int differenceContainerHeight = minContainerHeightDP - containerDp;
        if (differenceContainerHeight > 0) {
            perGuideInitialHeader = perGuideInitialHeader - (differenceContainerHeight * constDpPercent);
            if (perGuideInitialHeader < 0) perGuideInitialHeader = 0;
        }
        if (isKeyboardCurrentlyVisible != isKeyboardVisible) {
            isKeyboardCurrentlyVisible = isKeyboardVisible;

            ConstraintLayout.LayoutParams paramsGuideBottom = (ConstraintLayout.LayoutParams) binding.guidelineBottom.getLayoutParams();
            ConstraintLayout.LayoutParams paramsGuideHeader = (ConstraintLayout.LayoutParams) binding.guidelineTopHeader.getLayoutParams();

            if (isKeyboardVisible) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    ViewGroup.LayoutParams params = binding.mainContainer.getLayoutParams();
                    params.height = binding.mainContainer.getHeight() - keyboardHeight;
                    binding.mainContainer.setLayoutParams(params);
                }
                binding.infoTerms.setVisibility(View.GONE);
                paramsGuideBottom.guidePercent = 1.0f;
                paramsGuideHeader.guidePercent = perGuideInitialHeader;
            } else {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    ViewGroup.LayoutParams params = binding.mainContainer.getLayoutParams();
                    params.height = ViewGroup.LayoutParams.MATCH_PARENT;
                    binding.mainContainer.setLayoutParams(params);
                }
                binding.infoTerms.setVisibility(View.VISIBLE);
                paramsGuideBottom.guidePercent = 0.9f;
                paramsGuideHeader.guidePercent = perGuideFinalHeader;
            }

            binding.guidelineBottom.setLayoutParams(paramsGuideBottom);
            binding.guidelineTopHeader.setLayoutParams(paramsGuideHeader);
        }
    }

    private boolean passwordEvent(MotionEvent event) {
        final int DRAWABLE_RIGHT = 2;
        if (event.getAction() == MotionEvent.ACTION_UP &&
                binding.passwordEdit.getCompoundDrawables()[DRAWABLE_RIGHT] != null &&
                event.getRawX() >= (binding.passwordEdit.getRight() - binding.passwordEdit.getCompoundDrawables()[DRAWABLE_RIGHT].getBounds().width())) {
            // Toggle password visibility
            if (binding.passwordEdit.getInputType() == InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD) {
                binding.passwordEdit.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD);
                binding.passwordEdit.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_lock_password, 0, R.drawable.ic_showpassword, 0); // Change to "eye off" drawable
            } else {
                binding.passwordEdit.setInputType(InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD);
                binding.passwordEdit.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_lock_password, 0, R.drawable.ic_hidepassword, 0); // Change to "eye on" drawable
            }
            // Move the cursor to the end of the text
            binding.passwordEdit.setSelection(binding.passwordEdit.getText().length());
            return true;
        }
        return false;
    }

    private void logicForgot() {
        String mail = binding.mailEdit.getText().toString();
        if (mail.isEmpty()) {
            topSheet(Translate.getValue("fill_the_email_first"));
        } else if (!isValidMail) {
            topSheet(Translate.getValue(INVALID_EMAIL_ADDRESS));
        } else {
            LoginWebService.sendForgotPassword(mail, true, this);
        }
    }

    private void logicSignIn() {
        String mail = binding.mailEdit.getText().toString();
        String password = binding.passwordEdit.getText().toString();
        if (mail.isEmpty()) {
            topSheet(Translate.getValue("email_cannot_be_empty"));
        } else if (!isValidMail) {
            topSheet(Translate.getValue("please_enter_a_valid_email_address"));
        } else if (password.isEmpty()) {
            topSheet(Translate.getValue("password_cannot_be_empty"));
        } else {
            LoginWebService.getLoginWeb(new PrefsUtilsWtContext(getContext()), mail, password, this);
        }
    }

    private void toBack() {
        if (getContext() != null) {
            FragmentManager fm = getParentFragmentManager();
            if (!fm.isStateSaved())
                fm.popBackStack("frag_login_web", FragmentManager.POP_BACK_STACK_INCLUSIVE);
        }
    }

    private void translateString() {
        binding.signInTxt.setText(Translate.getValue("sign_in"));
        binding.forgotPassword.setText(Translate.getValue("forgot_password"));
        String tag = "%@";
        String infoLoginTxt = Translate.getValue("sign_in_information").replace(tag, getResources().getString(R.string.app_name));
        binding.infoLogin.setText(TextUtil.colorizeSubString(infoLoginTxt, ContextCompat.getColor(requireContext(), R.color.black)));
        binding.mailEdit.setHint(Translate.getValue("email"));
        binding.passwordEdit.setHint(Translate.getValue("password"));
        String infoTxt = Translate.getValue("by_signing_in,_you_agree_to_our_terms_of_use_and_acknowledge_our_privacy_policy");
        binding.titleAlert.setText(Translate.getValue("warning"));
        binding.infoTerms.setText(TextUtil.getText(infoTxt, getContext()));
        binding.infoTerms.setMovementMethod(LinkMovementMethod.getInstance());
    }

    @Override
    public void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }

    @Override
    public void onStop() {
        super.onStop();
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void onResume() {
        super.onResume();
        try {
            onMessageEvent(new TimeResendElapsed(Constant.timeElapsed));
        } catch (Exception ignored) {
            //ignored
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(TimeResendElapsed event) {
        if (event != null && elapsedTimerUtil != null) {
            long timeElapsed = event.getSecondElapse();
            binding.timeElapsedBigTxt.setText(elapsedTimerUtil.getSecondElapsed(timeElapsed));
            elapsedTimerUtil.isShowElapsed(binding.timeElapsedBigTxt, timeElapsed);
            elapsedTimerUtil.setValidResend(binding.forgotPassword, timeElapsed);
            elapsedTimerUtil.setTimeElapsed(timeElapsed);
        }
    }

    private void defaultValue() {
        binding.passwordEdit.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_lock_password, 0, 0, 0);
        binding.mailEdit.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_mail_login, 0, 0, 0);
    }

    @Override
    public void responseLoginError(String detailMessage) {
        topSheet(detailMessage);
    }

    @Override
    public void responseLoginFailed() {
        emailDefault = "<EMAIL>";
        passwordDefault = "pswd";
    }

    @Override
    public void responseLoginSuccess() {
        if (getContext() != null) {
            getUserInfo(new PrefsUtilsWtContext(getContext()), this);
        }
    }

    @Override
    public void responseUserInfoSuccess() {
        String email = binding.mailEdit.getText().toString();
        String pass = binding.passwordEdit.getText().toString();
        if (emailDefault.equals(email) && passwordDefault.equals(pass)) {
            closeFragment();
            return;
        }
        saveCredentials(email, pass);

    }

    private void closeFragment() {
        if (getContext() != null && lunchFirstTime)
            toBack();
        else
            setLocale(getActivity());
    }

    @Override
    public void responseUserInfoFailed() {
        //empty
    }

    private void topSheet(String message) {
        binding.messageAlert.setText(message);
        final Handler handler = new Handler(Looper.getMainLooper());
        TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_EXPANDED);
        binding.topSheet.setOnClickListener(v -> TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_COLLAPSED));
        handler.postDelayed(() -> TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_COLLAPSED), 3000);
    }

    @Override
    public void responseForgotPasswordMessage(String message, boolean isWait) {
        if (getActivity() != null) {
            topSheet(message);
            if (isWait)
                Constant.startCounterResend();
        }
    }

    @Override
    public void serverEndResponse(boolean isEnd) {
        setProgressServer(!isEnd);
    }

    @Override
    public void successForgotPassword(boolean openDialog) {
        if (getActivity() != null) {
            Constant.startCounterResend();
            if (openDialog)
                openDialogForgot();
        }
    }

    public void openDialogForgot() {
        if (getContext() != null) {
            final Dialog d = new Dialog(getContext(), R.style.BottomDialog);
            if (d.getWindow() != null) {
                d.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
                d.setContentView(R.layout.dialog_forgot_password);
                d.setCanceledOnTouchOutside(true);
                d.setCancelable(true);

                Window window = d.getWindow();
                WindowManager.LayoutParams wlp = window.getAttributes();
                wlp.gravity = Gravity.BOTTOM;
                wlp.flags &= ~WindowManager.LayoutParams.FLAG_DIM_BEHIND;
                window.setAttributes(wlp);
                TextView titleView = d.findViewById(R.id.footer_txt);
                titleView.setText(Translate.getValue("check_your_email_inbox"));
                TextView textForgot = d.findViewById(R.id.textForgot);
                String textMessage = Translate.getValue("reset_password_info");
                textForgot.setText(TextUtil.getText("<c>", textMessage, getContext()));
                textForgot.setMovementMethod(LinkMovementMethod.getInstance());
                final TextView btnOk = d.findViewById(R.id.btnDone);
                TextView timeElapsedTxt = d.findViewById(R.id.timeElapsedTxt);
                btnOk.setText(Translate.getValue("resend"));
                btnOk.setOnClickListener(arg0 -> {
                    setVibrate(arg0);
                    String mail = binding.mailEdit.getText().toString();
                    if (!isValidMail) {
                        topSheet(Translate.getValue(INVALID_EMAIL_ADDRESS));
                    } else {
                        LoginWebService.sendForgotPassword(mail, false, this);
                        if (getActivity() != null)
                            Constant.startCounterResend();
                        elapsedTimerUtil.displayElapseHandler(d, timeElapsedTxt, btnOk);
                    }
                });
                d.show();
                elapsedTimerUtil.displayElapseHandler(d, timeElapsedTxt, btnOk);
            }
        }
    }

    private void setProgressServer(boolean isVisible) {
        binding.progressServer.setVisibility(isVisible ? View.VISIBLE : View.INVISIBLE);
    }

    private void hideKeyboard(Context context, View view) {
        InputMethodManager inputMethodManager = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.hideSoftInputFromWindow(view.getWindowToken(), 0);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        if (getActivity() != null)
            WindowCompat.setDecorFitsSystemWindows(getActivity().getWindow(), true);
    }

    private void saveCredentials(String email, String password) {
        CreatePasswordRequest createPassRequest = new CreatePasswordRequest(email, password);
        credentialManager.createCredentialAsync(
                requireActivity(),
                createPassRequest,
                /* cancellationSignal= */ null,
                mainExecutor,
                new CredentialManagerCallback<CreateCredentialResponse, CreateCredentialException>() {
                    @Override
                    public void onResult(CreateCredentialResponse result) {
                        //saved credential with success
                        closeFragment();
                    }

                    @Override
                    public void onError(@NonNull CreateCredentialException e) {
                        //saved credential with error
                        closeFragment();
                    }
                }
        );
    }
}
