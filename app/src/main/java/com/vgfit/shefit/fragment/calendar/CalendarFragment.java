package com.vgfit.shefit.fragment.calendar;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.kizitonwose.calendar.core.CalendarDay;
import com.kizitonwose.calendar.core.CalendarMonth;
import com.kizitonwose.calendar.core.DayPosition;
import com.kizitonwose.calendar.view.CalendarView;
import com.kizitonwose.calendar.view.MonthDayBinder;
import com.kizitonwose.calendar.view.MonthHeaderFooterBinder;
import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.LayoutCalendarBinding;
import com.vgfit.shefit.fragment.calendar.model.MonthViewContainer;
import com.vgfit.shefit.fragment.userprofile.progress.adapter.WorkoutHistoryAdapter;
import com.vgfit.shefit.realm.WorkoutHistory;
import com.vgfit.shefit.util.Translate;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class CalendarFragment extends Fragment implements CalendarViewCallback {

    private LayoutCalendarBinding binding;
    private YearMonth currentMonth = YearMonth.now();
    private final LocalDate today = LocalDate.now();
    private Locale currentLocale;
    private DateTimeFormatter monthFormat;
    private static final String KEY_LANGUAGE = "langDevice";

    private Map<LocalDate, Boolean> completedWorkouts = new HashMap<>();
    private Map<LocalDate, Boolean> missedWorkouts = new HashMap<>();
    private Map<LocalDate, Integer> workoutProgress = new HashMap<>();

    private LocalDate selectedWeekStart;
    private LocalDate selectedWeekEnd;

    private boolean isWeekStats = true;
    private final List<LocalDate> weekStarts = new ArrayList<>();
    private final List<LocalDate> weekEnds = new ArrayList<>();

    private LocalDate selectedDate = null;

    // Workout history adapter for selected day
    private WorkoutHistoryAdapter workoutHistoryAdapter;

    private CalendarPresenter presenter;

    public static CalendarFragment newInstance() {
        return new CalendarFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        PrefsUtilsWtContext prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());

        presenter = new CalendarPresenter(this, prefsUtilsWtContext);

        String lang = Locale.getDefault().getLanguage();
        String langUser = prefsUtilsWtContext.getStringPreferenceProfile(KEY_LANGUAGE);
        if (langUser != null) {
            lang = langUser;
        }
        currentLocale = new Locale(lang);
        monthFormat = DateTimeFormatter.ofPattern("LLLL yyyy", currentLocale);

        // Initialize week selection with localized first day of week
        DayOfWeek firstDayOfWeek = getLocalizedFirstDayOfWeek();
        DayOfWeek lastDayOfWeek = firstDayOfWeek.minus(1); // Previous day is last day of week

        selectedWeekStart = today.with(TemporalAdjusters.previousOrSame(firstDayOfWeek));
        selectedWeekEnd = today.with(TemporalAdjusters.nextOrSame(lastDayOfWeek));

        updateWeeksForMonth(currentMonth);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = LayoutCalendarBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        translate();

        setupCalendar();
        setupButtons();
        setupExercisesRecyclerView();

        // Load calendar data after calendar is set up
        presenter.loadCalendarData();
        updateWeekSummary();
    }

    private void translate() {
        binding.titleText.setText(Translate.getValue("calendar"));
        binding.weekButton.setText(Translate.getValue("week"));
        binding.monthButton.setText(Translate.getValue("month"));
        binding.completedText.setText(Translate.getValue("training_completed"));
    }

    @Override
    public void updateCalendarData(Map<LocalDate, Boolean> completedWorkouts, Map<LocalDate, Boolean> missedWorkouts) {
        this.completedWorkouts = completedWorkouts;
        this.missedWorkouts = missedWorkouts;
    }

    @Override
    public void updateCalendarDataWithProgress(Map<LocalDate, Integer> workoutProgress, Map<LocalDate, Boolean> missedWorkouts) {
        this.workoutProgress = workoutProgress;
        this.missedWorkouts = missedWorkouts;

        // Update completedWorkouts based on progress data
        this.completedWorkouts.clear();
        for (LocalDate date : workoutProgress.keySet()) {
            this.completedWorkouts.put(date, true);
        }

        // Refresh the calendar view to show updated data only if calendar is ready
        if (binding != null && binding.calendarView.getAdapter() != null) {
            refreshCalendarView();
        }
    }

    @Override
    public void refreshCalendarView() {
        if (binding != null && binding.calendarView.getAdapter() != null) {
            binding.calendarView.notifyCalendarChanged();
            updateStatsBasedOnMode();
        }
    }

    private void setupExercisesRecyclerView() {
        if (binding == null) return;

        // Initialize adapter with empty list
        workoutHistoryAdapter = new WorkoutHistoryAdapter(getContext(), new ArrayList<>());

        // Setup RecyclerView
        binding.exercisesRecyclerView.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(getContext()));
        binding.exercisesRecyclerView.setAdapter(workoutHistoryAdapter);
        binding.exercisesRecyclerView.setNestedScrollingEnabled(false);
    }

    private void populateWorkoutsForSelectedDate(LocalDate selectedDate) {
        if (selectedDate == null || presenter == null) return;

        // Get workouts for the selected date from presenter
        List<WorkoutHistory> dayWorkouts = presenter.getWorkoutsForDate(selectedDate);

        // Update RecyclerView with workouts for this day
        if (workoutHistoryAdapter != null) {
            workoutHistoryAdapter.updateData(dayWorkouts);
        }
    }

    private YearMonth[] calculateDynamicDateRange() {
        YearMonth now = YearMonth.now();
        YearMonth startMonth;
        YearMonth endMonth;

        if (presenter == null) {
            // Default range if no presenter
            startMonth = now.minusMonths(12);
            endMonth = now.plusMonths(12);
            return new YearMonth[]{startMonth, endMonth};
        }

        // Get earliest workout date from presenter
        LocalDate earliestWorkout = getEarliestWorkoutDate();

        if (earliestWorkout == null) {
            // No workout history - use default 1 year range
            startMonth = now.minusMonths(12);
            endMonth = now.plusMonths(12);
        } else {
            // Calculate months from earliest workout to now
            YearMonth earliestMonth = YearMonth.from(earliestWorkout);
            YearMonth nowMonth = YearMonth.now();

            // Calculate the span of workout history
            long monthsBetween = java.time.temporal.ChronoUnit.MONTHS.between(earliestMonth, nowMonth);

            if (monthsBetween < 12) {
                // Less than 1 year of history - ensure minimum 1 year range
                startMonth = now.minusMonths(12);
                endMonth = now.plusMonths(12);
            } else {
                // More than 1 year of history - add extra year buffer
                startMonth = earliestMonth.minusMonths(12); // Add 1 year before earliest
                endMonth = currentMonth.plusMonths(12);     // Add 1 year after current
            }
        }

        return new YearMonth[]{startMonth, endMonth};
    }

    private LocalDate getEarliestWorkoutDate() {
        if (presenter == null) return null;

        // Get all workout progress data
        Map<LocalDate, Integer> allWorkoutProgress = presenter.getWorkoutProgress();
        if (allWorkoutProgress.isEmpty()) return null;

        // Find the earliest date with workout progress
        return allWorkoutProgress.keySet().stream()
                .min(LocalDate::compareTo)
                .orElse(null);
    }

    /**
     * Get localized first day of week (same logic as ProgressUserFragment)
     * @return DayOfWeek representing the first day of week for current locale
     */
    private DayOfWeek getLocalizedFirstDayOfWeek() {
        Calendar calendar = Calendar.getInstance(currentLocale);
        int firstDayOfWeek = calendar.getFirstDayOfWeek();

        // Convert Calendar constants to DayOfWeek enum
        // Calendar.SUNDAY = 1, Calendar.MONDAY = 2, etc.
        // DayOfWeek.SUNDAY = 7, DayOfWeek.MONDAY = 1, etc.
        switch (firstDayOfWeek) {
            case Calendar.SUNDAY:
                return DayOfWeek.SUNDAY;
            case Calendar.MONDAY:
                return DayOfWeek.MONDAY;
            case Calendar.TUESDAY:
                return DayOfWeek.TUESDAY;
            case Calendar.WEDNESDAY:
                return DayOfWeek.WEDNESDAY;
            case Calendar.THURSDAY:
                return DayOfWeek.THURSDAY;
            case Calendar.FRIDAY:
                return DayOfWeek.FRIDAY;
            case Calendar.SATURDAY:
                return DayOfWeek.SATURDAY;
            default:
                return DayOfWeek.MONDAY; // Default fallback
        }
    }




    private void setupButtons() {
        binding.leftButton.setOnClickListener(v -> navigateMonth(-1));
        binding.rightButton.setOnClickListener(v -> navigateMonth(1));
        binding.backContainer.setOnClickListener(v -> {
            setVibrate(binding.backContainer);
            if (getActivity() != null) {
                getActivity().onBackPressed();
            }
        });
        updateButtonsState();
        binding.weekButton.setOnClickListener(v -> switchToWeekMode());
        binding.monthButton.setOnClickListener(v -> switchToMonthMode());
        binding.closeContainer.setOnClickListener(v -> {
            setVibrate(v);
            binding.expandableLayout.collapse();
        });
    }

    private void navigateMonth(int monthOffset) {
        setVibrate(binding.leftButton);
        currentMonth = currentMonth.plusMonths(monthOffset);
        binding.calendarView.smoothScrollToMonth(currentMonth);
        updateWeeksForMonth(currentMonth);
        updateStatsBasedOnMode();
        resetSelectionAndRefresh();
    }

    private void switchToWeekMode() {
        setVibrate(binding.weekButton);
        if (!isWeekStats) {
            isWeekStats = true;
            updateButtonsState();
            updateWeekSummary();
            resetSelectionAndRefresh();
        }
    }

    private void switchToMonthMode() {
        setVibrate(binding.monthButton);
        if (isWeekStats) {
            isWeekStats = false;
            updateButtonsState();
            updateMonthSummary();
            resetSelectionAndRefresh();
        }
    }

    private void resetSelectionAndRefresh() {
        selectedDate = null;
        if (binding.calendarView.getAdapter() != null) {
            binding.calendarView.notifyCalendarChanged();
        }
        binding.expandableLayout.collapse();
    }

    private void updateStatsBasedOnMode() {
        if (isWeekStats) {
            if (selectedWeekStart.getMonth() != currentMonth.getMonth() &&
                    selectedWeekEnd.getMonth() != currentMonth.getMonth() && !weekStarts.isEmpty()) {
                selectedWeekStart = weekStarts.get(0);
                selectedWeekEnd = weekEnds.get(0);
            }
            updateWeekSummary();
        } else {
            updateMonthSummary();
        }
        updateButtonsState();
    }

    private void setupCalendar() {
        CalendarView calendarView = binding.calendarView;
        calendarView.setDayBinder(new MonthDayBinder<DayViewContainer>() {
            @NonNull
            @Override
            public DayViewContainer create(@NonNull View view) {
                return new DayViewContainer(view);
            }

            @Override
            public void bind(@NonNull DayViewContainer container, CalendarDay day) {
                container.bind(day);
            }
        });
        calendarView.setMonthHeaderBinder(new MonthHeaderFooterBinder<MonthViewContainer>() {
            @NonNull
            @Override
            public MonthViewContainer create(@NonNull View view) {
                return new MonthViewContainer(view, today, currentLocale);
            }

            @Override
            public void bind(@NonNull MonthViewContainer container, CalendarMonth calendarMonth) {
                // Nu este nevoie să facem nimic aici, deoarece header-ul este static
            }
        });

        // Calculate dynamic date range based on workout history
        YearMonth[] dateRange = calculateDynamicDateRange();
        YearMonth startMonth = dateRange[0];
        YearMonth endMonth = dateRange[1];

        // Get localized first day of week (same logic as ProgressUserFragment)
        DayOfWeek firstDayOfWeek = getLocalizedFirstDayOfWeek();

        calendarView.setup(startMonth, endMonth, firstDayOfWeek);
        calendarView.scrollToMonth(currentMonth);
        updateMonthTitle();

        calendarView.setMonthScrollListener(calendarMonth -> {
            currentMonth = calendarMonth.getYearMonth();
            updateWeeksForMonth(currentMonth);
            updateMonthTitle();
            updateStatsBasedOnMode();
            selectedDate = null;

            // Only notify if adapter is ready
            if (binding.calendarView.getAdapter() != null) {
                binding.calendarView.notifyCalendarChanged();
            }
            binding.expandableLayout.collapse();

            return null;
        });
    }

    private void updateMonthTitle() {
        binding.month.setText(currentMonth.format(monthFormat));
    }

    private void updateWeeksForMonth(YearMonth month) {
        weekStarts.clear();
        weekEnds.clear();

        LocalDate firstDay = month.atDay(1);
        LocalDate lastDay = month.atEndOfMonth();

        // Get localized first day of week for week calculation
        DayOfWeek firstDayOfWeek = getLocalizedFirstDayOfWeek();
        LocalDate weekStart = firstDay.with(TemporalAdjusters.previousOrSame(firstDayOfWeek));

        while (!weekStart.isAfter(lastDay)) {
            LocalDate weekEnd = weekStart.plusDays(6);

            weekStarts.add(weekStart);
            weekEnds.add(weekEnd);

            weekStart = weekStart.plusWeeks(1);
        }

        boolean weekInCurrentMonth = false;

        for (int i = 0; i < weekStarts.size(); i++) {
            LocalDate start = weekStarts.get(i);
            LocalDate end = weekEnds.get(i);

            if (selectedWeekStart.equals(start) && selectedWeekEnd.equals(end) &&
                    (isDateInMonth(start, month) || isDateInMonth(end, month) ||
                            isMonthBetweenDates(month, start, end))) {
                weekInCurrentMonth = true;
                break;
            }
        }

        if (!weekInCurrentMonth && !weekStarts.isEmpty()) {
            for (int i = 0; i < weekStarts.size(); i++) {
                LocalDate start = weekStarts.get(i);
                LocalDate end = weekEnds.get(i);

                if (containsDayInMonth(start, end, month)) {
                    selectedWeekStart = start;
                    selectedWeekEnd = end;
                    break;
                }
            }
        }
    }

    private boolean isDateInMonth(LocalDate date, YearMonth month) {
        return date.getMonth() == month.getMonth() && date.getYear() == month.getYear();
    }

    private boolean isMonthBetweenDates(YearMonth month, LocalDate start, LocalDate end) {
        YearMonth startMonth = YearMonth.from(start);
        YearMonth endMonth = YearMonth.from(end);

        return (month.isAfter(startMonth) || month.equals(startMonth)) &&
                (month.isBefore(endMonth) || month.equals(endMonth));
    }

    private boolean containsDayInMonth(LocalDate start, LocalDate end, YearMonth month) {
        YearMonth startMonth = YearMonth.from(start);
        YearMonth endMonth = YearMonth.from(end);

        if (month.isBefore(startMonth) || month.isAfter(endMonth)) {
            return false;
        }

        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            if (isDateInMonth(date, month)) {
                return true;
            }
        }

        return false;
    }

    private void updateWeekSummary() {
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("MMMM", currentLocale);
        binding.yearText.setText(String.valueOf(selectedWeekStart.getYear()));
        binding.weekRangeText.setText(String.format(currentLocale, "%s %d-%d",
                selectedWeekStart.format(monthFormatter),
                selectedWeekStart.getDayOfMonth(),
                selectedWeekEnd.getDayOfMonth()));

        int completed = 0;
        int total = 0;
        int totalProgress = 0;

        LocalDate date = selectedWeekStart;
        while (!date.isAfter(selectedWeekEnd)) {
            if (completedWorkouts.containsKey(date) || missedWorkouts.containsKey(date)) {
                total++;
                if (completedWorkouts.containsKey(date)) {
                    completed++;
                    // Add real progress if available
                    Integer dayProgress = workoutProgress.get(date);
                    totalProgress += (dayProgress != null) ? dayProgress : 100;
                }
            }
            date = date.plusDays(1);
        }

        binding.completionRatio.setText(String.format(currentLocale, "%d/%d", completed, total));
        // Calculate average progress percentage
        int progressPercentage = total != 0 ? totalProgress / total : 0;
        binding.progressBar.setProgress(progressPercentage);
    }

    private void updateMonthSummary() {
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("MMMM", currentLocale);
        binding.yearText.setText(String.valueOf(currentMonth.getYear()));
        binding.weekRangeText.setText(currentMonth.format(monthFormatter));

        int completed = 0;
        int total = 0;
        int totalProgress = 0;

        LocalDate firstDayOfMonth = currentMonth.atDay(1);
        LocalDate lastDayOfMonth = currentMonth.atEndOfMonth();
        LocalDate date = firstDayOfMonth;

        while (!date.isAfter(lastDayOfMonth)) {
            if (completedWorkouts.containsKey(date) || missedWorkouts.containsKey(date)) {
                total++;
                if (completedWorkouts.containsKey(date)) {
                    completed++;
                    // Add real progress if available
                    Integer dayProgress = workoutProgress.get(date);
                    totalProgress += (dayProgress != null) ? dayProgress : 100;
                }
            }
            date = date.plusDays(1);
        }
        binding.completionRatio.setText(String.format(currentLocale, "%d/%d", completed, total));
        // Calculate average progress percentage
        int progressPercentage = total != 0 ? totalProgress / total : 0;
        binding.progressBar.setProgress(progressPercentage);
    }

    private void updateButtonsState() {
        binding.weekButton.setSelected(isWeekStats);
        binding.monthButton.setSelected(!isWeekStats);
    }

    private class DayViewContainer extends com.kizitonwose.calendar.view.ViewContainer {
        private final TextView dayText;
        private final ImageView completionIndicator;
        private final ProgressBar dayIndicator;
        private final View dayBackground;
        private CalendarDay day;

        public DayViewContainer(@NonNull View view) {
            super(view);
            dayText = view.findViewById(R.id.dayText);
            completionIndicator = view.findViewById(R.id.completionIndicator);
            dayIndicator = view.findViewById(R.id.dayIndicator);
            dayBackground = view.findViewById(R.id.dayBackground);

            view.setOnClickListener(v -> {
                setVibrate(v);
                binding.expandableLayout.collapse();
                if (day.getPosition() == DayPosition.MonthDate) {
                    LocalDate clickedDate = day.getDate();
                    boolean hasWorkout = completedWorkouts.containsKey(clickedDate) ||
                            missedWorkouts.containsKey(clickedDate);
                    if (hasWorkout) {
                        selectedDate = clickedDate;
                        if (isWeekStats) {
                            selectWeek(clickedDate);
                        }
                        if (binding.calendarView.getAdapter() != null) {
                            binding.calendarView.notifyCalendarChanged();
                        }

                        // Apply selection and populate RecyclerView with workouts
                        populateWorkoutsForSelectedDate(clickedDate);

                        binding.expandableLayout.expand();

                    } else if (isWeekStats) {
                        binding.expandableLayout.collapse();
                        selectWeek(clickedDate);
                    }
                }
            });
        }

        public void bind(CalendarDay day) {
            this.day = day;
            dayText.setText(String.valueOf(day.getDate().getDayOfMonth()));

            if (day.getPosition() == DayPosition.MonthDate) {
                dayText.setVisibility(View.VISIBLE);

                LocalDate date = day.getDate();
                boolean isToday = date.equals(today);
                boolean isCompleted = completedWorkouts.containsKey(date);
                boolean isMissed = missedWorkouts.containsKey(date);
                Integer progressValue = workoutProgress.get(date);
                int progress = progressValue != null ? progressValue : 0;
                boolean isSelected = date.equals(selectedDate);
                boolean hasWorkout = isCompleted || isMissed;

                dayBackground.setBackground(null);

                applyWeekBackgroundIfNeeded(date);

                applySelectionIfNeeded(isSelected, progress);

                setDayTextColor(isToday);

                configureWorkoutIndicators(date, hasWorkout, isCompleted, isToday);
            } else {
                styleOutOfMonthDay();
            }
        }

        private void applyWeekBackgroundIfNeeded(LocalDate date) {
            if (isWeekStats) {
                if (!date.isBefore(selectedWeekStart) && !date.isAfter(selectedWeekEnd)) {
                    applyWeekBackground(date, selectedWeekStart, selectedWeekEnd);
                }
            } else {
                // Get localized first day of week for month background
                DayOfWeek firstDayOfWeek = getLocalizedFirstDayOfWeek();
                DayOfWeek lastDayOfWeek = firstDayOfWeek.minus(1); // Previous day is last day of week

                LocalDate weekStart = date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek));
                LocalDate weekEnd = date.with(TemporalAdjusters.nextOrSame(lastDayOfWeek));

                if (hasWorkoutInWeek(weekStart, weekEnd)) {
                    applyWeekBackground(date, weekStart, weekEnd);
                }
            }
        }

        private boolean hasWorkoutInWeek(LocalDate weekStart, LocalDate weekEnd) {
            for (LocalDate d = weekStart; !d.isAfter(weekEnd); d = d.plusDays(1)) {
                if (completedWorkouts.containsKey(d) || missedWorkouts.containsKey(d)) {
                    return true;
                }
            }
            return false;
        }

        private void applySelectionIfNeeded(boolean isSelected, int progress) {
            if (isSelected && getContext() != null) {
                binding.missedWorkoutContainer.setVisibility(progress == 0 ? View.VISIBLE : View.GONE);
                binding.missedText.setText(Translate.getValue("the_training_was_missed"));
                Drawable existingBackground = dayBackground.getBackground();
                Drawable selectionDrawable = ContextCompat.getDrawable(getContext(), R.drawable.bg_selected_day);

                if (existingBackground != null) {
                    LayerDrawable layerDrawable = new LayerDrawable(new Drawable[]{
                            existingBackground, selectionDrawable
                    });
                    dayBackground.setBackground(layerDrawable);
                } else {
                    dayBackground.setBackground(selectionDrawable);
                }
            }
        }

        private void setDayTextColor(boolean isToday) {
            if (getContext() != null) {
                int textColor = isToday
                        ? ContextCompat.getColor(getContext(), R.color.current_day_pink)
                        : ContextCompat.getColor(getContext(), android.R.color.white);
                dayText.setTextColor(textColor);
            }
        }

        private void configureWorkoutIndicators(LocalDate date, boolean hasWorkout, boolean isCompleted, boolean isToday) {
            if (!hasWorkout) {
                dayIndicator.setVisibility(View.GONE);
                completionIndicator.setVisibility(View.GONE);
                return;
            }

            completionIndicator.setVisibility(View.VISIBLE);
            if (getContext() != null) {
                int iconRes = isCompleted ? R.drawable.ic_day_complet : R.drawable.ic_day_missed;
                int colorRes = isCompleted ? R.color.green_color_day : R.color.red_color_day;

                completionIndicator.setImageDrawable(ContextCompat.getDrawable(getContext(), iconRes));
                completionIndicator.setImageTintList(ColorStateList.valueOf(
                        ContextCompat.getColor(getContext(), colorRes)));
            }

            dayIndicator.setVisibility(View.VISIBLE);

            // Use real progress if available, otherwise use default values
            int progress = 20; // Default for missed workouts
            if (isCompleted) {
                Integer realProgress = workoutProgress.get(date);
                progress = (realProgress != null) ? realProgress : 75; // Default to 75% if no specific progress
            }
            dayIndicator.setProgress(progress);

            if (getContext() != null && isCompleted) {
                Drawable progressDrawable = ContextCompat.getDrawable(getContext(),
                        isToday ? R.drawable.gradient_progress_day_pink : R.drawable.gradient_progress_day);
                dayIndicator.setProgressDrawable(progressDrawable);
            } else if (getContext() != null) {
                dayIndicator.setProgressTintList(ColorStateList.valueOf(
                        ContextCompat.getColor(getContext(), R.color.red_color_day)));
            }
        }

        private void styleOutOfMonthDay() {
            if (getContext() != null) {
                dayText.setTextColor(ContextCompat.getColor(getContext(), R.color.gray_text));
            }
            dayIndicator.setVisibility(View.GONE);
            completionIndicator.setVisibility(View.GONE);
            dayBackground.setBackground(null);
        }

        private void applyWeekBackground(LocalDate date, LocalDate weekStart, LocalDate weekEnd) {
            boolean isInCurrentMonth = date.getMonth() == currentMonth.getMonth() &&
                    date.getYear() == currentMonth.getYear();
            boolean isFirstVisibleDay;
            boolean isLastVisibleDay;

            if (isInCurrentMonth) {
                isFirstVisibleDay = date.equals(weekStart) || isFirstDayOfWeek(date, weekStart);
                isLastVisibleDay = date.equals(weekEnd) || isLastDayOfWeek(date, weekEnd);
            } else {
                isFirstVisibleDay = isFirstDayOfWeek(date, weekStart);
                isLastVisibleDay = isLastDayOfWeek(date, weekEnd);
            }
            setDayBackground(isFirstVisibleDay, isLastVisibleDay);
        }

        private boolean isFirstDayOfWeek(LocalDate date, LocalDate weekStart) {
            for (LocalDate d = date.minusDays(1); !d.isBefore(weekStart); d = d.minusDays(1)) {
                if (d.getMonth() == currentMonth.getMonth() && d.getYear() == currentMonth.getYear()) {
                    return false;
                }
            }
            return true;
        }

        private boolean isLastDayOfWeek(LocalDate date, LocalDate weekEnd) {
            for (LocalDate d = date.plusDays(1); !d.isAfter(weekEnd); d = d.plusDays(1)) {
                if (d.getMonth() == currentMonth.getMonth() && d.getYear() == currentMonth.getYear()) {
                    return false;
                }
            }
            return true;
        }

        private void setDayBackground(boolean isFirstVisibleDay, boolean isLastVisibleDay) {
            boolean isOnlyVisibleDay = isFirstVisibleDay && isLastVisibleDay;
            if (isOnlyVisibleDay) {
                dayBackground.setBackgroundResource(R.drawable.bg_week_single_day);
            } else if (isFirstVisibleDay) {
                dayBackground.setBackgroundResource(R.drawable.bg_week_first_day);
            } else if (isLastVisibleDay) {
                dayBackground.setBackgroundResource(R.drawable.bg_week_last_day);
            } else {
                dayBackground.setBackgroundResource(R.drawable.bg_week_middle_day);
            }
        }

        private void selectWeek(LocalDate date) {
            // Get localized first day of week
            DayOfWeek firstDayOfWeek = getLocalizedFirstDayOfWeek();
            DayOfWeek lastDayOfWeek = firstDayOfWeek.minus(1); // Previous day is last day of week

            selectedWeekStart = date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek));
            selectedWeekEnd = date.with(TemporalAdjusters.nextOrSame(lastDayOfWeek));

            if (!isWeekStats) {
                isWeekStats = true;
                updateButtonsState();
            }

            updateWeekSummary();

            if (binding.calendarView.getAdapter() != null) {
                binding.calendarView.notifyCalendarChanged();
            }
        }
    }
}
