package com.vgfit.shefit.fragment.exercises;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;
import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TableLayout;
import android.widget.TableRow;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.helper.widget.Flow;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;

import com.google.android.exoplayer2.C;
import com.google.android.exoplayer2.ui.AspectRatioFrameLayout;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultDataSource;
import com.google.android.exoplayer2.upstream.cache.Cache;
import com.google.android.exoplayer2.upstream.cache.CacheDataSink;
import com.google.android.exoplayer2.upstream.cache.CacheDataSource;
import com.vgfit.shefit.BaseApplication;
import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.R;
import com.vgfit.shefit.apprate.rateimplementation.RateImplementation;
import com.vgfit.shefit.apprate.rateimplementation.RateInit;
import com.vgfit.shefit.databinding.ExerciseDetailsBinding;
import com.vgfit.shefit.fragment.workouts.downloader.Downloaded;
import com.vgfit.shefit.fragment.workouts.model.ItemExercise;
import com.vgfit.shefit.fragment.workouts.player.MainPlayer;
import com.vgfit.shefit.realm.Exercise;
import com.vgfit.shefit.util.Translate;

import java.util.ArrayList;

public class ExerciseDetailsFragment extends Fragment implements Downloaded {
    private ExerciseDetailsBinding binding;
    Exercise exercise;
    ArrayList<ItemExercise> listVideo;
    String[] steps;
    private MainPlayer mainPlayer;
    private int sizeHeightFrameVideo;
    private String language = "";
    private DataSource.Factory dataSourceFactory;

    public static ExerciseDetailsFragment newInstance(Exercise exercise) {
        ExerciseDetailsFragment fragment = new ExerciseDetailsFragment();
        Bundle arguments = new Bundle();
        arguments.putParcelable("exercise", exercise);
        fragment.setArguments(arguments);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Context context = getContext();
        if (context != null) {
            DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();
            sizeHeightFrameVideo = displayMetrics.heightPixels - (displayMetrics.heightPixels / 2);
            dataSourceFactory = createDataSource(context);
        }
        listVideo = new ArrayList<>();
        Bundle arguments = getArguments();
        if (arguments != null) {
            exercise = arguments.getParcelable("exercise");
        }
        if (exercise != null) {
            String name = exercise.getVideo();
            String nameHD = exercise.getVideoHD();
            String nameLowQ = exercise.getVideoLowQ();
            String playlist = exercise.getPlaylist();
            listVideo.add(new ItemExercise("", name, nameHD, nameLowQ, 0, 0, "", playlist));

            String stepsStr = Translate.getValue(exercise.getDescription_());
            language = Translate.getLanguage();
            if (language.equals("en")) steps = stepsStr.split("\\n");
            else if (language.equals("ru"))
                steps = stepsStr.contains("Шаг") ? stepsStr.split("Шаг ") : stepsStr.split("\\n");

        }
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = ExerciseDetailsBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setStatusBar(binding.top);
        mainPlayer = new MainPlayer(getContext(), listVideo, binding.playerView);
        if (mainPlayer.getPlayer() == null) {
            mainPlayer.initPlayer(savedInstanceState, 0, dataSourceFactory);
        }
        binding.playerView.setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_ZOOM);
        binding.playerView.getLayoutParams().height = sizeHeightFrameVideo;

        isSuccess(true);
        String textEquipment = Translate.getValue(exercise.getEquipment());
        binding.textNeedInfo.setText(textEquipment);
        binding.back.setOnClickListener(v -> {
            setVibrate(v);
            if (new RateInit(getContext()).isValidShowRate())
                new RateImplementation(getContext(), getActivity()).setRateApp();
            if (getContext() != null)
                ((MainActivity) getContext()).onBackPressed();
        });
        String nameTxt = Translate.getValue(exercise.getName()) + " ";
        binding.nameExercise.setText(nameTxt);

        initSteps(view, steps);
        initBodyParts(view, Translate.getValue(exercise.getBodyPartsInvolved()));
        String needTxt = Translate.getValue("you_will_need") + ":";
        String bodyTxt = Translate.getValue("body_parts_involved") + ":";
        binding.textNeed.setText(needTxt);
        binding.textBody.setText(bodyTxt);
        binding.textNeed.setVisibility(!textEquipment.isEmpty() ? View.VISIBLE : View.INVISIBLE);
    }

    @Override
    public void onStart() {
        super.onStart();
        if (mainPlayer != null)
            mainPlayer.releasePlayer();
        if (mainPlayer != null)
            mainPlayer.initializePlayer();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mainPlayer != null)
            mainPlayer.releasePlayer();
        if (mainPlayer != null) {
            mainPlayer.initializePlayer();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        if (mainPlayer != null)
            mainPlayer.releasePlayer();
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mainPlayer != null)
            mainPlayer.releasePlayer();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mainPlayer != null)
            mainPlayer.releasePlayer();
    }

    public void initSteps(View view, String[] steps) {
        String text = "Step ";
        TableLayout ll = view.findViewById(R.id.tableSteps);
        TypedValue typedValue = new TypedValue();
        if (getActivity() != null)
            getActivity().getTheme().resolveAttribute(R.attr.colorPrimaryDark, typedValue, true);
        int color = typedValue.data;
        int count = 0;
        if (steps != null && getContext() != null)
            for (String line : steps) {
                if (!line.isEmpty()) {
                    if (!language.equals("en")) line = line.replace("" + (count + 1) + " : ", "");
                    inflateRow(ll, line, text, color, count);
                    count++;
                }
            }
    }

    private void inflateRow(TableLayout ll, String line, String text, int color, int count) {
        TableRow row2 = new TableRow(getContext());
        TableRow.LayoutParams lp2 = new TableRow.LayoutParams(TableRow.LayoutParams.WRAP_CONTENT, TableRow.LayoutParams.WRAP_CONTENT);
        row2.setLayoutParams(lp2);
        if (getContext() != null) {
            LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            View row = inflater.inflate(R.layout.item_steps, row2, false);
            TextView step = row.findViewById(R.id.step);
            if (language.equals("en")) text = "Step \n0" + (count + 1);
            else if (language.equals("ru")) text = "Шаг \n0" + (count + 1);
            step.setText(text);
            step.setTextColor(color);
            TextView stepInfo = row.findViewById(R.id.stepInfo);
            stepInfo.setText(line);
            stepInfo.setTextColor(color);
            row2.addView(row);
            ll.addView(row2, count);
        }
    }

    public void initBodyParts(View view, String bodyPart) {
        Flow ll = view.findViewById(R.id.flowD);
        ConstraintLayout constraintLayout = view.findViewById(R.id.constraintLayout);
        String[] parts = bodyPart.split(",");
        for (String part : parts) {
            part = part.replace(" ", "");
            if (!part.isEmpty() && getContext() != null) {
                LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
                View row = inflater.inflate(R.layout.item_body_part, constraintLayout, false);
                row.setId(View.generateViewId());
                TextView partTxt = row.findViewById(R.id.partInfo);
                partTxt.setText(part);
                constraintLayout.addView(row);
                ll.addView(row);
            }

        }
    }

    public float dpToPx(Context context, int dp) {
        Resources r = context.getResources();
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, r.getDisplayMetrics());
    }

    @Override
    public void isSuccess(boolean valid) {
        if (valid && mainPlayer != null) {
            mainPlayer.releasePlayer();
            mainPlayer.initializePlayer();
        }
    }

    private DataSource.Factory createDataSource(Context context) {
        Cache cache = BaseApplication.getCashExoplayer(context);
        DataSource.Factory upstreamFactory = new DefaultDataSource.Factory(context);
        CacheDataSink.Factory cacheWriteDataSinkFactory = new CacheDataSink.Factory().setCache(cache).setFragmentSize(C.LENGTH_UNSET);
        return new CacheDataSource.Factory().setCache(cache).setUpstreamDataSourceFactory(upstreamFactory).setCacheWriteDataSinkFactory(cacheWriteDataSinkFactory).setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR);
    }
}
