package com.vgfit.shefit.fragment.workouts.adapter;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.vgfit.shefit.databinding.ItemExerciseVideoWorkoutBinding;
import com.vgfit.shefit.fragment.premium.OnSingleClickListener;
import com.vgfit.shefit.fragment.workouts.callbacks.ExerciseItemClick;
import com.vgfit.shefit.fragment.workouts.model.ItemExercise;
import com.vgfit.shefit.util.Translate;

import java.util.List;

public class AdapterExerciseVideo extends RecyclerView.Adapter<AdapterExerciseVideo.ViewHolder> {
    private final List<ItemExercise> list;
    private final Context context;
    private final ExerciseItemClick onClickListener;

    private int positionSelected = 0;
    private int currentPosition = 0;
    private RequestOptions requestOptions;

    public AdapterExerciseVideo(List<ItemExercise> list, Context context, ExerciseItemClick onClickListener) {
        this.list = list;
        this.context = context;
        this.onClickListener = onClickListener;
    }

    public void setPositionSelected(int position) {
        currentPosition = positionSelected;
        this.positionSelected = position;
        notifyItemChanged(position);
        notifyItemChanged(currentPosition);
    }

    @NonNull
    @Override
    public AdapterExerciseVideo.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        LayoutInflater inflater = LayoutInflater.from(viewGroup.getContext());
        ItemExerciseVideoWorkoutBinding binding = ItemExerciseVideoWorkoutBinding.inflate(inflater, viewGroup, false);
        ViewHolder holder = new ViewHolder(binding);
        requestOptions = new RequestOptions();
        requestOptions = requestOptions.transforms(new CenterCrop(), new RoundedCorners(20));
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull AdapterExerciseVideo.ViewHolder viewHolder, int position1) {
        int adapterPosition = viewHolder.getBindingAdapterPosition();
        viewHolder.ivImageExercise.setScaleType(ImageView.ScaleType.CENTER_CROP);
        Glide.with(context)
                .load(Uri.parse(list.get(adapterPosition).getImage()))
                .apply(requestOptions)
                .into(viewHolder.ivImageExercise);

        viewHolder.tvNameExercise.setText(list.get(adapterPosition).getNameExercise());
        String duration = (int) (list.get(adapterPosition).getExerciseTime() / 1000) + " " + Translate.getValue("sec");
        viewHolder.tvDurationOfExercise.setText(duration);
        viewHolder.llItemExercise.setOnClickListener(new OnSingleClickListener() {
            @Override
            public void onSingleClick(View v) {
                setVibrate(v);
                onClickListener.onItemClick(adapterPosition);
                currentPosition = positionSelected;
                positionSelected = adapterPosition;
                notifyItemChanged(adapterPosition);
                notifyItemChanged(currentPosition);
            }
        });
        viewHolder.itemView.setSelected(positionSelected == adapterPosition);
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    public int getPositionSelected() {
        return positionSelected;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvNameExercise;
        TextView tvDurationOfExercise;
        ImageView ivImageExercise;
        RelativeLayout llItemExercise;

        public ViewHolder(@NonNull ItemExerciseVideoWorkoutBinding binding) {
            super(binding.getRoot());
            tvNameExercise = binding.tvNameOfExercise;
            ivImageExercise = binding.ivImageExercise;
            llItemExercise = binding.llItemExercise;
            tvDurationOfExercise = binding.tvDurationOfExercise;
        }
    }
}
