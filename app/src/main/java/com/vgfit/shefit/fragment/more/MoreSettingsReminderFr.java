package com.vgfit.shefit.fragment.more;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.vgfit.shefit.databinding.FragmentMoreReminderBinding;
import com.vgfit.shefit.fragment.more.reminder.MakingScrollView;
import com.vgfit.shefit.fragment.more.service.SharedPreferencesData;

public class MoreSettingsReminderFr extends Fragment {
    View view;
    MakingScrollView makingScrollView;
    SharedPreferencesData sharedPreferencesData;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        makingScrollView = new MakingScrollView(getContext());
        sharedPreferencesData = new SharedPreferencesData(getContext());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        FragmentMoreReminderBinding binding = FragmentMoreReminderBinding.inflate(inflater, container, false);
        view = binding.getRoot();
        sharedPreferencesData.fillDataWithSavedInformation(binding.tvDailyReminder, binding.tvInactivityText, binding.tvInactivity, binding.tvFrequency, binding.scInactivity, binding.scDailyReminder);
        binding.tvFrequency.setOnClickListener(v -> makingScrollView.setDaysScroll(binding.tvFrequency, binding.tvInactivityText, sharedPreferencesData.getFREQUENCY_KEY()));
        binding.tvDailyReminder.setOnClickListener(v -> makingScrollView.setTimeScroll(binding.scDailyReminder, binding.tvDailyReminder, sharedPreferencesData.getDAILY_REMINDER_KEY(), true));
        binding.tvInactivity.setOnClickListener(v -> makingScrollView.setTimeScroll(binding.scInactivity, binding.tvInactivity, sharedPreferencesData.getINACTIVITY_KEY(), false));

        binding.scInactivity.setOnCheckedChangeListener((buttonView, isChecked) -> sharedPreferencesData.saveDataAsKeyValueBoolean(sharedPreferencesData.getINACTIVITY_SWITCH_KEY(), isChecked));
        binding.scDailyReminder.setOnCheckedChangeListener((buttonView, isChecked) -> sharedPreferencesData.saveDataAsKeyValueBoolean(sharedPreferencesData.getDAILY_REMINDER_SWITCH_KEY(), isChecked));

        return view;
    }
}