package com.vgfit.shefit.fragment.workouts.player;

import static com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED;
import static com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import com.androidbolts.topsheet.TopSheetBehavior;
import com.google.android.exoplayer2.C;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.source.hls.HlsMediaSource;
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.util.EventLogger;
import com.vgfit.shefit.databinding.FragmentWorkoutsVideoBinding;
import com.vgfit.shefit.fragment.premium.OnSingleClickListener;
import com.vgfit.shefit.fragment.workouts.downloader.Downloaded;
import com.vgfit.shefit.fragment.workouts.model.ItemExercise;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.exo_utility.DownloadHLS;

import java.util.ArrayList;

public class MainPlayer implements Downloaded {

    private static final String KEY_WINDOW = "window";
    private static final String KEY_POSITION = "position";
    private Context context;
    private ArrayList<ItemExercise> list;
    private ExoPlayer player;
    private PlayerView playerView;
    private int currentWindow;
    private long playbackPosition;
    private boolean shouldAutoPlay;
    private DefaultTrackSelector trackSelector;
    private DataSource.Factory dataSourceFactory;
    private DownloadHLS downloadHLS;
    FragmentWorkoutsVideoBinding binding = null;

    public MainPlayer(Context context, ArrayList<ItemExercise> list, PlayerView playerView) {
        this.context = context;
        this.list = list;
        this.playerView = playerView;
        downloadHLS = new DownloadHLS(context);
    }

    public DownloadHLS getDownloadHLS() {
        return this.downloadHLS;
    }

    public void setBindingView(FragmentWorkoutsVideoBinding binding) {
        this.binding = binding;
    }

    public void initPlayer(Bundle savedInstanceState, int position, DataSource.Factory dataSourceFactory) {
        if (savedInstanceState == null) {
            currentWindow = 0;
            if (position != 0) currentWindow = position;
            playbackPosition = 0;
        } else {
            currentWindow = savedInstanceState.getInt(KEY_WINDOW);
            playbackPosition = savedInstanceState.getLong(KEY_POSITION);
        }
        shouldAutoPlay = true;
        this.dataSourceFactory = dataSourceFactory;
    }

    public void initializePlayer() {
        assert playerView != null;
        String badUrl;
        playerView.requestFocus();
        player = new ExoPlayer.Builder(context).build();
        player.addAnalyticsListener(new EventLogger(trackSelector));
        playerView.setPlayer(player);
        player.setRepeatMode(Player.REPEAT_MODE_ONE);
        player.setPlayWhenReady(shouldAutoPlay);
        String path = "";
        ArrayList<String> listVideoDownload = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            path = list.get(i).getPlaylist();
            String uri = path;
            HlsMediaSource mediaSource = new HlsMediaSource.Factory(dataSourceFactory)
                    .createMediaSource(MediaItem.fromUri(uri));
            player.addMediaSource(mediaSource);
            if (!downloadHLS.isVideoDownloaded(uri)) {
                listVideoDownload.add(uri);
            }
        }
        requestPermissionToDownload(listVideoDownload);
        boolean haveStartPosition = currentWindow != C.INDEX_UNSET;
        if (haveStartPosition) player.seekTo(currentWindow, playbackPosition);
        player.prepare();
        player.addListener(new Player.Listener() {
            @Override
            public void onPlayerError(PlaybackException error) {
                if (player != null) {
                    player.stop();
                    player.prepare();
                    player.setPlayWhenReady(shouldAutoPlay);
                }
                Player.Listener.super.onPlayerError(error);
                if (error.errorCode == ERROR_CODE_IO_NETWORK_CONNECTION_FAILED || error.errorCode == ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT) {
                    Toast.makeText(context, error.getMessage(), Toast.LENGTH_SHORT).show();
                }

                Log.d("TestError", "error player-->" + error.getMessage());
            }

            @Override
            public void onPlaybackStateChanged(int playbackState) {
                Player.Listener.super.onPlaybackStateChanged(playbackState);
                Log.d("TestPlayer", "Clicked state->" + playbackState);
            }
        });
    }

    private void requestPermissionToDownload(ArrayList<String> listVideo) {
        final Handler handler = new Handler(Looper.getMainLooper());
        String message = Translate.getValue("would_you_like_to_download_the_videos_for_offline_viewing");
        if (!listVideo.isEmpty() && context != null) {
            handler.postDelayed(() -> {
                if (binding != null)
                    topSheet(message, listVideo);
            }, 2000);
        }
    }

    private void topSheet(String message, ArrayList<String> listVideo) {

        binding.messageAlert.setText(message);
        final Handler handler = new Handler(Looper.getMainLooper());

        TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_EXPANDED);
        binding.topSheet.setOnClickListener(v -> TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_COLLAPSED));
        handler.postDelayed(() -> TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_COLLAPSED), 5000);
        binding.btnYes.setText(Translate.getValue("download"));
        binding.btnYes.setOnClickListener(new OnSingleClickListener() {
            @Override
            public void onSingleClick(View v) {
                for (String item : listVideo) {
                    downloadHLS.downloadManager(item);
                }
                TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_COLLAPSED);
            }
        });
        binding.btnNo.setText(Translate.getValue("cancel"));
        binding.btnNo.setOnClickListener(view -> TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_COLLAPSED));
    }


    public ExoPlayer getPlayer() {
        return this.player;
    }

    public void seekToPrev() {
        if (currentWindow - 1 >= 0) {
            currentWindow -= 1;
            player.seekTo(currentWindow, C.TIME_UNSET);
        }
    }

    public void seekToNext() {
        if (currentWindow + 1 < list.size()) {
            currentWindow += 1;
            player.seekTo(currentWindow, C.TIME_UNSET);
        }
    }

    public void releasePlayer() {
        if (player != null) {
            updateStartPosition();
            shouldAutoPlay = player.getPlayWhenReady();
            player.stop();
            player.release();
            player = null;
            trackSelector = null;
        }
    }

    public void updateStartPosition() {
        if (player != null) {
            playbackPosition = player.getCurrentPosition();
            currentWindow = player.getCurrentWindowIndex();
        }
    }

    @Override
    public void isSuccess(boolean valid) {
        if (valid) Log.e("SpTest", "isDownloaded");
    }

}
