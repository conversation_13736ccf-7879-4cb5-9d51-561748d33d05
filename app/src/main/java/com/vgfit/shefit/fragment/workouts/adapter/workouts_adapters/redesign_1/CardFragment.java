package com.vgfit.shefit.fragment.workouts.adapter.workouts_adapters.redesign_1;

import android.annotation.SuppressLint;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.vgfit.shefit.databinding.ItemViewpagerWorkoutsBinding;
import com.vgfit.shefit.realm.Superset;
import com.vgfit.shefit.util.Translate;

import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;


public class CardFragment extends Fragment {
    private ItemViewpagerWorkoutsBinding binding;
    private RequestOptions requestOptions;
    private List<Superset> supersetList;
    private AdapterViewPagerInfo adapterViewPagerInfo;

    int positionGlobal = 0;

    public static Fragment getInstance(int position) {
        CardFragment f = new CardFragment();
        Bundle args = new Bundle();
        args.putInt("position", position);
        f.setArguments(args);

        return f;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestOptions = new RequestOptions();
        requestOptions = requestOptions
                .transforms(new CenterCrop(), new RoundedCorners(30))
                .diskCacheStrategy(DiskCacheStrategy.ALL);

        try (Realm realm = Realm.getDefaultInstance()) {
            RealmResults<Superset> supersetResultRealmList = realm.where(Superset.class).findAll();
            supersetResultRealmList = supersetResultRealmList.sort("order_");
            supersetList = realm.copyFromRealm(supersetResultRealmList);
        }

        if (getArguments() != null)
            positionGlobal = getArguments().getInt("position");

        adapterViewPagerInfo = new AdapterViewPagerInfo(getContext(), supersetList, positionGlobal);
    }

    @SuppressLint("DefaultLocale")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        binding = ItemViewpagerWorkoutsBinding.inflate(inflater, container, false);
        String supersetName;
        String supersetTitle;
        String supersetImage;

        supersetName = Translate.getValue(supersetList.get(positionGlobal).getName());
        supersetTitle = supersetName.replaceFirst(" ", "\n");
        supersetImage = supersetName.replaceAll(" ", "") + ".png";

        binding.cardView.setMaxCardElevation(binding.cardView.getCardElevation() * CardAdapter.MAX_ELEVATION_FACTOR);
        binding.tvTitle.setText(supersetTitle);

        if (getContext() != null)
            Glide.with(getContext())
                    .load(takeImageFromAssets(supersetImage))
                    .apply(requestOptions)
                    .into(binding.ivImage);

        binding.viewPagerInfo.setAdapter(adapterViewPagerInfo);
        binding.viewPagerInfo.setOffscreenPageLimit(3);

        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }


    private Uri takeImageFromAssets(String image) {
        return Uri.parse("file:///android_asset/workouts_girls/" + image);
    }

    public CardView getCardView() {
        return binding.cardView;
    }
}