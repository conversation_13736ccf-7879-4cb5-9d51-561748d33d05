package com.vgfit.shefit.fragment.premium.redesign.adapter;

import android.content.Context;
import android.net.Uri;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.vgfit.shefit.databinding.ItemWorkVideoPlanBinding;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.text.TextTwoRow;

import java.util.ArrayList;


public class AdapterWorkoutPreview extends RecyclerView.Adapter<AdapterWorkoutPreview.WorkoutViewHolder> {
    private final ArrayList<String> listKcal;
    private final ArrayList<String> listTime;
    private final ArrayList<String> listDay;

    public AdapterWorkoutPreview() {
        this.listKcal = getListKcal();
        this.listTime = getListTime();
        this.listDay = getListDay();
    }

    @NonNull
    @Override
    public WorkoutViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ItemWorkVideoPlanBinding binding = ItemWorkVideoPlanBinding.inflate(inflater, parent, false);
        return new WorkoutViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull WorkoutViewHolder holder, int position) {
        int finalPosition = position;
        if (position > 28) {
            finalPosition = finalPosition % 28;
        }
        int order = finalPosition + 1;
        int pp = finalPosition % 7;
        loadImageWork(holder.itemView.getContext(), order, holder.imagePlan);

        holder.calInfo.setText(listKcal.get(pp));
        holder.timeInfo.setText(listTime.get(pp));
        TextTwoRow.setText(holder.titleWorkout, holder.titleWorkoutSecond, listDay.get(pp));
    }

    private void loadImageWork(Context context, int position, ImageView imagePlan) {
        if (context != null) {
            try {
                Glide.with(context).load(Uri.parse("file:///android_asset/imageDay/" + position + ".webp")).centerCrop().into(imagePlan);
            } catch (Exception e) {
                Log.e("LoadImageError", "error this-->" + e.getMessage());
            }

        }
    }

    @Override
    public int getItemCount() {
        return 300;
    }

    public static class WorkoutViewHolder extends RecyclerView.ViewHolder {
        ImageView imagePlan;
        TextView titleWorkout;
        TextView titleWorkoutSecond;
        TextView calInfo;
        TextView timeInfo;

        public WorkoutViewHolder(@NonNull ItemWorkVideoPlanBinding binding) {
            super(binding.getRoot());
            imagePlan = binding.imagePlan;
            titleWorkout = binding.titleWorkout;
            titleWorkoutSecond = binding.titleWorkoutSecond;
            calInfo = binding.calInfo;
            timeInfo = binding.calInfo;
        }
    }

    private ArrayList<String> getListKcal() {
        ArrayList<String> listKcal = new ArrayList<>();
        listKcal.add("410 kcal");
        listKcal.add("420 kcal");
        listKcal.add("430 kcal");
        listKcal.add("450 kcal");
        listKcal.add("450 kcal");
        listKcal.add("460 kcal");
        listKcal.add("480 kcal");
        return listKcal;
    }

    private ArrayList<String> getListTime() {
        ArrayList<String> listTime = new ArrayList<>();
        listTime.add("10 min");
        listTime.add("15 min");
        listTime.add("20 min");
        listTime.add("20 min");
        listTime.add("25 min");
        listTime.add("30 min");
        listTime.add("30 min");
        return listTime;
    }

    private ArrayList<String> getListDay() {
        ArrayList<String> listDay = new ArrayList<>();
        listDay.add(Translate.getValue("monday’s_workout"));
        listDay.add(Translate.getValue("tuesday’s_workout"));
        listDay.add(Translate.getValue("wednesday’s_workout"));
        listDay.add(Translate.getValue("thursday’s_workout"));
        listDay.add(Translate.getValue("friday’s_workout"));
        listDay.add(Translate.getValue("saturday’s_workout"));
        listDay.add(Translate.getValue("sunday’s_workout"));
        return listDay;
    }
}
