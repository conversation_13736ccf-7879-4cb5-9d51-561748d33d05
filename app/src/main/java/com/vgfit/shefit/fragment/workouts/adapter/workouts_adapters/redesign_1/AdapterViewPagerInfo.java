package com.vgfit.shefit.fragment.workouts.adapter.workouts_adapters.redesign_1;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.vgfit.shefit.R;
import com.vgfit.shefit.fragment.workouts.model.Level;
import com.vgfit.shefit.realm.Superset;

import java.util.ArrayList;
import java.util.List;

import io.realm.Realm;

public class AdapterViewPagerInfo extends PagerAdapter {
    private RequestOptions requestOptions;
    private List<Superset> list;
    private List<Level> levelList;
    private Context context;
    private int positionSuperset;

    public AdapterViewPagerInfo(Context context, List<Superset> list, int position) {
        this.list = list;
        this.context = context;
        this.positionSuperset = position;
        this.levelList = prepareScaleList(positionSuperset);
        requestOptions = new RequestOptions();
        requestOptions = requestOptions
                .transforms(new CenterCrop(), new RoundedCorners(30))
                .diskCacheStrategy(DiskCacheStrategy.ALL);
    }

    @Override
    public int getCount() {
//        int size = 3;
//        if (levelList != null) size = levelList.size();
        return 3;
    }

    @Override
    public float getPageWidth(int position) {
        return 0.98f;
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return (view == object);
    }

    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, int position) {
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View view = inflater.inflate(R.layout.item_viewpager_info, container, false);

//        imageView.setOnClickListener(v -> imageSupersetClick.onClickedImageSuperset(positionSuperset, position));
        container.addView(view);
        return view;
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        container.removeView((RelativeLayout) object);
    }

    private List<Level> prepareScaleList(int position) {
        Realm realm = Realm.getDefaultInstance();
        ArrayList<Level> levels = new ArrayList<>();
        for (int i = 0; i < list.get(position).getWorkouts().size(); i++) {
            levels.add(new Level(list.get(position).getWorkouts().get(i).getLevel(), 0));
        }
        realm.close();
        return levels;
    }
}