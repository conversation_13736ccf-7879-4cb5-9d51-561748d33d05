package com.vgfit.shefit.fragment.userprofile.progress.presenter;

import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.fragment.userprofile.progress.view.ProgressUserView;
import com.vgfit.shefit.realm.Achievement;
import com.vgfit.shefit.realm.WorkoutHistory;
import com.vgfit.shefit.repository.WorkoutHistoryRepository;
import com.vgfit.shefit.util.AchievementHelper;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class ProgressUserPresenter {

    private final ProgressUserView view;
    private final WorkoutHistoryRepository repository;
    private static final String KEY_LANGUAGE = "langDevice";
    private final Locale currentLocale;
    private final PrefsUtilsWtContext prefsUtilsWtContext;
    private static final String MONDAY = "mondayDay";
    private static final String TUESDAY = "tuesdayDay";
    private static final String WEDNESDAY = "wednesdayDay";
    private static final String THURSDAY = "thursdayDay";
    private static final String FRIDAY = "fridayDay";
    private static final String SATURDAY = "satDay";
    private static final String SUNDAY = "sunDay";

    public ProgressUserPresenter(ProgressUserView view, PrefsUtilsWtContext prefsUtilsWtContext) {
        this.prefsUtilsWtContext = prefsUtilsWtContext;
        this.view = view;
        this.repository = new WorkoutHistoryRepository();
        String lang = Locale.getDefault().getLanguage();
        String langUser = prefsUtilsWtContext.getStringPreferenceProfile(KEY_LANGUAGE);
        if (langUser != null) {
            lang = langUser;
        }
        currentLocale = new Locale(lang);
    }

    public void loadWorkoutHistory() {
        // Default to weekly view
        loadWorkoutHistoryByPeriod(0);
        // Load achievements
        loadAchievements();
    }

    public void loadAchievements() {
        List<Achievement> allAchievements = AchievementHelper.getAllAchievements();
        view.displayAchievements(allAchievements);
    }

    public void loadWorkoutHistoryByPeriod(int periodType) {
        List<WorkoutHistory> workoutHistoryList;

        switch (periodType) {
            case 0: // Week
                workoutHistoryList = repository.getWorkoutHistoryForCurrentWeek();
                displayWeekData(workoutHistoryList, periodType);
                break;

            case 1: // Month
                workoutHistoryList = repository.getWorkoutHistoryForCurrentMonth();
                displayMonthData(workoutHistoryList, periodType);
                break;

            case 2: // Year
                workoutHistoryList = repository.getWorkoutHistoryForCurrentYear();
                displayYearData(workoutHistoryList, periodType);
                break;

            default:
                loadWorkoutHistoryByPeriod(0); // Fallback to week
                return;
        }

        // Always update the view
        view.displayWorkoutHistory(workoutHistoryList);

        // Load and display achievements
        loadAchievements();
    }

    private void displayWeekData(List<WorkoutHistory> workoutHistoryList, int periodType) {
        // Calculate correct statistics
        int[] basicStats = calculateBasicStats(workoutHistoryList);
        int totalDuration = basicStats[0];
        int totalCalories = basicStats[1];
        int[] weekStats = calculateWeekWorkoutStats(workoutHistoryList);
        int totalWorkouts = weekStats[0];
        int completedWorkouts = weekStats[1];

        view.displayStatistics(completedWorkouts, totalWorkouts, totalDuration, totalCalories);

        // Setup calendar and date formatting
        Calendar calendar = Calendar.getInstance(currentLocale);
        int firstDayOfWeek = calendar.getFirstDayOfWeek();

        // Format date range
        String[] dateInfo = formatWeekDateRange(calendar);
        String year = dateInfo[0];
        String dateRange = dateInfo[1];

        // Create week chart data
        List<Integer> progressValues = new ArrayList<>();
        List<String> labels = getLocalizedWeekdayLabels();
        List<Integer> missingList = new ArrayList<>();

        // Map days of week to our day constants
        String[] dayKeys = mapDaysOfWeek(firstDayOfWeek);

        // Initialize progress values
        for (int i = 0; i < 7; i++) {
            progressValues.add(0);
        }

        // Calculate current day index (only if we're viewing current week)
        int selectedIndex = calculateSelectedDayIndex(calendar);

        // Note: earliestDayIndex not needed anymore - using date comparison in missing list

        int[] progressData = processWeekWorkoutHistory(workoutHistoryList, progressValues, dayKeys, firstDayOfWeek);
        int totalPlannedProgress = progressData[0];
        int totalCompletedProgress = progressData[1];

        // Calculate completion percentage
        int completionPercent = totalPlannedProgress > 0 ?
                (totalCompletedProgress * 100) / totalPlannedProgress : 0;

        view.displayDateRange(year, dateRange, completionPercent + "%");

        // Create missing list
        createWeekMissingList(progressValues, dayKeys, selectedIndex, missingList);

        view.displayProgressChart(progressValues, labels, periodType, selectedIndex, missingList);
    }

    private String[] formatWeekDateRange(Calendar calendar) {
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
        Date weekStart = calendar.getTime();
        calendar.add(Calendar.DAY_OF_WEEK, 6);
        Date weekEnd = calendar.getTime();

        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy", currentLocale);
        SimpleDateFormat monthFormat = new SimpleDateFormat("MMMM", currentLocale);
        SimpleDateFormat dayFormat = new SimpleDateFormat("d", currentLocale);

        String year = yearFormat.format(weekStart);
        String month = monthFormat.format(weekStart);
        String startDay = dayFormat.format(weekStart);
        String endDay = dayFormat.format(weekEnd);

        String dateRange = month + " " + startDay + "-" + endDay;

        return new String[]{year, dateRange};
    }

    private String[] mapDaysOfWeek(int firstDayOfWeek) {
        String[] dayKeys = new String[7];

        for (int i = 0; i < 7; i++) {
            int dayOfWeek = (firstDayOfWeek + i - 1) % 7 + 1; // Convert to 1-based Calendar constants
            dayKeys[i] = getDayKey(dayOfWeek);
        }

        return dayKeys;
    }

    private int calculateSelectedDayIndex(Calendar calendar) {
        Calendar today = Calendar.getInstance(currentLocale);

        // Set calendar to start of current week for comparison
        Calendar weekStart = (Calendar) calendar.clone();
        weekStart.set(Calendar.DAY_OF_WEEK, weekStart.getFirstDayOfWeek());
        weekStart.set(Calendar.HOUR_OF_DAY, 0);
        weekStart.set(Calendar.MINUTE, 0);
        weekStart.set(Calendar.SECOND, 0);
        weekStart.set(Calendar.MILLISECOND, 0);

        Calendar weekEnd = (Calendar) weekStart.clone();
        weekEnd.add(Calendar.DAY_OF_WEEK, 6);
        weekEnd.set(Calendar.HOUR_OF_DAY, 23);
        weekEnd.set(Calendar.MINUTE, 59);
        weekEnd.set(Calendar.SECOND, 59);

        // Check if today is in the displayed week
        if (today.getTime().before(weekStart.getTime()) || today.getTime().after(weekEnd.getTime())) {
            // Today is not in this week, so no day should be selected
            return -1;
        }

        // Today is in this week, calculate the index
        int currentDayOfWeek = today.get(Calendar.DAY_OF_WEEK);
        int firstDayOfWeek = calendar.getFirstDayOfWeek();

        // Convert to 0-based index (0 = first day of week)
        return (currentDayOfWeek - firstDayOfWeek + 7) % 7;
    }

    // Removed getEarliestWeekDayIndex - no longer needed with date-based missing list logic

    private int[] processWeekWorkoutHistory(List<WorkoutHistory> workoutHistoryList,
                                            List<Integer> progressValues,
                                            String[] dayKeys,
                                            int firstDayOfWeek) {
        // Calculate total planned progress based on selected days
        int totalPlannedProgress = 0;
        for (String dayKey : dayKeys) {
            if (existDayPlan(dayKey)) {
                totalPlannedProgress += 100;
            }
        }

        int totalCompletedProgress = 0;
        Calendar historyCalendar = Calendar.getInstance(currentLocale);

        for (WorkoutHistory workout : workoutHistoryList) {
            Date workoutDate = workout.getCompletedDate();
            if (workoutDate == null) continue;

            historyCalendar.setTime(workoutDate);
            int workoutDayOfWeek = historyCalendar.get(Calendar.DAY_OF_WEEK);

            // Convert to 0-based index relative to first day of week
            int dayIndex = (workoutDayOfWeek - firstDayOfWeek + 7) % 7;

            int currentProgress = progressValues.get(dayIndex);
            int workoutProgress = workout.getProgressPercentage();
            boolean isDayPlan = WorkoutHistory.TYPE_DAYPLAN.equals(workout.getWorkoutType());

            if (isDayPlan) {
                // DayPlan has absolute priority - take maximum progress from DayPlan workouts
                if (workoutProgress > currentProgress) {
                    progressValues.set(dayIndex, workoutProgress);
                }
            } else {
                // Regular workout only if no DayPlan exists for this day and progress is higher
                // We need to check if there's already a DayPlan for this day
                boolean hasDayPlanForThisDay = hasDayPlanWorkoutForDay(workoutHistoryList, dayIndex, firstDayOfWeek);
                if (!hasDayPlanForThisDay && workoutProgress > currentProgress) {
                    progressValues.set(dayIndex, workoutProgress);
                }
            }

            // Count completed progress for DayPlan on planned days
            if (isDayPlan && existDayPlan(dayKeys[dayIndex])) {
                totalCompletedProgress += workoutProgress;
            }
        }

        return new int[]{totalPlannedProgress, totalCompletedProgress};
    }

    private boolean hasDayPlanWorkoutForDay(List<WorkoutHistory> workoutHistoryList, int targetDayIndex, int firstDayOfWeek) {
        Calendar historyCalendar = Calendar.getInstance(currentLocale);

        for (WorkoutHistory workout : workoutHistoryList) {
            if (workout.getCompletedDate() == null) continue;

            historyCalendar.setTime(workout.getCompletedDate());
            int workoutDayOfWeek = historyCalendar.get(Calendar.DAY_OF_WEEK);
            int dayIndex = (workoutDayOfWeek - firstDayOfWeek + 7) % 7;

            if (dayIndex == targetDayIndex && WorkoutHistory.TYPE_DAYPLAN.equals(workout.getWorkoutType())) {
                return true;
            }
        }
        return false;
    }

    private boolean hasDayPlanWorkoutForMonth(List<WorkoutHistory> workoutHistoryList, String targetDateKey) {
        for (WorkoutHistory workout : workoutHistoryList) {
            if (workout.getCompletedDate() == null) continue;

            Calendar historyCalendar = Calendar.getInstance(currentLocale);
            historyCalendar.setTime(workout.getCompletedDate());
            String dateKey = historyCalendar.get(Calendar.DAY_OF_MONTH) + "";

            if (dateKey.equals(targetDateKey) && WorkoutHistory.TYPE_DAYPLAN.equals(workout.getWorkoutType())) {
                return true;
            }
        }
        return false;
    }

    private void createWeekMissingList(List<Integer> progressValues,
                                       String[] dayKeys,
                                       int selectedIndex,
                                       List<Integer> missingList) {
        // Get earliest workout date from ALL history (not just current week)
        Date earliestWorkoutDate = getEarliestWorkoutDateFromAllHistory();
        Calendar calendar = Calendar.getInstance(currentLocale);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());

        for (int i = 0; i < progressValues.size(); i++) {
            int progress = progressValues.get(i);
            boolean isDaySelected = existDayPlan(dayKeys[i]);

            // Calculate the actual date for this day index
            Calendar dayCalendar = (Calendar) calendar.clone();
            dayCalendar.add(Calendar.DAY_OF_WEEK, i);
            Date currentDayDate = dayCalendar.getTime();

            // A day is missing if:
            // 1. No progress (0%)
            // 2. Day has passed (before current day)
            // 3. Day is after earliest workout date (user started using app)
            // 4. Day is selected for workouts
            boolean dayHasPassed = selectedIndex == -1 || (i < selectedIndex);
            boolean isAfterEarliestWorkout = earliestWorkoutDate == null ||
                                           !currentDayDate.before(earliestWorkoutDate);
            boolean isMissing = (progress == 0 && dayHasPassed &&
                               isAfterEarliestWorkout && isDaySelected);

            missingList.add(isMissing ? 1 : 0);
        }
    }

    private List<String> getLocalizedWeekdayLabels() {
        List<String> labels = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("E", currentLocale);
        Calendar calendar = Calendar.getInstance(currentLocale);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());

        for (int i = 0; i < 7; i++) {
            String dayLabel = sdf.format(calendar.getTime());
            // Take just the first letter of the day name
            if (!dayLabel.isEmpty()) {
                dayLabel = dayLabel.substring(0, 1);
            }
            labels.add(dayLabel);
            calendar.add(Calendar.DAY_OF_WEEK, 1);
        }

        return labels;
    }

    private void displayMonthData(List<WorkoutHistory> workoutHistoryList, int periodType) {
        // Calculate correct statistics
        int[] basicStats = calculateBasicStats(workoutHistoryList);
        int totalDuration = basicStats[0];
        int totalCalories = basicStats[1];
        int[] monthStats = calculateMonthWorkoutStats(workoutHistoryList);
        int totalWorkouts = monthStats[0];
        int completedWorkouts = monthStats[1];

        view.displayStatistics(completedWorkouts, totalWorkouts, totalDuration, totalCalories);

        // Get current month and format date range
        Calendar calendar = Calendar.getInstance(currentLocale);
        String dateRange = new SimpleDateFormat("MMMM yyyy", currentLocale).format(calendar.getTime());

        // Create chart data structures
        List<Integer> progressValues = new ArrayList<>();
        List<String> labels = new ArrayList<>();
        List<Integer> missingList = new ArrayList<>();

        // Setup calendar and indexes
        int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int currentDayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        int currentMonth = calendar.get(Calendar.MONTH);
        int currentYear = calendar.get(Calendar.YEAR);

        // Note: earliestDayIndex not needed anymore - using date comparison in missing list

        // Initialize progress values and labels
        initializeMonthLabelsAndProgress(calendar, daysInMonth, labels, progressValues);

        // Process workout history
        processMonthWorkoutHistory(workoutHistoryList, progressValues, currentMonth, currentYear);

        // Calculate completion percentage
        int[] progressData = calculateMonthProgress(calendar, daysInMonth, progressValues);
        int totalPlannedProgress = progressData[0];
        int totalCompletedProgress = progressData[1];

        int completionPercent = totalPlannedProgress > 0 ?
                (totalCompletedProgress * 100) / totalPlannedProgress : 0;

        view.displayDateRange("", dateRange, completionPercent + "%");

        // Create missing list
        createMonthMissingList(calendar, progressValues, currentDayOfMonth - 1, missingList);

        view.displayProgressChart(progressValues, labels, periodType, currentDayOfMonth - 1, missingList);
    }

    private void displayYearData(List<WorkoutHistory> workoutHistoryList, int periodType) {
        int[] basicStats = calculateBasicStats(workoutHistoryList);
        int totalDuration = basicStats[0];
        int totalCalories = basicStats[1];

        // Calculate correct total and completed workouts for the year
        int[] yearStats = calculateYearWorkoutStats(workoutHistoryList);
        int totalWorkouts = yearStats[0];
        int completedWorkouts = yearStats[1];

        view.displayStatistics(completedWorkouts, totalWorkouts, totalDuration, totalCalories);

        Calendar calendar = Calendar.getInstance(currentLocale);
        String dateRange = new SimpleDateFormat("yyyy", currentLocale).format(calendar.getTime());

        List<Integer> progressValues = new ArrayList<>();
        List<String> labels = getLocalizedMonthLabels();
        List<Integer> missingList = new ArrayList<>();

        int currentMonth = calendar.get(Calendar.MONTH);
        int currentYear = calendar.get(Calendar.YEAR);

        Date earliestWorkoutDate = getEarliestWorkoutDateFromAllHistory();
        int earliestMonthIndex = getEarliestMonthIndex(earliestWorkoutDate, currentYear);

        Map<Integer, List<WorkoutHistory>> workoutsByMonth = groupWorkoutsByMonth(workoutHistoryList, currentYear);

        int[] progressData = calculateYearProgress(calendar, currentYear, currentMonth, earliestMonthIndex, workoutsByMonth, progressValues);
        int totalPlannedProgress = progressData[0];
        int totalCompletedProgress = progressData[1];

        int completionPercent = totalPlannedProgress > 0 ? (totalCompletedProgress * 100 / totalPlannedProgress) : 0;
        view.displayDateRange("", dateRange, completionPercent + "%");

        for (int i = 0; i < progressValues.size(); i++) {
            int progress = progressValues.get(i);

            // Calculate the actual date for this month
            Calendar monthCalendar = Calendar.getInstance(currentLocale);
            monthCalendar.set(Calendar.YEAR, currentYear);
            monthCalendar.set(Calendar.MONTH, i);
            monthCalendar.set(Calendar.DAY_OF_MONTH, 1);
            Date currentMonthDate = monthCalendar.getTime();

            // A month is missing if:
            // 1. No progress (0%)
            // 2. Month has passed (before current month)
            // 3. Month is after earliest workout date (user started using app)
            boolean monthHasPassed = i < currentMonth;
            boolean isAfterEarliestWorkout = earliestWorkoutDate == null ||
                                           !currentMonthDate.before(earliestWorkoutDate);
            boolean isMissing = (progress == 0 && monthHasPassed && isAfterEarliestWorkout);

            missingList.add(isMissing ? 1 : 0);
        }

        view.displayProgressChart(progressValues, labels, periodType, currentMonth, missingList);
    }

    private int getEarliestMonthIndex(Date earliestDate, int currentYear) {
        Calendar earliestCalendar = Calendar.getInstance(currentLocale);
        earliestCalendar.setTime(earliestDate);
        return earliestCalendar.get(Calendar.YEAR) == currentYear ? earliestCalendar.get(Calendar.MONTH) : 0;
    }

    private Map<Integer, List<WorkoutHistory>> groupWorkoutsByMonth(List<WorkoutHistory> workoutHistoryList, int currentYear) {
        Map<Integer, List<WorkoutHistory>> workoutsByMonth = new HashMap<>();
        Calendar historyCalendar = Calendar.getInstance(currentLocale);
        for (int i = 0; i < 12; i++) workoutsByMonth.put(i, new ArrayList<>());

        for (WorkoutHistory workout : workoutHistoryList) {
            Date workoutDate = workout.getCompletedDate();

            // Skip if workout date is null or not from current year
            if (workoutDate == null) continue;

            historyCalendar.setTime(workoutDate);
            if (historyCalendar.get(Calendar.YEAR) == currentYear) {
                int month = historyCalendar.get(Calendar.MONTH);
                List<WorkoutHistory> monthList = workoutsByMonth.get(month);
                if (monthList != null) {
                    monthList.add(workout);
                }
            }
        }

        return workoutsByMonth;
    }

    private int[] calculateYearProgress(Calendar calendar, int year, int currentMonth, int earliestMonthIndex,
                                        Map<Integer, List<WorkoutHistory>> workoutsByMonth, List<Integer> progressValues) {
        int totalPlannedProgress = 0;
        int totalCompletedProgress = 0;
        Calendar historyCalendar = Calendar.getInstance(currentLocale);

        for (int month = 0; month < 12; month++) {
            progressValues.add(0);
            // Skip future months or months before user started using the app
            if (month > currentMonth || month < earliestMonthIndex) continue;

            List<WorkoutHistory> monthWorkouts = workoutsByMonth.get(month);
            setCalendarDay(calendar, year, month, 1);
            int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);

            int plannedWorkoutsInMonth = 0;
            for (int day = 1; day <= daysInMonth; day++) {
                setCalendarDay(calendar, year, month, day);
                if (existDayPlan(getDayKey(calendar.get(Calendar.DAY_OF_WEEK)))) {
                    plannedWorkoutsInMonth++;
                }
            }

            Map<String, Integer> dayProgressMap = new HashMap<>();
            int totalProgressInMonth = 0;

            if (monthWorkouts != null) {
                for (WorkoutHistory workout : monthWorkouts) {
                Date workoutDate = workout.getCompletedDate();

                // Skip if workout date is null or not on a selected day
                if (workoutDate == null) continue;

                historyCalendar.setTime(workoutDate);
                String dayKey = getDayKey(historyCalendar.get(Calendar.DAY_OF_WEEK));
                if (!existDayPlan(dayKey)) continue;

                int day = historyCalendar.get(Calendar.DAY_OF_MONTH);
                String key = String.valueOf(day);
                int progress = workout.getProgressPercentage();

                // For progress chart, keep the highest progress per day
                Integer currentProgress = dayProgressMap.get(key);
                int existingProgress = (currentProgress != null) ? currentProgress : 0;
                boolean isDayPlan = WorkoutHistory.TYPE_DAYPLAN.equals(workout.getWorkoutType());

                if (isDayPlan) {
                    // DayPlan has absolute priority - take maximum progress from DayPlan workouts
                    if (progress > existingProgress) {
                        dayProgressMap.put(key, progress);
                    }
                } else {
                    // Regular workout only if no DayPlan exists for this day and progress is higher
                    boolean hasDayPlanForThisDay = hasDayPlanWorkoutForMonth(monthWorkouts, key);
                    if (!hasDayPlanForThisDay && progress > existingProgress) {
                        dayProgressMap.put(key, progress);
                    }
                }

                // Count ALL Daily Plan workouts (regardless of completion percentage)
                if (WorkoutHistory.TYPE_DAYPLAN.equals(workout.getWorkoutType())) {
                    totalProgressInMonth += progress;  // Add actual progress for percentage calculation
                }
                }
            }

            int completedWorkoutsInMonth = dayProgressMap.values().stream().mapToInt(Integer::intValue).sum();
            int monthProgress = plannedWorkoutsInMonth > 0 ? Math.min((completedWorkoutsInMonth * 100) / (plannedWorkoutsInMonth * 100), 100) : 0;
            progressValues.set(month, monthProgress);

            totalPlannedProgress += plannedWorkoutsInMonth * 100;
            totalCompletedProgress += totalProgressInMonth;  // Use actual progress sum for percentage calculation
        }

        return new int[]{totalPlannedProgress, totalCompletedProgress};
    }

    private int[] calculateYearWorkoutStats(List<WorkoutHistory> workoutHistoryList) {
        Calendar calendar = Calendar.getInstance(currentLocale);
        int currentYear = calendar.get(Calendar.YEAR);

        // Count selected workout days using the same logic as other methods
        int selectedDaysPerWeek = 0;
        String[] dayKeys = {MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY};
        for (String dayKey : dayKeys) {
            if (existDayPlan(dayKey)) {
                selectedDaysPerWeek++;
            }
        }

        // Calculate total weeks in year (52 weeks)
        int weeksInYear = 52;
        int totalWorkouts = selectedDaysPerWeek * weeksInYear;

        // Count Daily Plan workouts - one per day with max progress, excluding 0% progress
        int completedWorkouts = 0;
        Map<String, Integer> dailyMaxProgress = new HashMap<>(); // date -> max progress
        Calendar workoutCalendar = Calendar.getInstance(currentLocale);

        // First pass: find max progress per day for Daily Plan workouts
        for (WorkoutHistory workout : workoutHistoryList) {
            if (workout.getCompletedDate() == null) continue;

            workoutCalendar.setTime(workout.getCompletedDate());
            if (workoutCalendar.get(Calendar.YEAR) != currentYear) continue;

            // Only process Daily Plan workouts with progress > 0
            if (WorkoutHistory.TYPE_DAYPLAN.equals(workout.getWorkoutType()) &&
                    workout.getProgressPercentage() > 0) {

                // Check if workout was done on a selected day
                int dayOfWeek = workoutCalendar.get(Calendar.DAY_OF_WEEK);
                String dayKey = getDayKey(dayOfWeek);
                if (existDayPlan(dayKey)) {

                    // Create unique key for this date
                    String dateKey = workoutCalendar.get(Calendar.YEAR) + "-" +
                            workoutCalendar.get(Calendar.MONTH) + "-" +
                            workoutCalendar.get(Calendar.DAY_OF_MONTH);

                    // Keep only the max progress for this day
                    int currentProgress = workout.getProgressPercentage();
                    Integer existingProgress = dailyMaxProgress.get(dateKey);
                    int maxProgress = (existingProgress != null) ? existingProgress : 0;
                    if (currentProgress > maxProgress) {
                        dailyMaxProgress.put(dateKey, currentProgress);
                    }
                }
            }
        }

        // Count days with Daily Plan workouts (progress > 0)
        completedWorkouts = dailyMaxProgress.size();

        return new int[]{totalWorkouts, completedWorkouts};
    }

    private int[] calculateWeekWorkoutStats(List<WorkoutHistory> workoutHistoryList) {
        Calendar calendar = Calendar.getInstance(currentLocale);

        // Count selected workout days using the same logic as other methods
        int totalWorkouts = 0;
        String[] dayKeys = {MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY};
        for (String dayKey : dayKeys) {
            if (existDayPlan(dayKey)) {
                totalWorkouts++;
            }
        }

        // Count completed Daily Plan workouts - one per day with max progress, excluding 0% progress
        Map<String, Integer> dailyMaxProgress = new HashMap<>(); // date -> max progress
        Calendar workoutCalendar = Calendar.getInstance(currentLocale);

        // Get current week start and end
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
        Date weekStart = calendar.getTime();
        calendar.add(Calendar.DAY_OF_WEEK, 6);
        Date weekEnd = calendar.getTime();

        for (WorkoutHistory workout : workoutHistoryList) {
            if (workout.getCompletedDate() == null) continue;

            Date workoutDate = workout.getCompletedDate();
            if (workoutDate.before(weekStart) || workoutDate.after(weekEnd)) continue;

            workoutCalendar.setTime(workoutDate);

            // Only process Daily Plan workouts with progress > 0
            if (WorkoutHistory.TYPE_DAYPLAN.equals(workout.getWorkoutType()) &&
                    workout.getProgressPercentage() > 0) {

                // Check if workout was done on a selected day
                int dayOfWeek = workoutCalendar.get(Calendar.DAY_OF_WEEK);
                String dayKey = getDayKey(dayOfWeek);
                if (existDayPlan(dayKey)) {

                    // Create unique key for this date
                    String dateKey = workoutCalendar.get(Calendar.YEAR) + "-" +
                            workoutCalendar.get(Calendar.MONTH) + "-" +
                            workoutCalendar.get(Calendar.DAY_OF_MONTH);

                    // Keep only the max progress for this day
                    int currentProgress = workout.getProgressPercentage();
                    Integer existingProgress = dailyMaxProgress.get(dateKey);
                    int maxProgress = (existingProgress != null) ? existingProgress : 0;
                    if (currentProgress > maxProgress) {
                        dailyMaxProgress.put(dateKey, currentProgress);
                    }
                }
            }
        }

        int completedWorkouts = dailyMaxProgress.size();
        return new int[]{totalWorkouts, completedWorkouts};
    }

    private int[] calculateMonthWorkoutStats(List<WorkoutHistory> workoutHistoryList) {
        Calendar calendar = Calendar.getInstance(currentLocale);
        int currentMonth = calendar.get(Calendar.MONTH);
        int currentYear = calendar.get(Calendar.YEAR);

        int totalWorkouts = calculateTotalWorkoutsForMonth(calendar);
        int completedWorkouts = calculateCompletedWorkoutsForMonth(workoutHistoryList, currentMonth, currentYear);

        return new int[]{totalWorkouts, completedWorkouts};
    }

    private int calculateTotalWorkoutsForMonth(Calendar calendar) {
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        int totalWorkouts = 0;

        // Count selected days in current month
        for (int day = 1; day <= daysInMonth; day++) {
            calendar.set(Calendar.DAY_OF_MONTH, day);
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
            String dayKey = getDayKey(dayOfWeek);
            if (existDayPlan(dayKey)) {
                totalWorkouts++;
            }
        }
        return totalWorkouts;
    }

    private int calculateCompletedWorkoutsForMonth(List<WorkoutHistory> workoutHistoryList, int currentMonth, int currentYear) {
        Map<String, Integer> dailyMaxProgress = new HashMap<>();
        Calendar workoutCalendar = Calendar.getInstance(currentLocale);

        for (WorkoutHistory workout : workoutHistoryList) {
            if (workout.getCompletedDate() == null) continue;

            workoutCalendar.setTime(workout.getCompletedDate());

            if (isValidMonthWorkout(workoutCalendar, currentMonth, currentYear, workout)) {
                processMonthWorkout(workout, workoutCalendar, dailyMaxProgress);
            }
        }

        return dailyMaxProgress.size();
    }

    private boolean isValidMonthWorkout(Calendar workoutCalendar, int currentMonth, int currentYear, WorkoutHistory workout) {
        return workoutCalendar.get(Calendar.YEAR) == currentYear &&
                workoutCalendar.get(Calendar.MONTH) == currentMonth &&
                WorkoutHistory.TYPE_DAYPLAN.equals(workout.getWorkoutType()) &&
                workout.getProgressPercentage() > 0;
    }

    private void processMonthWorkout(WorkoutHistory workout, Calendar workoutCalendar, Map<String, Integer> dailyMaxProgress) {
        int dayOfWeek = workoutCalendar.get(Calendar.DAY_OF_WEEK);
        String dayKey = getDayKey(dayOfWeek);

        if (existDayPlan(dayKey)) {
            String dateKey = createDateKey(workoutCalendar);
            updateMaxProgress(workout, dateKey, dailyMaxProgress);
        }
    }

    private String createDateKey(Calendar calendar) {
        return calendar.get(Calendar.YEAR) + "-" +
                calendar.get(Calendar.MONTH) + "-" +
                calendar.get(Calendar.DAY_OF_MONTH);
    }

    private void updateMaxProgress(WorkoutHistory workout, String dateKey, Map<String, Integer> dailyMaxProgress) {
        int currentProgress = workout.getProgressPercentage();
        Integer existingProgress = dailyMaxProgress.get(dateKey);
        int maxProgress = (existingProgress != null) ? existingProgress : 0;

        if (currentProgress > maxProgress) {
            dailyMaxProgress.put(dateKey, currentProgress);
        }
    }

    private List<String> getLocalizedMonthLabels() {
        List<String> labels = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("MMM", currentLocale);
        Calendar calendar = Calendar.getInstance(currentLocale);
        calendar.set(Calendar.MONTH, Calendar.JANUARY);

        for (int i = 0; i < 12; i++) {
            String monthLabel = sdf.format(calendar.getTime());
            // Take just the first letter of the month name
            if (!monthLabel.isEmpty()) {
                monthLabel = monthLabel.substring(0, 1);
            }
            labels.add(monthLabel);
            calendar.add(Calendar.MONTH, 1);
        }

        return labels;
    }

    private int[] calculateBasicStats(List<WorkoutHistory> workoutHistoryList) {
        int totalDuration = 0;
        int totalCalories = 0;

        for (WorkoutHistory workout : workoutHistoryList) {
            totalDuration += workout.getDuration();
            totalCalories += workout.getKcalBurned();
        }

        return new int[]{totalDuration, totalCalories};
    }

    private boolean existDayPlan(String keyDay) {
        return prefsUtilsWtContext.getBooleanPreference(keyDay, false);
    }

    // Helper method to map Calendar dayOfWeek to dayKey string
    private String getDayKey(int dayOfWeek) {
        switch (dayOfWeek) {
            case Calendar.MONDAY:
                return MONDAY;
            case Calendar.TUESDAY:
                return TUESDAY;
            case Calendar.WEDNESDAY:
                return WEDNESDAY;
            case Calendar.THURSDAY:
                return THURSDAY;
            case Calendar.FRIDAY:
                return FRIDAY;
            case Calendar.SATURDAY:
                return SATURDAY;
            case Calendar.SUNDAY:
                return SUNDAY;
            default:
                return MONDAY;
        }
    }

    // Helper method to set Calendar date fields
    private void setCalendarDay(Calendar calendar, int year, int month, int dayOfMonth) {
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);
    }

    // Removed getEarliestWorkoutDate - replaced by getEarliestWorkoutDateFromAllHistory for consistency

    private Date getEarliestWorkoutDateFromAllHistory() {
        // Use existing repository method to get all workout history
        List<WorkoutHistory> allWorkouts = repository.getAllWorkoutHistory();

        Date earliestDate = null;

        for (WorkoutHistory workout : allWorkouts) {
            // Only consider Daily Plan workouts with progress > 0
            if (WorkoutHistory.TYPE_DAYPLAN.equals(workout.getWorkoutType()) &&
                workout.getProgressPercentage() > 0 &&
                workout.getCompletedDate() != null &&
                (earliestDate == null || workout.getCompletedDate().before(earliestDate))) {

                earliestDate = workout.getCompletedDate();
            }
        }

        // If no workout history found, return current date to prevent null issues
        // This ensures new users start tracking from today instead of showing "missed" days
        if (earliestDate == null) {
            return new Date();
        }

        return earliestDate;
    }

    // Removed getEarliestDayIndex - no longer needed with date-based missing list logic

    private void initializeMonthLabelsAndProgress(Calendar calendar, int daysInMonth,
                                                  List<String> labels, List<Integer> progressValues) {
        for (int i = 1; i <= daysInMonth; i++) {
            calendar.set(Calendar.DAY_OF_MONTH, i);
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);

            // Add day number as label for Sundays or first day of month
            if (dayOfWeek == Calendar.SUNDAY || i == 1) {
                labels.add(String.valueOf(i));
            } else {
                labels.add("");
            }

            // Initialize progress values
            progressValues.add(0);
        }
    }

    private void processMonthWorkoutHistory(List<WorkoutHistory> workoutHistoryList,
                                            List<Integer> progressValues,
                                            int currentMonth, int currentYear) {
        Calendar historyCalendar = Calendar.getInstance(currentLocale);

        for (WorkoutHistory workout : workoutHistoryList) {
            Date workoutDate = workout.getCompletedDate();

            // Skip if workout date is null
            if (workoutDate == null) {
                continue;
            }

            historyCalendar.setTime(workoutDate);

            // Process only if workout is in current month/year
            if (historyCalendar.get(Calendar.MONTH) == currentMonth &&
                    historyCalendar.get(Calendar.YEAR) == currentYear) {

                int dayIndex = historyCalendar.get(Calendar.DAY_OF_MONTH) - 1;
                int currentProgress = progressValues.get(dayIndex);
                int workoutProgress = workout.getProgressPercentage();
                boolean isDayPlan = WorkoutHistory.TYPE_DAYPLAN.equals(workout.getWorkoutType());

                if (isDayPlan) {
                    // DayPlan has absolute priority - take maximum progress from DayPlan workouts
                    if (workoutProgress > currentProgress) {
                        progressValues.set(dayIndex, workoutProgress);
                    }
                } else {
                    // Regular workout only if no DayPlan exists for this day and progress is higher
                    boolean hasDayPlanForThisDay = hasDayPlanWorkoutForMonthDay(workoutHistoryList, dayIndex + 1, currentMonth, currentYear);
                    if (!hasDayPlanForThisDay && workoutProgress > currentProgress) {
                        progressValues.set(dayIndex, workoutProgress);
                    }
                }
            }
        }
    }

    private int[] calculateMonthProgress(Calendar calendar, int daysInMonth, List<Integer> progressValues) {
        int totalPlannedProgress = 0;
        int totalCompletedProgress = 0;

        calendar.set(Calendar.DAY_OF_MONTH, 1);
        for (int i = 0; i < daysInMonth; i++) {
            setCalendarDay(calendar, calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), i + 1);
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
            String dayKey = getDayKey(dayOfWeek);

            if (existDayPlan(dayKey)) {
                totalPlannedProgress += 100;
                totalCompletedProgress += progressValues.get(i);
            }
        }

        return new int[]{totalPlannedProgress, totalCompletedProgress};
    }

    private void createMonthMissingList(Calendar calendar, List<Integer> progressValues,
                                        int selectedIndex,
                                        List<Integer> missingList) {
        // Get earliest workout date from ALL history (not just current month)
        Date earliestWorkoutDate = getEarliestWorkoutDateFromAllHistory();

        for (int i = 0; i < progressValues.size(); i++) {
            int progress = progressValues.get(i);
            setCalendarDay(calendar, calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), i + 1);
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
            String dayKey = getDayKey(dayOfWeek);

            // Get the actual date for this day
            Date currentDayDate = calendar.getTime();

            boolean isDaySelected = existDayPlan(dayKey);

            // A day is missing if:
            // 1. No progress (0%)
            // 2. Day has passed (before current day)
            // 3. Day is after earliest workout date (user started using app)
            // 4. Day is selected for workouts
            boolean dayHasPassed = i < selectedIndex;
            boolean isAfterEarliestWorkout = earliestWorkoutDate == null ||
                                           !currentDayDate.before(earliestWorkoutDate);
            boolean isMissing = (progress == 0 && dayHasPassed &&
                               isAfterEarliestWorkout && isDaySelected);

            missingList.add(isMissing ? 1 : 0);
        }
    }

    private boolean hasDayPlanWorkoutForMonthDay(List<WorkoutHistory> workoutHistoryList, int targetDay, int targetMonth, int targetYear) {
        Calendar historyCalendar = Calendar.getInstance(currentLocale);

        for (WorkoutHistory workout : workoutHistoryList) {
            if (workout.getCompletedDate() == null) continue;

            historyCalendar.setTime(workout.getCompletedDate());
            int workoutDay = historyCalendar.get(Calendar.DAY_OF_MONTH);
            int workoutMonth = historyCalendar.get(Calendar.MONTH);
            int workoutYear = historyCalendar.get(Calendar.YEAR);

            if (workoutDay == targetDay && workoutMonth == targetMonth && workoutYear == targetYear &&
                WorkoutHistory.TYPE_DAYPLAN.equals(workout.getWorkoutType())) {
                return true;
            }
        }
        return false;
    }
}
