package com.vgfit.shefit.fragment.premium.timer;

import android.util.Log;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class LockDays {
    public  String getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        DateFormat df = new SimpleDateFormat("EEE, MMM d, yyyy");
        String mdata = df.format(Calendar.getInstance().getTime());
        Log.e("LockDays","StartOfDay =>"+mdata);
        return mdata;
    }
    public  String getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        DateFormat df = new SimpleDateFormat("EEE, MMM d, yyyy");
        String mdata = df.format(Calendar.getInstance().getTime());
        Log.e("LockDays","EndOfDay =>"+mdata);
        return mdata;
    }
}
