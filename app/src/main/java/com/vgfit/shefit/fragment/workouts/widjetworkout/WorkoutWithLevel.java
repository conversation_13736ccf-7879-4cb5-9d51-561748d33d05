package com.vgfit.shefit.fragment.workouts.widjetworkout;


import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;
import androidx.transition.TransitionInflater;

import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.assist.FailReason;
import com.nostra13.universalimageloader.core.listener.ImageLoadingListener;
import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.MenuBottom;
import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.GlideFragmentBBinding;
import com.vgfit.shefit.fragment.workouts.WorkoutsExercisesFr;
import com.vgfit.shefit.fragment.workouts.WorkoutsVideoFr;
import com.vgfit.shefit.fragment.workouts.widjetworkout.adapter.AdapterWidgetLevel;
import com.vgfit.shefit.fragment.workouts.widjetworkout.callbacks.ShowWorkExercise;
import com.vgfit.shefit.fragment.workouts.widjetworkout.callbacks.StartWorkoutCliced;
import com.vgfit.shefit.realm.Superset;
import com.vgfit.shefit.realm.Workout;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.RCUtils;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.screen.ImageUtils;
import com.vgfit.shefit.util.text.TextTwoRow;
import com.yarolegovich.discretescrollview.DSVOrientation;
import com.yarolegovich.discretescrollview.DiscreteScrollView;
import com.yarolegovich.discretescrollview.transform.ScaleTransformer;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Objects;


public class WorkoutWithLevel extends Fragment implements DiscreteScrollView.OnItemChangedListener, StartWorkoutCliced, ShowWorkExercise {
    private GlideFragmentBBinding binding;
    private static final String EXTRA_TRANSITION_NAME_IMAGEVIEW = "transition_name";
    private static final String KEY_IMAGE_URL = "KEY_IMAGE_URL";
    private static final String KEY_ORDER_WORKOUT = "KEY_ORDER_WORKOUT";
    private static final String KEY_NAME_WORKOUT = "KEY_NAME_WORKOUT";
    private static final String EXTRA_TRANSITION_NAME_TEXTVIEW = "transition_name_textview";
    private static final String EXTRA_TRANSITION_NAME_TEXTVIEW_SECOND = "transition_name_textview_second";
    private static final String EXTRA_TRANSITION_NAME_PAGER = "transition_name_pager";
    private static final String KEY_POSITION_WORKOUT = "KEY_POSITION_WORKOUT";
    private static final String ASSETS_PHOTOS = "assets://imageWidget/";
    private static final String WEBP = ".webp";
    AdapterWidgetLevel adapterWidgetLevel;
    private ArrayList<Workout> listLevel;
    private Context context;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private int orderWorkout;
    private String nameWorkout = "";

    public WorkoutWithLevel() {
        //empty constructor
    }

    public static WorkoutWithLevel newInstance(Superset itemCarousel, String transitionName, String transitionTextview, String transitionTextviewSecond, String transitionPager, int position) {
        Bundle args = new Bundle();
        WorkoutWithLevel workoutWithLevel = new WorkoutWithLevel();
        if (itemCarousel.getWorkouts() != null) {
            args.putParcelableArrayList("superset", new ArrayList<>(itemCarousel.getWorkouts()));
        }
        args.putString(EXTRA_TRANSITION_NAME_IMAGEVIEW, transitionName);
        args.putString(EXTRA_TRANSITION_NAME_TEXTVIEW, transitionTextview);
        args.putString(EXTRA_TRANSITION_NAME_TEXTVIEW_SECOND, transitionTextviewSecond);
        args.putString(EXTRA_TRANSITION_NAME_PAGER, transitionPager);
        args.putString(KEY_IMAGE_URL, itemCarousel.getImage());
        args.putInt(KEY_ORDER_WORKOUT, itemCarousel.getOrder());
        args.putString(KEY_NAME_WORKOUT, itemCarousel.getName());
        args.putInt(KEY_POSITION_WORKOUT, position);
        workoutWithLevel.setArguments(args);
        return workoutWithLevel;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.context = getContext();
        Bundle arg = getArguments();
        listLevel = new ArrayList<>();
        if (arg != null) {
            listLevel.addAll(Objects.requireNonNull(arg.getParcelableArrayList("superset")));
            orderWorkout = arg.getInt(KEY_ORDER_WORKOUT);
            nameWorkout = arg.getString(KEY_NAME_WORKOUT);
        }
        Collections.sort(listLevel, (o1, o2) -> o1.getOrder() - o2.getOrder());
        postponeEnterTransition();
        setSharedElementEnterTransition(TransitionInflater.from(getContext()).inflateTransition(R.transition.simple_fragment_transition));
        prefsUtilsWtContext = new PrefsUtilsWtContext(context);
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = GlideFragmentBBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (getArguments() != null) {
            String transitionName = getArguments().getString(EXTRA_TRANSITION_NAME_IMAGEVIEW);
            String transitionNameTextview = getArguments().getString(EXTRA_TRANSITION_NAME_TEXTVIEW);
            String transitionNameTextviewSecond = getArguments().getString(EXTRA_TRANSITION_NAME_TEXTVIEW_SECOND);
            String transitionNamePager = getArguments().getString(EXTRA_TRANSITION_NAME_PAGER);


            binding.glideFragmentBImage.setTransitionName(transitionName);
            Log.d("TestImageWWW", "name work-->" + transitionName);
//            tvSupersetName.setTransitionName(transitionNameTextview);
//            tvSupersetNameSecond.setTransitionName(transitionNameTextviewSecond);
            binding.pagerContainer.setTransitionName(transitionNamePager);
        }
        String thumbURL = ASSETS_PHOTOS + "superset_" + orderWorkout + WEBP;
        ImageLoader.getInstance().displayImage(thumbURL, binding.glideFragmentBImage, ImageUtils.getDefaultDisplayImageOptions(), new ImageLoadingListener() {
            @Override
            public void onLoadingStarted(String imageUri, View view) {
                ImageLoader.getInstance().displayImage(thumbURL, binding.glideFragmentBImage, ImageUtils.getDefaultDisplayImageOptions(), null);
                startPostponedEnterTransition();
            }

            @Override
            public void onLoadingFailed(String imageUri, View view, FailReason failReason) {
                startPostponedEnterTransition();
            }

            @Override
            public void onLoadingComplete(String imageUri, View view, Bitmap loadedImage) {
                //not used
            }

            @Override
            public void onLoadingCancelled(String imageUri, View view) {
                //not used
            }
        });
        String supersetName = Translate.getValue(nameWorkout);
        TextTwoRow.setText(binding.tvTitle, binding.tvTitleSecond, supersetName);
        binding.backContainer.setOnClickListener(v -> {
            setVibrate(v);
            if (getContext() != null)
                ((Activity) getContext()).onBackPressed();
        });

        adapterWidgetLevel = new AdapterWidgetLevel(listLevel, context, this, this);
        binding.scroolViewLevel.setOrientation(DSVOrientation.HORIZONTAL);
        binding.scroolViewLevel.addOnItemChangedListener(this);
        binding.scroolViewLevel.setAdapter(adapterWidgetLevel);
        PagerSnapHelper pagerSnapHelper = new PagerSnapHelper();
        pagerSnapHelper.attachToRecyclerView(binding.scroolViewLevel);

        binding.indicator.attachToRecyclerView(binding.scroolViewLevel, pagerSnapHelper);

        binding.scroolViewLevel.setItemTransitionTimeMillis(200);
        binding.scroolViewLevel.setItemTransformer(new ScaleTransformer.Builder()
                .setMinScale(0.9f)
                .build());
        binding.scroolViewLevel.setOffscreenItems(2);
        new MenuBottom((MainActivity) getActivity(), binding.containerMenu, true);
    }


    @Override
    public void onCurrentItemChanged(@Nullable RecyclerView.ViewHolder viewHolder, int i) {
        //not used
    }


    @Override
    public void init(Workout workout) {
        boolean oneTimeSeeWork = prefsUtilsWtContext.getBooleanPreferenceProfile(Constant.KEY_FINISHED_WORKOUT, false);
        Log.e("TestOpenWorkout", "isPremium==>" + Constant.premium + " oneTimeSeeWork==>" + oneTimeSeeWork);
        if (Constant.premium || !oneTimeSeeWork) openWorkout(workout);
        else openMainSubscribe();
    }

    private void openMainSubscribe() {
        RCUtils rcUtils = new RCUtils(getContext());
        rcUtils.getMainPaywallAndroidOffer(this);
    }

    private void openWorkout(Workout workout) {
        FragmentTransaction fragmentTransaction;
        sendAmplitudeLog(workout.getName());
        fragmentTransaction = getParentFragmentManager().beginTransaction();
        fragmentTransaction.addToBackStack(null);
        WorkoutsVideoFr workoutsVideoFr = new WorkoutsVideoFr();
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList("workout", new ArrayList<>(workout.getWorkoutExercises()));
        bundle.putString("idWorkout", workout.getId());
        bundle.putString("typeWorkout", Superset.class.getName());
        bundle.putString("nameWorkout", workout.getName());
        bundle.putString("level", extractLevel(workout.getName()));
        workoutsVideoFr.setArguments(bundle);
        fragmentTransaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
        fragmentTransaction.replace(R.id.root_fragment, workoutsVideoFr);
        fragmentTransaction.commitAllowingStateLoss(); //not ".commit" 'cause Handling IllegalStateException: Can not perform this action after onSaveInstanceState
    }

    private  String extractLevel(String name) {
        if (name == null) return "";
        int lastUnderscore = name.lastIndexOf('_');
        if (lastUnderscore != -1 && lastUnderscore < name.length() - 1) {
            String level = name.substring(lastUnderscore + 1).replaceAll("[^a-zA-Z]", "");
            return level.toLowerCase();
        }

        return "";
    }

    private void sendAmplitudeLog(String workout) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("withEventProperties", workout);
            sendAmplitude("[View] Workout opened values: ", jsonObject);
        } catch (JSONException ignored) {
            //ignored
        }

    }

    @Override
    public void init(Workout workout, String supersetName) {
        int countRun = prefsUtilsWtContext.getIntegerPreferenceProfile(Constant.KEY_COUNT_RUN, 0);
        Log.e("TestExerciseList", "isPremium==>" + Constant.premium + " countRun==>" + countRun);
        if (Constant.premium || countRun < 3) {
            openListExerciseWorkout(workout, supersetName);

            countRun++;
            prefsUtilsWtContext.setIntegerPreferenceProfile(Constant.KEY_COUNT_RUN, countRun);
        } else openMainSubscribe();
    }

    private void openListExerciseWorkout(Workout workout, String supersetName) {
        FragmentTransaction fragmentTransaction;
        if (workout != null) {
            fragmentTransaction = getParentFragmentManager().beginTransaction();
            fragmentTransaction.addToBackStack(null);
            WorkoutsExercisesFr workoutsExercisesFr = new WorkoutsExercisesFr();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("workout", new ArrayList<>(workout.getWorkoutExercises()));
            fragmentTransaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            workoutsExercisesFr.setArguments(bundle);
            fragmentTransaction.replace(R.id.root_fragment, workoutsExercisesFr);
            fragmentTransaction.commit();
        }
    }
}