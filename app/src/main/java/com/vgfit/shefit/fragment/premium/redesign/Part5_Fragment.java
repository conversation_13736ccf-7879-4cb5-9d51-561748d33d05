package com.vgfit.shefit.fragment.premium.redesign;

import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.bumptech.glide.Glide;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.Part5SubscribeBinding;
import com.vgfit.shefit.fragment.premium.redesign.model.MealOfferModel;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.text.TextTwoRow;

import java.util.ArrayList;
import java.util.Arrays;


public class Part5_Fragment extends Fragment {
    private Part5SubscribeBinding binding;

    public static Part5_Fragment newInstance() {

        Bundle args = new Bundle();

        Part5_Fragment fragment = new Part5_Fragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = Part5SubscribeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        binding.topTitle.setText(Translate.getValueLine("lose_weight_with_no_diet", Arrays.asList(3), false));
        binding.shortDescription.setText(Translate.getValue("150+_easy_and_yummy_recipes_based_on_your_food_preferences"));
        initMeal(getContext());
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
//        view.setOnKeyListener((v, keyCode, event) -> {
//            // pass on to be processed as normal
//            return keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN; // pretend we've processed it
//        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void initMeal(Context context) {
        ArrayList<MealOfferModel> listMeal = initListMeal();
        for (int i = 0; i < listMeal.size(); i++) {
            MealOfferModel mealOfferModel = listMeal.get(i);
            LayoutInflater inflater = LayoutInflater.from(context);
            View mealView = inflater.inflate(R.layout.item_cover_nutrition_offer, null);
            TextView titleNutrition = mealView.findViewById(R.id.titleNutrition);
            ImageView imageNutrition = mealView.findViewById(R.id.imageNutrition);
            TextView titleNutritionSecond = mealView.findViewById(R.id.titleNutritionSecond);

            TextView shortNutrition = mealView.findViewById(R.id.shortNutrition);
            TextView calInfo = mealView.findViewById(R.id.calInfo);
            TextView timeInfo = mealView.findViewById(R.id.timeInfo);

            TextTwoRow.setText(titleNutrition, titleNutritionSecond, mealOfferModel.dailyMealName);
            shortNutrition.setText(mealOfferModel.mealName);
            String kcal = mealOfferModel.calories + " kcal";
            calInfo.setText(kcal);
            String min = mealOfferModel.prepareTime + " min";
            timeInfo.setText(min);
            Glide.with(context).load(Uri.parse("file:///android_asset/imageMealOffer/" + mealOfferModel.image + ".webp")).centerCrop().into(imageNutrition);
            addViewToContainer(mealView, i);
        }
    }

    private void addViewToContainer(View view, int posMeal) {
        switch (posMeal) {
            case 0:
                binding.containerMeal1.addView(view);
                break;
            case 1:
                binding.containerMeal3.addView(view);
                break;
            case 2:
                binding.containerMeal2.addView(view);
                break;
            case 3:
                binding.containerMeal4.addView(view);
                break;
        }
    }

    private ArrayList<MealOfferModel> initListMeal() {
        ArrayList<MealOfferModel> listMeal = new ArrayList<>();
        listMeal.add(new MealOfferModel(Translate.getValue("breakfast"), Translate.getValue("papaya_smoothie"), 440, 5, "breakfast"));
        listMeal.add(new MealOfferModel(Translate.getValue("lunch"), Translate.getValue("healthy_shrimp_name"), 390, 25, "lunch"));
        listMeal.add(new MealOfferModel(Translate.getValue("morning_snack"), Translate.getValue("garlic_roasted_asparagus_with_parmesan"), 210, 15, "snack"));
        listMeal.add(new MealOfferModel(Translate.getValue("dinner"), Translate.getValue("tomato_soup_with_chicken_and_broccoli"), 430, 15, "dinner"));
        return listMeal;
    }

}
