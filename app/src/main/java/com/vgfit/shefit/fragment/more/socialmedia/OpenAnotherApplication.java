package com.vgfit.shefit.fragment.more.socialmedia;

import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.util.Log;

public class OpenAnotherApplication {

    Context context;

    public OpenAnotherApplication(Context context) {
        this.context = context;
    }

    public void openFacebookApp() {
        String facebookUrl = "www.facebook.com/vgfitcom";
        String facebookID = "378498615816626";

        try {
            int versionCode = context.getPackageManager().getPackageInfo("com.facebook.katana", 0).versionCode;

            if (!facebookID.isEmpty()) {
                // open the Facebook app using facebookID (fb://profile/facebookID or fb://page/facebookID)
                Uri uri = Uri.parse("fb://page/" + facebookID);
                context.startActivity(new Intent(Intent.ACTION_VIEW, uri));
                Log.e("Facebook", "open the Facebook app using facebookID (fb://profile/facebookID or fb://page/facebookID: " + uri);
            } else if (versionCode >= 3002850 && !facebookUrl.isEmpty()) {
                // open Facebook app using facebook url
                Uri uri = Uri.parse("fb://facewebmodal/f?href=" + facebookUrl);
                context.startActivity(new Intent(Intent.ACTION_VIEW, uri));
                Log.e("Facebook", "open Facebook app using facebook url: " + uri);
            } else {
                Log.e("Facebook", "Facebook is not installed. Open the browser.");
                Uri uri = Uri.parse(facebookUrl);
                context.startActivity(new Intent(Intent.ACTION_VIEW, uri));
            }
        } catch (PackageManager.NameNotFoundException e) {
            Log.e("Facebook", "Facebook is not installed. Open the browser. Error: " + e);
            Uri uri = Uri.parse(facebookUrl);
            context.startActivity(new Intent(Intent.ACTION_VIEW, uri));
        }
    }

    public void openInstagramApp() {
        Uri uri = Uri.parse("http://instagram.com/vgfitcom");
        Intent likeIng = new Intent(Intent.ACTION_VIEW, uri);

        likeIng.setPackage("com.instagram.android");

        try {
            context.startActivity(likeIng);
        } catch (ActivityNotFoundException e) {
            context.startActivity(new Intent(Intent.ACTION_VIEW,
                    Uri.parse("http://instagram.com/vgfitcom")));
        }
    }

    public void openTwitterApp() {
        Intent intent;
        try {
            // get the Twitter app if possible
            context.getPackageManager().getPackageInfo("com.twitter.android", 0);
            intent = new Intent(Intent.ACTION_VIEW, Uri.parse("twitter://user?user_id=806486911586070528"));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        } catch (Exception e) {
            // no Twitter app, revert to browser
            intent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://twitter.com/vgfitcom"));
        }
        context.startActivity(intent);
    }
}
