package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentSportsRedesignBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;

import java.util.Arrays;

public class SportLatelyRedesignFragment extends Fragment {
    private FragmentSportsRedesignBinding binding;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;
    private static final String CHOOSE_SPORT_1 = "chooseSport1";
    private static final String CHOOSE_SPORT_2 = "chooseSport2";
    private static final String CHOOSE_SPORT_3 = "chooseSport3";
    private static final String CHOOSE_SPORT_4 = "chooseSport4";

    public static SportLatelyRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        SportLatelyRedesignFragment fragment = new SportLatelyRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View] Sports lately View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentSportsRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();

        binding.rlContinue.setOnClickListener(view1 -> {
            isValidPush = false;
            binding.rlContinue.setSelected(true);
            AreasSelectorRedesignFragment fragmentC = AreasSelectorRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_").commitAllowingStateLoss();
            setVibrate(view1);
        });
        binding.chooseSport1.setOnClickListener(v -> {
            saveStatusSport(CHOOSE_SPORT_1, binding.chooseSport1);
            getStatusSport(CHOOSE_SPORT_1, binding.chooseSport1, binding.chooseSport1Txt);
            prefsUtilsWtContext.setBooleanPreference(CHOOSE_SPORT_2, false);
            prefsUtilsWtContext.setBooleanPreference(CHOOSE_SPORT_3, false);
            prefsUtilsWtContext.setBooleanPreference(CHOOSE_SPORT_4, false);
            getStatusSport(CHOOSE_SPORT_2, binding.chooseSport2, binding.chooseSport2Txt);
            getStatusSport(CHOOSE_SPORT_3, binding.chooseSport3, binding.chooseSport3Txt);
            getStatusSport(CHOOSE_SPORT_4, binding.chooseSport4, binding.chooseSport4Txt);
            setVibrate(v);
        });
        binding.chooseSport2.setOnClickListener(v -> {
            saveStatusSport(CHOOSE_SPORT_2, binding.chooseSport2);
            getStatusSport(CHOOSE_SPORT_2, binding.chooseSport2, binding.chooseSport2Txt);
            prefsUtilsWtContext.setBooleanPreference(CHOOSE_SPORT_1, false);
            prefsUtilsWtContext.setBooleanPreference(CHOOSE_SPORT_3, false);
            prefsUtilsWtContext.setBooleanPreference(CHOOSE_SPORT_4, false);
            getStatusSport(CHOOSE_SPORT_1, binding.chooseSport1, binding.chooseSport1Txt);
            getStatusSport(CHOOSE_SPORT_3, binding.chooseSport3, binding.chooseSport3Txt);
            getStatusSport(CHOOSE_SPORT_4, binding.chooseSport4, binding.chooseSport4Txt);
            setVibrate(v);
        });
        binding.chooseSport3.setOnClickListener(v -> {
            saveStatusSport(CHOOSE_SPORT_3, binding.chooseSport3);
            getStatusSport(CHOOSE_SPORT_3, binding.chooseSport3, binding.chooseSport3Txt);
            prefsUtilsWtContext.setBooleanPreference(CHOOSE_SPORT_1, false);
            prefsUtilsWtContext.setBooleanPreference(CHOOSE_SPORT_2, false);
            prefsUtilsWtContext.setBooleanPreference(CHOOSE_SPORT_4, false);
            getStatusSport(CHOOSE_SPORT_1, binding.chooseSport1, binding.chooseSport1Txt);
            getStatusSport(CHOOSE_SPORT_2, binding.chooseSport2, binding.chooseSport2Txt);
            getStatusSport(CHOOSE_SPORT_4, binding.chooseSport4, binding.chooseSport4Txt);
            setVibrate(v);
        });
        binding.chooseSport4.setOnClickListener(v -> {
            saveStatusSport(CHOOSE_SPORT_4, binding.chooseSport4);
            getStatusSport(CHOOSE_SPORT_4, binding.chooseSport4, binding.chooseSport4Txt);
            prefsUtilsWtContext.setBooleanPreference(CHOOSE_SPORT_1, false);
            prefsUtilsWtContext.setBooleanPreference(CHOOSE_SPORT_2, false);
            prefsUtilsWtContext.setBooleanPreference(CHOOSE_SPORT_3, false);
            getStatusSport(CHOOSE_SPORT_1, binding.chooseSport1, binding.chooseSport1Txt);
            getStatusSport(CHOOSE_SPORT_2, binding.chooseSport2, binding.chooseSport2Txt);
            getStatusSport(CHOOSE_SPORT_3, binding.chooseSport3, binding.chooseSport3Txt);
            setVibrate(v);
        });
        getStatusSport(CHOOSE_SPORT_1, binding.chooseSport1, binding.chooseSport1Txt);
        getStatusSport(CHOOSE_SPORT_2, binding.chooseSport2, binding.chooseSport2Txt);
        getStatusSport(CHOOSE_SPORT_3, binding.chooseSport3, binding.chooseSport3Txt);
        getStatusSport(CHOOSE_SPORT_4, binding.chooseSport4, binding.chooseSport4Txt);
        binding.backLayout.setOnClickListener(view12 -> toBack());
        binding.backTxt.getPaint().setUnderlineText(true);
        setParamsFragment();
    }

    private void saveStatusSport(String keyMeal, RelativeLayout layout) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyMeal, layout == binding.chooseSport2);
        prefsUtilsWtContext.setBooleanPreference(keyMeal, !isSelected);
    }

    private void getStatusSport(String keyMeal, RelativeLayout layout, TextView textView) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyMeal, layout == binding.chooseSport2);
        layout.setSelected(isSelected);
        textView.setSelected(isSelected);
    }

    public void setParamsFragment() {
        binding.textView16.setText(Translate.getValueLine("have_you_been_into_sports_lately", Arrays.asList(2, 4), false));
        binding.chooseSport1Txt.setText(Translate.getValue("been_hitting_the_gym"));
        binding.chooseSport2Txt.setText(Translate.getValue("not_much"));
        binding.chooseSport3Txt.setText(Translate.getValue("too_busy_at_work"));
        binding.chooseSport4Txt.setText(Translate.getValue("trying_to_get_back_into_it"));
        binding.btnContinue.setText(Translate.getValue("next"));
        binding.backTxt.setText(Translate.getValue("go_back"));
        binding.linearProgress.setProgress(8);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
                toBack();
                return true; // pretend we've processed it
            } else
                return false; // pass on to be processed as normal
        });
    }

    private void toBack() {
        isValidPush = false;
        if (getActivity() != null)
            getActivity().onBackPressed();
    }
}
