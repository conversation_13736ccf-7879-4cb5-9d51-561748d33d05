package com.vgfit.shefit.fragment.workouts.widjetworkout.adapter;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.assist.FailReason;
import com.nostra13.universalimageloader.core.listener.ImageLoadingListener;
import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.ItemCarouselBinding;
import com.vgfit.shefit.fragment.workouts.widjetworkout.callbacks.ItemWidgetWorkoutClicked;
import com.vgfit.shefit.realm.Superset;
import com.vgfit.shefit.realm.Workout;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.screen.ImageUtils;
import com.vgfit.shefit.util.text.TextTwoRow;

import java.util.ArrayList;
import java.util.HashMap;

public class AdapterWidgetCarousel extends RecyclerView.Adapter<AdapterWidgetCarousel.WorkoutViewHolder> {
    private ArrayList<Superset> listworkout;
    private ArrayList<String> listphoto;
    private Context context;
    private ItemWidgetWorkoutClicked itemWorkoutClicked;
    private int heightCell = 0;
    private int widthCell = 0;
    private static final String ASSETS_PHOTOS = "assets://imageWidget/";
    private static final String JPG = ".webp";
    private PrefsUtilsWtContext prefsUtilsWtContext;

    public AdapterWidgetCarousel(ArrayList<Superset> listworkout, Context context, ItemWidgetWorkoutClicked itemWorkoutClicked) {
        this.listworkout = listworkout;
        this.context = context;
        this.itemWorkoutClicked = itemWorkoutClicked;
        prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        for (Superset superset : listworkout) {
            Log.d("TestOrder", "order-->" + superset.getOrder());
        }
    }

    @NonNull
    @Override
    public WorkoutViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ItemCarouselBinding binding = ItemCarouselBinding.inflate(inflater, parent, false);
        return new WorkoutViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull WorkoutViewHolder holder, int position) {
        Superset itemCarousel = listworkout.get(position);
        HashMap<Integer, Boolean> mapLevel = viewLevels(itemCarousel);
        String supersetName = Translate.getValue(itemCarousel.getName());
        String supersetTitle = supersetName.replaceFirst(" ", "\n");
//        viewHolder.tvSupersetName.setText(supersetTitle);
        TextTwoRow.setText(holder.tvSupersetName, holder.tvSupersetNameSecond, supersetTitle);
        String thumbURL = ASSETS_PHOTOS + "superset_" + itemCarousel.getOrder() + JPG;

        ImageLoader.getInstance().displayImage(thumbURL, holder.imageView, ImageUtils.getDefaultDisplayImageOptions(), new ImageLoadingListener() {
            @Override
            public void onLoadingStarted(String imageUri, View view) {
                ImageLoader.getInstance().displayImage(thumbURL, holder.imageView, ImageUtils.getDefaultDisplayImageOptions(), null);
            }

            @Override
            public void onLoadingFailed(String imageUri, View view, FailReason failReason) {

            }

            @Override
            public void onLoadingComplete(String imageUri, View view, Bitmap loadedImage) {

            }

            @Override
            public void onLoadingCancelled(String imageUri, View view) {

            }
        });
        if (heightCell != 0 && widthCell != 0) {
            holder.cardView.getLayoutParams().height = heightCell;
            holder.cardView.getLayoutParams().width = widthCell;
            holder.cardView.requestLayout();
        }

//        Picasso.with(holder.itemView.getContext()).load(itemCarousel.getImage()).transform(new RoundedCornersTransform()).into(viewHolder.imageView);

        ViewCompat.setTransitionName(holder.imageView, itemCarousel.getImage());
        ViewCompat.setTransitionName(holder.tvSupersetName, itemCarousel.getName());
        ViewCompat.setTransitionName(holder.tvSupersetNameSecond, itemCarousel.getName() + "_second");
        ViewCompat.setTransitionName(holder.pagerContainer, itemCarousel.getId() + itemCarousel.getName());
        holder.itemView.setOnClickListener(v -> {
            setVibrate(v);
            itemWorkoutClicked.thisWorkoutClicked(itemCarousel, position, holder.imageView, holder.tvSupersetName, holder.tvSupersetNameSecond, holder.pagerContainer);
        });
        holder.btnContinue.setText(Translate.getValue("open_workout"));
        Log.e("TestImage", "Image==>" + itemCarousel.getImage());
//        if (!Utils.isDarkMode(prefsUtilsWtContext)) {
//            viewHolder.tvSupersetName.setBackgroundDrawable(context.getResources().getDrawable(R.drawable.bg_superset_name_white));
//        }
        try {
            holder.level1.setBackgroundDrawable(mapLevel.get(1) ? context.getDrawable(R.drawable.backgorund_level_progress_) : context.getDrawable(R.drawable.backgorund_level_progress));
            holder.level2.setBackgroundDrawable(mapLevel.get(2) ? context.getDrawable(R.drawable.backgorund_level_progress_) : context.getDrawable(R.drawable.backgorund_level_progress));
            holder.level3.setBackgroundDrawable(mapLevel.get(3) ? context.getDrawable(R.drawable.backgorund_level_progress_) : context.getDrawable(R.drawable.backgorund_level_progress));
            holder.level4.setBackgroundDrawable(mapLevel.get(4) ? context.getDrawable(R.drawable.backgorund_level_progress_) : context.getDrawable(R.drawable.backgorund_level_progress));
            holder.level5.setBackgroundDrawable(mapLevel.get(5) ? context.getDrawable(R.drawable.backgorund_level_progress_) : context.getDrawable(R.drawable.backgorund_level_progress));
            holder.level6.setBackgroundDrawable(mapLevel.get(6) ? context.getDrawable(R.drawable.backgorund_level_progress_) : context.getDrawable(R.drawable.backgorund_level_progress));
            holder.level7.setBackgroundDrawable(mapLevel.get(7) ? context.getDrawable(R.drawable.backgorund_level_progress_) : context.getDrawable(R.drawable.backgorund_level_progress));
            holder.level8.setBackgroundDrawable(mapLevel.get(8) ? context.getDrawable(R.drawable.backgorund_level_progress_) : context.getDrawable(R.drawable.backgorund_level_progress));
            holder.level9.setBackgroundDrawable(mapLevel.get(9) ? context.getDrawable(R.drawable.backgorund_level_progress_) : context.getDrawable(R.drawable.backgorund_level_progress));
        } catch (
                Exception e) {
        }

    }

    public HashMap<Integer, Boolean> viewLevels(Superset superset) {
        HashMap<Integer, Boolean> mapLevel = new HashMap<>();
        for (Workout workout : superset.getWorkouts()) {

            boolean isDone = prefsUtilsWtContext.getBooleanPreferenceProfile(workout.getId(), false);
            Log.d("TestSuperset", "workout    ID-->" + workout.getId());
            Log.d("TestSuperset", "workout order-->" + workout.getOrder());
            Log.d("TestSuperset", "isDone-->" + isDone);
            Log.d("TestSuperset", "===================================================================");
            mapLevel.put(workout.getOrder(), isDone);
        }
        return mapLevel;
    }

    @Override
    public int getItemCount() {
        return listworkout.size();
    }

    public void setHeightCell(int heightCell, int widthCell) {
        this.heightCell = heightCell;
        this.widthCell = widthCell;
    }

    public class WorkoutViewHolder extends RecyclerView.ViewHolder {
        CardView cardView;
        ImageView imageView;
        TextView tvSupersetName;
        TextView tvSupersetNameSecond;
        RelativeLayout pagerContainer;
        TextView btnContinue;
        View level1;
        View level2;
        View level3;
        View level4;
        View level5;
        View level6;
        View level7;
        View level8;
        View level9;


        public WorkoutViewHolder(@NonNull ItemCarouselBinding binding) {
            super(binding.getRoot());
            cardView = binding.cardView;
            imageView = binding.ivImage;
            tvSupersetName = binding.tvTitle;
            tvSupersetNameSecond = binding.tvTitleSecond;
            pagerContainer = binding.pagerContainer;
            btnContinue = binding.btnContinue;
            level1 = binding.level1;
            level2 = binding.level2;
            level3 = binding.level3;
            level4 = binding.level4;
            level5 = binding.level5;
            level6 = binding.level6;
            level7 = binding.level7;
            level8 = binding.level8;
            level9 = binding.level9;
        }
    }
}
