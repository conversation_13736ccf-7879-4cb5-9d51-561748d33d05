package com.vgfit.shefit.fragment.calendar;

import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.realm.WorkoutHistory;
import com.vgfit.shefit.repository.WorkoutHistoryRepository;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class CalendarPresenter {
    private final CalendarViewCallback calendarView;
    private final PrefsUtilsWtContext prefsUtilsWtContext;
    private final WorkoutHistoryRepository repository;
    private final Locale currentLocale;

    // Day constants - same as ProgressUserPresenter
    private static final String MONDAY = "mondayDay";
    private static final String TUESDAY = "tuesdayDay";
    private static final String WEDNESDAY = "wednesdayDay";
    private static final String THURSDAY = "thursdayDay";
    private static final String FRIDAY = "fridayDay";
    private static final String SATURDAY = "satDay";
    private static final String SUNDAY = "sunDay";

    private final Map<LocalDate, Boolean> completedWorkouts = new HashMap<>();
    private final Map<LocalDate, Boolean> missedWorkouts = new HashMap<>();
    private final Map<LocalDate, Integer> workoutProgress = new HashMap<>();
    private final Map<LocalDate, List<WorkoutHistory>> dailyWorkouts = new HashMap<>();

    public CalendarPresenter(CalendarViewCallback calendarView, PrefsUtilsWtContext prefsUtilsWtContext) {
        this.calendarView = calendarView;
        this.prefsUtilsWtContext = prefsUtilsWtContext;
        this.repository = new WorkoutHistoryRepository();
        this.currentLocale = Locale.getDefault();
    }

    public void loadCalendarData() {
        loadRealWorkoutData();
        calendarView.updateCalendarDataWithProgress(workoutProgress, missedWorkouts);
    }


    public void loadRealWorkoutData() {
        completedWorkouts.clear();
        missedWorkouts.clear();
        workoutProgress.clear();
        dailyWorkouts.clear();

        // Get all workout history
        List<WorkoutHistory> allWorkoutHistory = repository.getAllWorkoutHistory();

        // Process workout history to determine completed workouts and their progress
        processWorkoutHistory(allWorkoutHistory);

        // Calculate missed workouts based on selected workout days
        calculateMissedWorkouts();
    }

    public Map<LocalDate, Integer> getWorkoutProgress() {
        return workoutProgress;
    }

    public List<WorkoutHistory> getWorkoutsForDate(LocalDate date) {
        return dailyWorkouts.getOrDefault(date, new ArrayList<>());
    }

    private void processWorkoutHistory(List<WorkoutHistory> workoutHistoryList) {
        Map<LocalDate, Integer> dailyMaxDayPlanProgress = new HashMap<>();
        Map<LocalDate, Integer> dailyMaxSupersetProgress = new HashMap<>();

        // First pass: collect Day Plan and Superset workouts separately
        for (WorkoutHistory workout : workoutHistoryList) {
            if (workout.getCompletedDate() == null) continue;

            // Convert Date to LocalDate
            LocalDate workoutDate = workout.getCompletedDate()
                    .toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();


            // Check if workout was done on a selected day and has progress > 0
            if (workout.getProgressPercentage() > 0) {

                // Store workout in daily workouts map
                dailyWorkouts.computeIfAbsent(workoutDate, k -> new ArrayList<>()).add(workout);

                if (WorkoutHistory.TYPE_DAYPLAN.equals(workout.getWorkoutType())) {
                    // Process Day Plan workouts
                    Integer existingProgress = dailyMaxDayPlanProgress.get(workoutDate);
                    int maxProgress = (existingProgress != null) ? existingProgress : 0;

                    if (workout.getProgressPercentage() > maxProgress) {
                        dailyMaxDayPlanProgress.put(workoutDate, workout.getProgressPercentage());
                    }
                } else if (WorkoutHistory.TYPE_SUPERSET.equals(workout.getWorkoutType())) {
                    // Process Superset workouts
                    Integer existingProgress = dailyMaxSupersetProgress.get(workoutDate);
                    int maxProgress = (existingProgress != null) ? existingProgress : 0;

                    if (workout.getProgressPercentage() > maxProgress) {
                        dailyMaxSupersetProgress.put(workoutDate, workout.getProgressPercentage());
                    }
                }
            }
        }

        // Second pass: apply priority logic and store final progress
        // Add all Day Plan workouts (they have priority)
        Map<LocalDate, Integer> finalDailyProgress = new HashMap<>(dailyMaxDayPlanProgress);

        // Add Superset workouts only for days without Day Plan workouts
        for (Map.Entry<LocalDate, Integer> entry : dailyMaxSupersetProgress.entrySet()) {
            LocalDate date = entry.getKey();
            if (!finalDailyProgress.containsKey(date)) {
                finalDailyProgress.put(date, entry.getValue());
            }
        }

        // Store workout progress and mark completed workouts
        for (Map.Entry<LocalDate, Integer> entry : finalDailyProgress.entrySet()) {
            LocalDate date = entry.getKey();
            Integer progress = entry.getValue();

            // Store the actual progress percentage
            workoutProgress.put(date, progress);

            // Mark as completed if progress > 0
            completedWorkouts.put(date, true);
        }
    }

    private void calculateMissedWorkouts() {
        // Get earliest workout date to know when user started using the app
        Date earliestWorkoutDate = getEarliestWorkoutDate();
        if (earliestWorkoutDate == null) return;

        LocalDate earliestDate = earliestWorkoutDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        LocalDate currentDate = LocalDate.now();
        LocalDate checkDate = earliestDate;

        // Check each day from earliest workout date to today
        while (!checkDate.isAfter(currentDate)) {
            // Skip if workout was completed on this day
            if (completedWorkouts.containsKey(checkDate)) {
                checkDate = checkDate.plusDays(1);
                continue;
            }

            // Check if this day was selected for workouts and has passed
            if (isWorkoutDaySelected(checkDate) && checkDate.isBefore(currentDate)) {
                missedWorkouts.put(checkDate, true);
            }

            checkDate = checkDate.plusDays(1);
        }
    }

    private boolean isWorkoutDaySelected(LocalDate date) {
        // Convert LocalDate to Calendar to get day of week
        Calendar calendar = Calendar.getInstance(currentLocale);
        calendar.set(date.getYear(), date.getMonthValue() - 1, date.getDayOfMonth());
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);

        String dayKey = getDayKey(dayOfWeek);
        return existDayPlan(dayKey);
    }

    /**
     * Get day key using same mapping as ProgressUserPresenter for consistency
     */
    private String getDayKey(int dayOfWeek) {
        switch (dayOfWeek) {
            case Calendar.MONDAY:
                return MONDAY;
            case Calendar.TUESDAY:
                return TUESDAY;
            case Calendar.WEDNESDAY:
                return WEDNESDAY;
            case Calendar.THURSDAY:
                return THURSDAY;
            case Calendar.FRIDAY:
                return FRIDAY;
            case Calendar.SATURDAY:
                return SATURDAY;
            case Calendar.SUNDAY:
                return SUNDAY;
            default:
                return MONDAY;
        }
    }

    private boolean existDayPlan(String dayKey) {
        // Use the same logic as ProgressUserPresenter for consistency
        return prefsUtilsWtContext.getBooleanPreference(dayKey, false);
    }

    private Date getEarliestWorkoutDate() {
        List<WorkoutHistory> allHistory = repository.getAllWorkoutHistory();
        Date earliestDate = null;

        for (WorkoutHistory workout : allHistory) {
            if (workout.getCompletedDate() != null &&
                (earliestDate == null || workout.getCompletedDate().before(earliestDate))) {
                earliestDate = workout.getCompletedDate();
            }
        }

        return earliestDate;
    }
}