package com.vgfit.shefit.fragment.workouts.streaming;

import android.content.Context;

import java.io.File;
import java.io.IOException;

/**
 * Some utils methods.
 *
 * <AUTHOR> (<EMAIL>).
 */
public class Utils {

    public static File getVideoCacheDir(Context context) {
        // In this folder for save video cached from exoplayer
        File file = new File(context.getExternalCacheDir(), "video-cache");// NOSONAR
        if (file.mkdirs())
            return file;
        else return new File(String.valueOf(context.getExternalCacheDir()));// NOSONAR
    }

    public static void cleanVideoCacheDir(Context context) throws IOException {
        File videoCacheDir = getVideoCacheDir(context);
        cleanDirectory(videoCacheDir);
    }

    private static void cleanDirectory(File file) throws IOException {
        if (!file.exists()) {
            return;
        }
        File[] contentFiles = file.listFiles();
        if (contentFiles != null) {
            for (File contentFile : contentFiles) {
                delete(contentFile);
            }
        }
    }

    private static void delete(File file) throws IOException {
        if (file.isFile() && file.exists()) {
            deleteOrThrow(file);
        } else {
            cleanDirectory(file);
            deleteOrThrow(file);
        }
    }

    private static void deleteOrThrow(File file) throws IOException {
        if (file.exists()) {
            boolean isDeleted = file.delete();
            if (!isDeleted) {
                throw new IOException(String.format("File %s can't be deleted", file.getAbsolutePath()));
            }
        }
    }
}