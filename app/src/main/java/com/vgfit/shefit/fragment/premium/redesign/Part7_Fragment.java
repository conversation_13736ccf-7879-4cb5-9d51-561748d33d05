package com.vgfit.shefit.fragment.premium.redesign;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.app.Activity;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.R;
import com.vgfit.shefit.billing.BillingProcessor;
import com.vgfit.shefit.databinding.Part7SubscribeBinding;
import com.vgfit.shefit.fragment.premium.BillingAndRemoteConfig;
import com.vgfit.shefit.fragment.premium.redesign.model.UpdateExistTrial;
import com.vgfit.shefit.fragment.premium.redesign.model.UpdatePart1;
import com.vgfit.shefit.fragment.premium.redesign.model.UpdatePart7;
import com.vgfit.shefit.fragment.premium.redesign.model.UpdatePart8;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.RCUtils;
import com.vgfit.shefit.util.Translate;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class Part7_Fragment extends Fragment {
    private Part7SubscribeBinding binding;
    private boolean isChecked = false;
    private BillingProcessor bp;
    private boolean readyToPurchase;
    private int typeSubscribe = 0;// month and yearly =0 (default)
    private String monthTrial = "";
    private String yearTrial = "";
    private boolean existTrialMonth = false;
    private boolean existTrialYear = false;
    private final boolean existTrial = false;

    public static Part7_Fragment newInstance() {

        Bundle args = new Bundle();
        Part7_Fragment fragment = new Part7_Fragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initActivity();
        RCUtils rcUtils = new RCUtils(getContext());
        typeSubscribe = rcUtils.getScrollingPaywall();
    }

    private void initActivity() {
        Activity activity = (Activity) getContext();
        if (activity instanceof BillingAndRemoteConfig) {
            if (getActivity() != null && ((BillingAndRemoteConfig) getActivity()).bp != null) {
                bp = ((BillingAndRemoteConfig) getActivity()).bp;
                readyToPurchase = BillingAndRemoteConfig.readyToPurchase;
            }
        } else {
            if (getActivity() != null && ((MainActivity) getActivity()).bp != null) {
                bp = ((MainActivity) getActivity()).bp;
                readyToPurchase = MainActivity.readyToPurchase;
            }
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = Part7SubscribeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        binding.changeLife.setSelected(true);
        Animation rotation = AnimationUtils.loadAnimation(getContext(), R.anim.rotate);
        rotation.setFillAfter(true);
        binding.iconRotation1.startAnimation(rotation);
        binding.iconRotation2.startAnimation(rotation);
        binding.iconRotation3.startAnimation(rotation);
        String changeLifeTxt = Translate.getValue("change_your_life");
        binding.changeLife.setText(changeLifeTxt);
        binding.aboutInfo.setText(Translate.getValue("about_free_trial"));
        binding.cancelAny.setText(Translate.getValue("cancel_anytime_by_one_click_during_the_trial"));
        binding.accessAll.setText(Translate.getValue("access_to_all_types_of_trainings_and_meal_plans"));
        binding.zeroHiden.setText(Translate.getValue("zero_hidden_charges_during_the_trial"));
        binding.startSubscribe.setText(Translate.getValue("start_free_trial"));
        binding.startSubscribe.setOnClickListener(v -> {
            setVibrate(v);
            if (readyToPurchase && getActivity() != null && bp != null) {
                if (isChecked)
                    bp.subscribe(getActivity(), Constant.getFirstItemPurchase(typeSubscribe));
                else
                    bp.subscribe(getActivity(), Constant.getSecondItemPurchase(typeSubscribe));
            }
        });
        binding.containerAbout.setVisibility(existTrial ? View.VISIBLE : View.GONE);
        setPrice();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
//        view.setOnKeyListener((v, keyCode, event) -> {
//            // pass on to be processed as normal
//            return keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN; // pretend we've processed it
//        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        try {
            EventBus.getDefault().unregister(this);
        } catch (Exception ignored) {
        }

    }

    @Override
    public void onStart() {
        super.onStart();
        try {
            EventBus.getDefault().register(this);
        } catch (Exception ignored) {
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageSecondOffer(UpdatePart1 event) {
        Log.d("TestExist", "event accessed UpdatePart1");
        if (event != null) {
            isChecked = event.isChecked();
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageSecondOffer(UpdatePart8 event) {
        Log.d("TestExist", "event accessed UpdatePart8");
        if (event != null) {
            isChecked = event.isChecked();
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onExistTrial(UpdatePart7 event) {
        Log.d("TestExist", "event accessed trial");
        if (event != null) {
            Log.d("TestExist", "existTrial--->" + event.isExistTrial());
            visibleTrial(event.isExistTrial);
        }
    }

    private void visibleTrial(boolean existTrial) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> binding.rlStart.setAlpha(existTrial ? 1.0f : 0.6f));
            getActivity().runOnUiThread(() -> {
                binding.containerAbout.setVisibility(existTrial ? View.VISIBLE : View.GONE);
            });
        }
    }

    private void setPrice() {
        try {
            if (getActivity() != null) {
                if (bp != null) {
                    bp.getSubscriptionListingDetails(Constant.getFirstItemPurchase(typeSubscribe), purchaseInfoMonth -> {
                        try {

                            monthTrial = getFormattedTrial(purchaseInfoMonth.subscriptionFreeTrialPeriod);
                            if (monthTrial.length() > 0) {
                                existTrialMonth = true;

                                if (monthTrial.contains("7")) {
                                    monthTrial = "1 " + Translate.getValue("week");
                                } else
                                    monthTrial = monthTrial + " " + Translate.getValue("days");
                            }
                            if (isChecked) {
                                visibleTrial(existTrialMonth);
                            }
                        } catch (Exception ignored) {
                        }
                    });
                    bp.getSubscriptionListingDetails(Constant.getSecondItemPurchase(typeSubscribe), purchaseInfoYear -> {
                        try {

                            yearTrial = getFormattedTrial(purchaseInfoYear.subscriptionFreeTrialPeriod);
                            if (yearTrial.length() > 0) {
                                existTrialYear = true;
                                if (yearTrial.contains("7")) {
                                    yearTrial = "1 " + Translate.getValue("week");
                                } else
                                    yearTrial = yearTrial + " " + Translate.getValue("days");
                            }
                            if (!isChecked) {
                                visibleTrial(existTrialYear);
                            }
                        } catch (Exception ignored) {
                        }
                    });
                }
            }
        } catch (
                Exception ignored) {
        }

    }

    private String getFormattedTrial(String trialDefault) {
        String numb = extractOnlyNumber(trialDefault);
        int numbInt = -1;
        try {
            numbInt = Integer.parseInt(numb);
        } catch (Exception ignored) {
        }
        if (numbInt != -1 && trialDefault.contains("W")) {
            return String.valueOf(numbInt * 7);
        } else if (numbInt != -1 && trialDefault.contains("D"))
            return String.valueOf(numbInt);
        else return numb;
    }

    private String extractOnlyNumber(String text) {
        Pattern p = Pattern.compile("\\d+");
        Matcher m = p.matcher(text);
        if (m.find()) {
            return m.group();
        }
        return "";
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageTrial(UpdateExistTrial event) {
        Log.d("Teeeeeeee", "event null");
        if (event != null) {
            Log.d("Teeeeeeee", "existTrial--->" + event.isExistTrial());
            binding.containerAbout.setVisibility(event.isExistTrial() ? View.VISIBLE : View.GONE);
        }

    }


}
