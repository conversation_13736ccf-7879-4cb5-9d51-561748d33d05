package com.vgfit.shefit.fragment.nutrition;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;
import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.transition.TransitionInflater;

import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.MenuBottom;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.FragmentNutritionScrollBinding;
import com.vgfit.shefit.fragment.nutrition.adapter.AdapterNutritionOneDayR;
import com.vgfit.shefit.fragment.nutrition.adapter.AdapterScrollHorizontal;
import com.vgfit.shefit.fragment.nutrition.callbacks.NutrItemClick;
import com.vgfit.shefit.fragment.nutrition.callbacks.NutritionItemAccessed;
import com.vgfit.shefit.fragment.nutrition.model.ItemNutrition;
import com.vgfit.shefit.realm.Meal;
import com.vgfit.shefit.realm.MealByDay;
import com.vgfit.shefit.realm.NutritionDay;
import com.vgfit.shefit.realm.NutritionPlan;
import com.vgfit.shefit.util.AnimatorView;
import com.vgfit.shefit.util.CenterLayoutManager;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.RCUtils;
import com.yarolegovich.discretescrollview.DSVOrientation;
import com.yarolegovich.discretescrollview.DiscreteScrollView;
import com.yarolegovich.discretescrollview.transform.ScaleTransformer;

import java.util.ArrayList;
import java.util.Collections;

import io.realm.Realm;
import io.realm.RealmResults;
import me.everything.android.ui.overscroll.OverScrollDecoratorHelper;

public class NutritionDayScrollFr extends Fragment implements NutritionItemAccessed, DiscreteScrollView.OnItemChangedListener, NutrItemClick {
    private FragmentNutritionScrollBinding binding;
    View view;
    ArrayList<ArrayList<ItemNutrition>> listNutrition;
    private ArrayList<MealByDay> listIdDay;
    private Context context;
    private String mealDayName = "";
    private int positionSelected = 0;
    private AdapterNutritionOneDayR adapterNutritionOneDay;

    public static NutritionDayScrollFr newInstance(int currentDay, String mealDay) {

        Bundle args = new Bundle();
        NutritionDayScrollFr fragment = new NutritionDayScrollFr();
        args.putInt("dayKeyMeal", currentDay);
        args.putString("nameKeyMeal", mealDay);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arg = getArguments();
        context = getContext();
        listNutrition = new ArrayList<>();
        ArrayList<NutritionDay> listNutrionDay = new ArrayList<>();
        listIdDay = new ArrayList<>();
        Realm realm = Realm.getDefaultInstance();
        RealmResults<NutritionPlan> listNutritionPlanRealm = realm.where(NutritionPlan.class).findAll();
        if (listNutritionPlanRealm.size() > 0 && listNutritionPlanRealm.get(0) != null) {
            listNutrionDay.addAll(listNutritionPlanRealm.get(0).getDays());
        }
        Collections.sort(listNutrionDay, (o1, o2) -> o1.getOrder() - o2.getOrder());

        if (arg != null) {
            int posDay = arg.getInt("dayKeyMeal");
            listIdDay.addAll(listNutrionDay.get(posDay).getMeals());
            mealDayName = arg.getString("nameKeyMeal");
            Collections.sort(listIdDay, (o1, o2) -> o1.getOrder() - o2.getOrder());
        }
        mealDayName = mealDayName.replace("\n", " ");
        AnimatorView animatorView = new AnimatorView();
        animatorView.initScaleUpAlpha();
        animatorView.initScaleDownAlpha();
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup parent, Bundle savedInstanceState) {
        binding = FragmentNutritionScrollBinding.inflate(inflater, parent, false);
        view = binding.getRoot();
        setStatusBar(view);
        binding.backContainer.setOnClickListener(v ->
        {
            setVibrate(v);
            if (getActivity() != null) getActivity().onBackPressed();
        });
        adapterNutritionOneDay = new AdapterNutritionOneDayR(listIdDay, this);
        binding.rvNutritionVertical.setAdapter(adapterNutritionOneDay);
        CenterLayoutManager centerLayoutManager = new CenterLayoutManager(context, LinearLayoutManager.HORIZONTAL, false);
        binding.rvNutritionVertical.setLayoutManager(centerLayoutManager);
        adapterNutritionOneDay.selectItemNutrition(positionSelected);
        AdapterScrollHorizontal adapterScrollHorizontal = new AdapterScrollHorizontal(listIdDay, this);
        binding.listHorizontal.setOrientation(DSVOrientation.HORIZONTAL);
        binding.listHorizontal.addOnItemChangedListener(this);
        binding.listHorizontal.setAdapter(adapterScrollHorizontal);
        binding.listHorizontal.setItemTransitionTimeMillis(100);
        binding.listHorizontal.setItemTransformer(new ScaleTransformer.Builder()
                .setMinScale(0.92f)
                .build());
        binding.listHorizontal.setOffscreenItems(2);

        if (getActivity() != null)
            ((MainActivity) getActivity()).goToPrevious();
        OverScrollDecoratorHelper.setUpOverScroll(binding.rvNutritionVertical, OverScrollDecoratorHelper.ORIENTATION_VERTICAL);
        binding.tvSupersetName.setText(mealDayName);

        new MenuBottom((MainActivity) getActivity(), binding.containerMenu);
        return view;
    }

    private void openMainSubscribe() {
        RCUtils rcUtils = new RCUtils(getContext());
        rcUtils.getMainPaywallAndroidOffer(this);
    }

    private void openMeal(Meal itemNutrition, ImageView imageView, TextView shortDesc, TextView calories, TextView time, LinearLayout containerInfoMeal, View view2) {
        Fragment fragment = NutritionRecipeFr.newInstance(itemNutrition, ViewCompat.getTransitionName(imageView), ViewCompat.getTransitionName(shortDesc), ViewCompat.getTransitionName(calories), ViewCompat.getTransitionName(time), ViewCompat.getTransitionName(containerInfoMeal), ViewCompat.getTransitionName(view2));

        setSharedElementReturnTransition(TransitionInflater.from(
                getActivity()).inflateTransition(R.transition.simple_fragment_transition));
        setExitTransition(TransitionInflater.from(
                getActivity()).inflateTransition(android.R.transition.fade));

        fragment.setSharedElementEnterTransition(TransitionInflater.from(
                getActivity()).inflateTransition(R.transition.simple_fragment_transition));
        fragment.setEnterTransition(TransitionInflater.from(
                getActivity()).inflateTransition(android.R.transition.fade));

        FragmentTransaction fragmentTransaction = getParentFragmentManager().beginTransaction();
        fragmentTransaction.addSharedElement(imageView, ViewCompat.getTransitionName(imageView));
        fragmentTransaction.addSharedElement(shortDesc, ViewCompat.getTransitionName(shortDesc));
        fragmentTransaction.addSharedElement(calories, ViewCompat.getTransitionName(calories));
        fragmentTransaction.addSharedElement(time, ViewCompat.getTransitionName(time));
        fragmentTransaction.addSharedElement(containerInfoMeal, ViewCompat.getTransitionName(containerInfoMeal));
        fragmentTransaction.addSharedElement(view2, ViewCompat.getTransitionName(view2));
        fragmentTransaction.addToBackStack(null);
        fragmentTransaction.replace(R.id.root_fragment, fragment);
        fragmentTransaction.commit();
        if (getActivity() != null)
            ((MainActivity) getActivity()).goToPrevious();
    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    public void onResume() {
        super.onResume();

    }

    @Override
    public void onItemClick(Meal itemNutrition, String nameMeal, int position) {
        try {
            binding.listHorizontal.smoothScrollToPosition(position);
            positionSelected = position;
        } catch (Exception ignored) {
        }
    }

    @Override
    public void onCurrentItemChanged(@Nullable RecyclerView.ViewHolder viewHolder, int adapterPosition) {
        positionSelected = adapterPosition;
        binding.rvNutritionVertical.smoothScrollToPosition(adapterPosition);
        adapterNutritionOneDay.selectItemNutrition(adapterPosition);
    }

    @Override
    public void onItemClick(Meal meal) {

    }

    @Override
    public void onItemClick(Meal meal, ImageView imageView, TextView shortDesc, TextView calories, TextView time, LinearLayout containerInfoMeal, View view2) {
        setVibrate(imageView);
        if (meal != null)
            if (!Constant.premium)
                openMainSubscribe();
            else
                openMeal(meal, imageView, shortDesc, calories, time, containerInfoMeal, view2);
    }
}