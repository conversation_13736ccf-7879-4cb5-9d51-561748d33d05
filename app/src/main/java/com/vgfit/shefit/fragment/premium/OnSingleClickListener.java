package com.vgfit.shefit.fragment.premium;

import android.os.SystemClock;
import android.view.View;

/**
 * Created by Nemo on 4/27/17.
 */

public abstract class OnSingleClickListener implements View.OnClickListener {

    private static final long MIN_CLICK_INTERVAL = 600;

    private long mLastClickTime;

    public abstract void onSingleClick(View v);

    @Override
    public final void onClick(View v) {
        long currentClickTime = SystemClock.uptimeMillis();
        long elapsedTime = currentClickTime - mLastClickTime;

        if (elapsedTime <= MIN_CLICK_INTERVAL) {
            return;
        }

        mLastClickTime = currentClickTime;

        onSingleClick(v);
    }

}