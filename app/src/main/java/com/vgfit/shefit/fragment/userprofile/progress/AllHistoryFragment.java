package com.vgfit.shefit.fragment.userprofile.progress;

import static android.os.Build.VERSION_CODES.R;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;
import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.vgfit.shefit.databinding.FragmentAllHistoryBinding;
import com.vgfit.shefit.fragment.userprofile.progress.adapter.WorkoutHistoryAdapter;
import com.vgfit.shefit.realm.WorkoutHistory;
import com.vgfit.shefit.repository.WorkoutHistoryRepository;
import com.vgfit.shefit.util.Translate;

import java.util.ArrayList;
import java.util.List;

public class AllHistoryFragment extends Fragment {

    private FragmentAllHistoryBinding binding;
    private WorkoutHistoryAdapter adapter;
    private List<WorkoutHistory> workoutHistoryList;
    private WorkoutHistoryRepository repository;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        workoutHistoryList = new ArrayList<>();
        repository = new WorkoutHistoryRepository();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentAllHistoryBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        binding.titleText.setText(Translate.getValue("my_workouts"));
        setStatusBar(binding.headerLayout);
        setupViews();
        setupRecyclerView();
        loadWorkoutHistory();
        translateView();
    }

    private void translateView(){
        binding.noWorkoutTxt.setText(Translate.getValue("no_completed_workouts"));
        binding.startWorkoutTxt.setText(Translate.getValue("start_a_workout_to_see_your_history"));
    }

    private void setupViews() {
        // Setup back button
        binding.backButton.setOnClickListener(v -> {
            setVibrate(v);
            if (getActivity() != null) {
                getActivity().onBackPressed();
            }
        });
    }

    private void setupRecyclerView() {
        adapter = new WorkoutHistoryAdapter(getContext(), workoutHistoryList);
        binding.recyclerViewHistory.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerViewHistory.setAdapter(adapter);
    }

    private void loadWorkoutHistory() {
        showLoading(true);

        // Load all workout history from repository
        workoutHistoryList.clear();
        workoutHistoryList.addAll(repository.getAllWorkoutHistory());

        // Update UI
        updateUI();
        showLoading(false);
    }

    private void updateUI() {
        if (workoutHistoryList.isEmpty()) {
            // Show empty state
            binding.recyclerViewHistory.setVisibility(View.GONE);
            binding.emptyStateLayout.setVisibility(View.VISIBLE);
        } else {
            // Show workout history list
            binding.recyclerViewHistory.setVisibility(View.VISIBLE);
            binding.emptyStateLayout.setVisibility(View.GONE);
            adapter.updateData(workoutHistoryList);
        }
    }

    private void showLoading(boolean show) {
        if (binding != null) {
            binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
            if (show) {
                binding.recyclerViewHistory.setVisibility(View.GONE);
                binding.emptyStateLayout.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }
}