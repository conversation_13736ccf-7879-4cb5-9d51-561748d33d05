package com.vgfit.shefit.fragment.network;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.vgfit.shefit.StartScreen;
import com.vgfit.shefit.databinding.NetworkFailedLayoutBinding;


public class NetworkAlertFragment extends Fragment {
    private NetworkFailedLayoutBinding binding;
    private View view;

    public static NetworkAlertFragment newInstance() {
        Bundle args = new Bundle();
        NetworkAlertFragment fragment = new NetworkAlertFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = NetworkFailedLayoutBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();
        binding.btnRetry.setOnClickListener(view1 -> {
            if (isNetworkAvailable()) {
                setVibrate(view1);
                if (getActivity() != null)
                    ((StartScreen) getActivity()).closeNetworkProblem();
            } else
                Toast.makeText(getContext(), "No connection", Toast.LENGTH_LONG).show();
        });
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
        view.setOnKeyListener((v, keyCode, event) -> {
            // pass on to be processed as normal
            return keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN; // pretend we've processed it
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private boolean isNetworkAvailable() {
        if (getActivity() != null) {
            ConnectivityManager connectivityManager = (ConnectivityManager) getActivity().getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        } else return false;
    }
}
