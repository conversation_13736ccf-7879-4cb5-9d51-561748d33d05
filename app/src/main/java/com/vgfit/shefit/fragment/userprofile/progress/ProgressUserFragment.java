package com.vgfit.shefit.fragment.userprofile.progress;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;
import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.content.res.ColorStateList;
import android.os.Bundle;
import android.os.Handler;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentProgressUserBinding;
import com.vgfit.shefit.fragment.userprofile.progress.adapter.AchievementAdapter;
import com.vgfit.shefit.fragment.userprofile.progress.adapter.WorkoutHistoryAdapter;
import com.vgfit.shefit.fragment.userprofile.progress.presenter.ProgressUserPresenter;
import com.vgfit.shefit.fragment.userprofile.progress.view.ProgressUserView;
import com.vgfit.shefit.realm.Achievement;
import com.vgfit.shefit.realm.WorkoutHistory;
import com.vgfit.shefit.util.Translate;

import java.util.ArrayList;
import java.util.List;

public class ProgressUserFragment extends Fragment implements ProgressUserView {

    private FragmentProgressUserBinding binding;
    private ProgressUserPresenter presenter;
    private WorkoutHistoryAdapter adapter;
    private AchievementAdapter achievementAdapter;


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        PrefsUtilsWtContext prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        presenter = new ProgressUserPresenter(this, prefsUtilsWtContext);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentProgressUserBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setStatusBar(binding.main);
        translateViews();
        setupViews();
        setupListeners();

        // Initial data load
        presenter.loadWorkoutHistory();
    }

    private void translateViews() {
        binding.durationUnit.setText(Translate.getValue("min"));
        binding.weekButton.setText(Translate.getValue("week"));
        binding.monthButton.setText(Translate.getValue("month"));
        binding.yearButton.setText(Translate.getValue("year"));
        binding.caloriesLabel.setText(Translate.getValue("calories"));
        binding.durationLabel.setText(Translate.getValue("duration"));
        binding.completedWorkoutsLabel.setText(Translate.getValue("completed_workouts"));
        binding.compeletedText.setText(Translate.getValue("training_completed"));
        binding.myWorkoutText.setText(Translate.getValue("my_workouts"));
        binding.titleText.setText(Translate.getValue("my_progress"));
    }

    private void setupViews() {
        // Set up Workout History RecyclerView
        binding.workoutHistoryRecycler.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.workoutHistoryRecycler.setNestedScrollingEnabled(false);
        adapter = new WorkoutHistoryAdapter(getContext(), new ArrayList<>());
        binding.workoutHistoryRecycler.setAdapter(adapter);

        // Set up Achievement RecyclerView (horizontal)
        LinearLayoutManager horizontalLayoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
        binding.achievementRecycler.setLayoutManager(horizontalLayoutManager);
        binding.achievementRecycler.setNestedScrollingEnabled(false);
        achievementAdapter = new AchievementAdapter(getContext(), new ArrayList<>(), this);
        binding.achievementRecycler.setAdapter(achievementAdapter);

        // Set initial view mode
        updateViewMode(0);
    }

    private void setupListeners() {
        binding.weekButton.setOnClickListener(v -> updateViewMode(0));

        binding.monthButton.setOnClickListener(v -> updateViewMode(1));

        binding.yearButton.setOnClickListener(v -> updateViewMode(2));

        binding.backButton.setOnClickListener(v -> {
            setVibrate(v);
            if (getActivity() != null) {
                getActivity().onBackPressed();
            }
        });
        binding.allWorkoutsHistory.setOnClickListener(v -> {
            setVibrate(v);
            AllHistoryFragment allHistoryFragment = new AllHistoryFragment();
            FragmentTransaction fragmentTransaction = getParentFragmentManager().beginTransaction();
            fragmentTransaction.addToBackStack(null);
            fragmentTransaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            fragmentTransaction.replace(R.id.root_fragment, allHistoryFragment);
            fragmentTransaction.commit();
        });
        binding.progressCircular.setStartPositionInDegrees(-90);
        binding.progressCircular.setRoundEdgeProgress(true);
        binding.progressCircular.setProgressColor(getResources().getColor(R.color.green_color_day));
    }

    private void updateViewMode(int currentViewMode) {
        setVibrate(binding.weekButton);
        presenter.loadWorkoutHistoryByPeriod(currentViewMode);
        // Reset all buttons
        binding.weekButton.setSelected(false);
        binding.monthButton.setSelected(false);
        binding.yearButton.setSelected(false);

        // Set selected button
        switch (currentViewMode) {
            case 0:
                binding.weekButton.setSelected(true);
                break;
            case 1:
                binding.monthButton.setSelected(true);
                break;
            case 2:
                binding.yearButton.setSelected(true);
                break;
            default:
                break;
        }
    }

    @Override
    public void displayWorkoutHistory(List<WorkoutHistory> workoutHistoryList) {
        Handler handler = new Handler();
        handler.post(() -> adapter.updateData(workoutHistoryList));
    }

    @Override
    public void displayAchievements(List<Achievement> achievementList) {
        achievementAdapter.updateData(achievementList);
    }

    @Override
    public void displayStatistics(int completedWorkouts, int totalWorkouts, int totalDuration, int totalCalories) {
        String completedWorkoutsText = completedWorkouts + "/" + totalWorkouts + " " + Translate.getValue("completed");
        binding.completedWorkoutsText.setText(completedWorkoutsText);

        int percentCircular = totalWorkouts > 0 ? (completedWorkouts * 1000) / totalWorkouts : 0;
        int percent = totalWorkouts > 0 ? (completedWorkouts * 100) / totalWorkouts : 0;
        String percentText = percent + "%";
        binding.completedWorkoutsPercent.setText(percentText);
        binding.progressCircular.setProgress(percentCircular);

        // Convert totalDuration from seconds to minutes
        int durationInMinutes = totalDuration / 60;
        binding.durationText.setText(String.valueOf(durationInMinutes));

        binding.caloriesText.setText(String.valueOf(totalCalories));
        String kcal = " " + Translate.getValue("kcal").toLowerCase();
        binding.caloriesUnit.setText(kcal);
    }

    @Override
    public void displayDateRange(String year, String dateRange, String completedPercent) {
        binding.yearRangeText.setText(year);
        binding.dateRangeText.setText(dateRange);
        binding.completedPercentText.setText(completedPercent);
    }

    @Override
    public void displayProgressChart(List<Integer> progressValues, List<String> labels, int periodType, int selectedIndex, List<Integer> missingList) {
        binding.progressChartLayout.removeAllViews();

        for (int i = 0; i < progressValues.size(); i++) {
            View chartItem = getLayoutInflater().inflate(R.layout.item_progress_chart, binding.progressChartLayout, false);
            TextView label = chartItem.findViewById(R.id.label_text);

            // Find the indicator views
            View line = chartItem.findViewById(R.id.lineIndicator);
            View triangle = chartItem.findViewById(R.id.triangleIndicator);

            // Show/hide indicators based on whether this is the selected item
            boolean isSelected = (i == selectedIndex);
            int progress = progressValues.get(i);
            boolean missing = missingList.get(i) == 1 && progress == 0;
            if (line != null) {
                line.setVisibility(isSelected ? View.VISIBLE : View.INVISIBLE);
            }
            if (triangle != null) {
                triangle.setVisibility(isSelected ? View.VISIBLE : View.INVISIBLE);
            }

            ProgressBar verticalProgressBar = chartItem.findViewById(R.id.verticalProgressBar);
            if (isSelected && progress == 0) {
                verticalProgressBar.setProgressTintList(ColorStateList.valueOf(
                        ContextCompat.getColor(requireContext(), R.color.current_day_pink)));
            }

            verticalProgressBar.setProgress(progress);

            // Set appropriate drawable based on progress value
            if (progress <= 0 && !missing) {
                // For zero progress, use the gray drawable
                verticalProgressBar.setProgressDrawable(ContextCompat.getDrawable(
                        requireContext(), R.drawable.vertical_progress_bar_zero));
            } else {
                // For non-zero progress, use the default drawable
                verticalProgressBar.setProgressDrawable(ContextCompat.getDrawable(
                        requireContext(), R.drawable.vertical_progress_bar));

                // For high progress, keep the default green color
                verticalProgressBar.setProgressTintList(ColorStateList.valueOf(
                        ContextCompat.getColor(requireContext(), R.color.green_color_day)));

            }
            if (missing) {
                verticalProgressBar.setProgressTintList(ColorStateList.valueOf(
                        ContextCompat.getColor(requireContext(), R.color.red_color_day)));
            }
            if (isSelected) {
                label.setTextColor(ContextCompat.getColor(requireContext(), R.color.black));
            } else {
                label.setTextColor(ContextCompat.getColor(requireContext(), R.color.label_color));
            }

            ViewGroup.LayoutParams params = verticalProgressBar.getLayoutParams();
            if (periodType == 0) {
                params.width = dpToPx(14);
                label.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
                if (progress == 0)
                    verticalProgressBar.setProgress(18);
            } else if (periodType == 1) {
                params.width = dpToPx(8);
                label.setTextSize(TypedValue.COMPLEX_UNIT_SP, 6);
                if (progress == 0)
                    verticalProgressBar.setProgress(10);
            } else if (periodType == 2) {
                params.width = dpToPx(14);
                label.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
                if (progress == 0)
                    verticalProgressBar.setProgress(17);
            }
            verticalProgressBar.setLayoutParams(params);

            if (i < labels.size()) {
                label.setText(labels.get(i));
            }

            binding.progressChartLayout.addView(chartItem);
        }
    }

    private int dpToPx(int dp) {
        float density = getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }
}