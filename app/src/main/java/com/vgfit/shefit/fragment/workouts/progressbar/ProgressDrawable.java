package com.vgfit.shefit.fragment.workouts.progressbar;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;

public class ProgressDrawable extends Drawable {
    private final int mForeground;
    private final int mBackground;
    private final Paint mPaint = new Paint();
    private final RectF mSegment = new RectF();
    Paint strokePaint = new Paint();
    private int anotherColor;
    private int numberOfSegments;

    public ProgressDrawable(int fgColor, int bgColor, int anotherColor) {
        mForeground = fgColor;
        mBackground = bgColor;
        this.anotherColor = anotherColor;
    }

    @Override
    protected boolean onLevelChange(int level) {
        invalidateSelf();
        return true;
    }

    @Override
    public void draw(Canvas canvas) {
        float level = getLevel() / 10000f;
        int roundValue = 5;
        int strokeSize = 2;
        Rect b = getBounds();
        float gapWidth = b.height() / 15f; //it was 2f before
        float segmentWidth = (b.width() - (numberOfSegments - 1) * gapWidth) / numberOfSegments;
        mSegment.set(0, 0, segmentWidth, b.height());
        mPaint.setColor(mForeground);

        strokePaint.setStyle(Paint.Style.STROKE);
        strokePaint.setColor(anotherColor);
        strokePaint.setStrokeWidth(strokeSize);
        for (int i = 0; i < numberOfSegments; i++) {
            float loLevel = i / (float) numberOfSegments;
            float hiLevel = (i + 1) / (float) numberOfSegments;
            if (loLevel <= level && level <= hiLevel) {
                mPaint.setColor(mBackground);
                canvas.drawRoundRect(new RectF(mSegment.left, mSegment.top, mSegment.right, mSegment.bottom), roundValue, roundValue, mPaint);


                float middle = mSegment.left + numberOfSegments * segmentWidth * (level - loLevel);
                mPaint.setColor(mForeground);
                canvas.drawRoundRect(new RectF(mSegment.left, mSegment.top, middle, mSegment.bottom), roundValue, roundValue, mPaint);
                canvas.drawRoundRect(new RectF(mSegment.left, mSegment.top, mSegment.right, mSegment.bottom), roundValue, roundValue, strokePaint);
                mPaint.setColor(mBackground);
            } else {
                canvas.drawRoundRect(mSegment, roundValue, roundValue, mPaint);
                canvas.drawRoundRect(new RectF(mSegment.left, mSegment.top, mSegment.right, mSegment.bottom), roundValue, roundValue, strokePaint);
            }
            mSegment.offset(mSegment.width() + gapWidth, 0);
        }
    }

    public void setNumberOfSegments(int numberOfSegments) {
        this.numberOfSegments = numberOfSegments;
    }

    @Override
    public void setAlpha(int alpha) {
    }

    @Override
    public void setColorFilter(ColorFilter cf) {
    }

    @Override
    public int getOpacity() {
        return PixelFormat.TRANSLUCENT;
    }
}