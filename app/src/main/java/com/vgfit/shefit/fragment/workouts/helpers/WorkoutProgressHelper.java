package com.vgfit.shefit.fragment.workouts.helpers;

import android.util.Log;

import com.vgfit.shefit.BuildConfig;
import com.vgfit.shefit.fragment.workouts.model.WorkoutMetaData;
import com.vgfit.shefit.realm.DayPlan;
import com.vgfit.shefit.realm.ExercisePlan;
import com.vgfit.shefit.realm.Superset;
import com.vgfit.shefit.realm.SupersetPlan;
import com.vgfit.shefit.realm.Workout;
import com.vgfit.shefit.realm.WorkoutExercise;
import com.vgfit.shefit.realm.WorkoutHistory;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import io.realm.Realm;
import io.realm.RealmList;

public class WorkoutProgressHelper {
    private WorkoutProgressHelper() {
        setMessageLog("WorkoutProgressHelper created");
    }

    private static WorkoutHistory currentHistory = null;
    private static final String TAG_LOG = "WorkoutHelper";
    private static int totalKcal = 0;

    public static Date getSelectedDate() {
        return selectedDate;
    }

    public static void setSelectedDate(Date selectedDate) {
        WorkoutProgressHelper.selectedDate = selectedDate;
    }

    private static Date selectedDate;

    private static int getTotalKcal() {
        return totalKcal;
    }

    private static void setTotalKcal(int totalKcal) {
        WorkoutProgressHelper.totalKcal = totalKcal;
    }


    public static void initWorkoutHistoryAsync(String idWorkout, String typeWorkout, String workoutName, Date date, String levelWorkout) {
        Realm.getDefaultInstance().executeTransactionAsync(bgRealm -> {
                    Log.d("TestWorkout", "levelWorkout--->" + levelWorkout);
                    String type = Superset.class.getName().equals(typeWorkout) ? WorkoutHistory.TYPE_SUPERSET : WorkoutHistory.TYPE_DAYPLAN;
                    WorkoutMetaData metaData = new WorkoutMetaData(idWorkout, type, workoutName, levelWorkout);
                    WorkoutHistory history = findOrCreateHistory(bgRealm, metaData, date);
                    currentHistory = bgRealm.copyFromRealm(history);
                    setSelectedDate(date);
                }, () -> setMessageLog("History initialized successfully"),
                error ->
                        setMessageLog("Error initializing workout history: " + error.getMessage()));
    }

    private static WorkoutHistory getCurrentHistory() {
        return currentHistory;
    }

    private static WorkoutHistory findOrCreateHistory(Realm realm, WorkoutMetaData metaData, Date date) {
        setMessageLog("Time Date---->" + date);
        Calendar calendar = Calendar.getInstance();
        if (date != null)
            calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date todayStart = calendar.getTime();
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        Date todayEnd = calendar.getTime();
        String completedDate = "completedDate";

        WorkoutHistory todayHistory = realm.where(WorkoutHistory.class)
                .equalTo("workoutId", metaData.idWorkout)
                .equalTo("workoutType", metaData.workoutType)
                .greaterThanOrEqualTo(completedDate, todayStart)
                .lessThan(completedDate, todayEnd)
                .sort(completedDate, io.realm.Sort.DESCENDING)
                .findFirst();

        // Daca nu există un workoutHistory pentru astazi, sau daca exista dar este completat,
        // atunci cream unul nou, <100 daca dorim sa continui
        if (todayHistory != null && todayHistory.getProgressPercentage() == 0) {
            setMessageLog("Found existing history for today: " + todayHistory.getId());
            return todayHistory;
        }else setMessageLog("History not existing");

        WorkoutHistory newHistory = realm.createObject(WorkoutHistory.class, java.util.UUID.randomUUID().toString());
        newHistory.setWorkoutId(metaData.idWorkout);
        newHistory.setWorkoutType(metaData.workoutType);
        newHistory.setWorkoutName(metaData.workoutName);
        newHistory.setWorkoutLevel(metaData.workoutLevel);
        if (metaData.workoutType.equals(WorkoutHistory.TYPE_DAYPLAN)) {
            try {
                newHistory.setWorkoutIntId(Integer.parseInt(metaData.idWorkout));
            } catch (NumberFormatException ignored) {
                // ignored
            }
        }

        return newHistory;
    }

    private static int calculateProgressPercent(Set<Integer> completedExercises, int totalSize) {
        return totalSize == 0 ? 0 : (int) ((float) completedExercises.size() / totalSize * 100);
    }

    public static void saveOrUpdateWorkoutHistory(
            Realm realm,
            String idWorkout,
            String typeWorkout,
            Set<Integer> completedExercises) {

        WorkoutHistory detachedHistory = getCurrentHistory();
        if (detachedHistory == null) return;

        WorkoutHistory history = realm.where(WorkoutHistory.class)
                .equalTo("id", detachedHistory.getId())
                .findFirst();

        if (history == null) return;

        List<WorkoutExercise> workoutExerciseList = extractAllExercises(realm, idWorkout, typeWorkout);

        int progressPercent = calculateProgressPercent(completedExercises, workoutExerciseList.size());
        if (progressPercent > history.getProgressPercentage()) {
            history.setProgressPercentage(progressPercent);
        } else {
            progressPercent = history.getProgressPercentage();
        }

        history.setCompletedDate(getSelectedDate());

        int totalDuration = calculateTotalDuration(workoutExerciseList, completedExercises);
        if (totalDuration > history.getDuration()) {
            history.setDuration(totalDuration);
        } else {
            totalDuration = history.getDuration();
        }

        int workoutKcal = getTotalKcal();
        int burnedKcal = calculateBurnedKcal(workoutExerciseList, completedExercises, workoutKcal);
        if (burnedKcal > history.getKcalBurned()) {
            history.setKcalBurned(burnedKcal);
        } else {
            burnedKcal = history.getKcalBurned();
        }

        RealmList<String> ids = history.getCompletedExerciseIds();
        if (ids == null) {
            ids = new RealmList<>();
            history.setCompletedExerciseIds(ids);
        }

        Set<String> newIds = collectExerciseInstanceIds(workoutExerciseList, completedExercises);
        for (String id : newIds) {
            if (!ids.contains(id)) {
                ids.add(id);
            }
        }

        setMessageLog("Saved workout history idWorkout--> " + idWorkout + " name-->" + history.getWorkoutName() + ", progress = "
                + progressPercent + "% with duration " + totalDuration
                + ", burnedKcal --> " + burnedKcal + "/" + workoutKcal
                + "selected Date--->" + getSelectedDate());
    }

    private static Set<String> collectExerciseInstanceIds(List<WorkoutExercise> allExercises, Set<Integer> completedIndexes) {
        Set<String> ids = new HashSet<>();
        for (Integer i : completedIndexes) {
            if (i < allExercises.size()) {
                WorkoutExercise ex = allExercises.get(i);
                if (ex != null && ex.getExercise() != null) {
                    ids.add(ex.getExercise().getId() + ":" + i);
                }
            }
        }
        return ids;
    }

    private static List<WorkoutExercise> extractAllExercises(Realm realm, String idWorkout, String typeWorkout) {
        if (Superset.class.getName().equals(typeWorkout)) {
            return extractFromSuperset(realm, idWorkout);
        } else if (DayPlan.class.getName().equals(typeWorkout)) {
            return extractFromDayPlan(realm, idWorkout);
        }
        return new ArrayList<>();
    }

    private static List<WorkoutExercise> extractFromSuperset(Realm realm, String idWorkout) {
        List<WorkoutExercise> exercises = new ArrayList<>();
        Workout workout = realm.where(Workout.class).equalTo("id", idWorkout).findFirst();
        if (workout != null && workout.getWorkoutExercises() != null) {
            exercises.addAll(workout.getWorkoutExercises());
            setTotalKcal(workout.getKcal());
        }
        return exercises;
    }

    private static DayPlan getDayPlan(Realm realm, String idWorkout) {
        try {
            int dayId = Integer.parseInt(idWorkout);
            return realm.where(DayPlan.class).equalTo("id", dayId).findFirst();
        } catch (Exception e) {
            setMessageLog("Invalid dayPlan ID: " + e);
            return null;
        }
    }

    private static void addExercisesFromSupersetPlans(DayPlan dayPlan, List<WorkoutExercise> exercises) {
        for (SupersetPlan plan : dayPlan.getSupersetPlans()) {
            if (plan.getListExercisesPlan() != null) {
                for (ExercisePlan exercisePlan : plan.getListExercisesPlan()) {
                    if (exercisePlan != null && exercisePlan.getExercise() != null) {
                        WorkoutExercise workoutExercise = new WorkoutExercise();
                        workoutExercise.setExercise(exercisePlan.getExercise());
                        workoutExercise.setDuration(exercisePlan.getTime());
                        workoutExercise.setRestTime(plan.getRestTime());
                        workoutExercise.setOrder(exercisePlan.getOrder());
                        exercises.add(workoutExercise);
                    }
                }
            }
        }
    }

    private static List<WorkoutExercise> extractFromDayPlan(Realm realm, String idWorkout) {
        List<WorkoutExercise> exercises = new ArrayList<>();
        DayPlan dayPlan = getDayPlan(realm, idWorkout);
        if (dayPlan != null && dayPlan.getSupersetPlans() != null) {
            addExercisesFromSupersetPlans(dayPlan, exercises);
            String kcalStr = dayPlan.getRecomandations();
            int kcal = getTotalKcalFromDayPlan(kcalStr);
            setTotalKcal(kcal);
        }
        return exercises;
    }

    private static int calculateTotalDuration(List<WorkoutExercise> allExercises, Set<Integer> completedIndexes) {
        int totalDuration = 0;

        for (Integer index : completedIndexes) {
            if (index < allExercises.size()) {
                WorkoutExercise exercise = allExercises.get(index);
                if (exercise != null) {
                    totalDuration += exercise.getDuration();
                    totalDuration += exercise.getRestTime();
                }
            }
        }

        return totalDuration;
    }

    private static int getTotalKcalFromDayPlan(String kcalStrInitial) {
        int kcal = 0;
        try {
            String kcalStr = kcalStrInitial.replaceAll("\\D", "");
            if (!kcalStr.isEmpty()) {
                kcal = Integer.parseInt(kcalStr);
            }
        } catch (Exception e) {
//            not used
        }
        return kcal;
    }

    private static int calculateBurnedKcal(List<WorkoutExercise> allExercises, Set<Integer> completedIndexes, int totalKcal) {
        if (allExercises.isEmpty()) return 0;

        float kcalPerExercise = (float) totalKcal / allExercises.size();

        float burnedKcal = 0;
        for (Integer index : completedIndexes) {
            if (index < allExercises.size()) {
                burnedKcal += kcalPerExercise;
            }
        }
        return Math.round(burnedKcal);
    }

    private static void setMessageLog(String message) {
        if (BuildConfig.DEBUG)
            Log.d(TAG_LOG, message);
    }
}
