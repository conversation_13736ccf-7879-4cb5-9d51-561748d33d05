package com.vgfit.shefit.fragment.profile;

import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.bumptech.glide.Glide;
import com.vgfit.shefit.databinding.FragmentProfilePurchaseBinding;

public class ProfilePurchase extends Fragment {

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        com.vgfit.shefit.databinding.FragmentProfilePurchaseBinding binding = FragmentProfilePurchaseBinding.inflate(inflater, container, false);
        View view = binding.getRoot();
        if (getContext() != null)
            Glide.with(getContext())
                    .load(Uri.parse("file:///android_asset/images_profile/" + "subscribtion.png"))
                    .into(binding.ivSubscription);

        binding.animationViewDone.setImageAssetsFolder("images/");
        binding.animationViewDone.setAnimation("dataSimple.json");
        binding.animationViewDone.loop(true);

        return view;
    }

}