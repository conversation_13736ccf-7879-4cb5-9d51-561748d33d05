package com.vgfit.shefit.fragment.personal_plan.player_plan;

import static com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED;
import static com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;

import com.google.android.exoplayer2.C;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.util.EventLogger;
import com.vgfit.shefit.fragment.personal_plan.callbacks.PlayerSizeView;
import com.vgfit.shefit.fragment.personal_plan.model.OneDayData;
import com.vgfit.shefit.fragment.workouts.downloader.Downloaded;
import com.vgfit.shefit.fragment.workouts.downloader.DownloaderManager;
import com.vgfit.shefit.fragment.workouts.streaming.Utils;

import java.io.File;
import java.util.ArrayList;

public class MainPlanPlayer implements Downloaded {

    private static final String KEY_WINDOW = "window";
    private static final String KEY_POSITION = "position";
    private Context context;
    private ArrayList<OneDayData> list;
    private ExoPlayer player;
    private PlayerView playerView;
    private int currentWindow;
    private long playbackPosition;
    private boolean shouldAutoPlay;
    private DefaultTrackSelector trackSelector;
    private boolean validFormat = true;
    private boolean isBadInternet;
    private String pathExistingVideo;
    private PlayerSizeView playerSizeView;
    private DataSource.Factory dataSourceFactory;

    public MainPlanPlayer(Context context, boolean isBadInternet, PlayerSizeView playerSizeView) {
        this.context = context;
        this.isBadInternet = isBadInternet;
        this.playerSizeView = playerSizeView;
    }

    public void setListPlanCover(ArrayList<OneDayData> list, PlayerView playerView) {
        this.list = list;
        this.playerView = playerView;
    }

    public void initPlayer(Bundle savedInstanceState, int position, DataSource.Factory dataSourceFactory) {
        if (savedInstanceState == null) {
            currentWindow = position;
            playbackPosition = 0;
        } else {
            currentWindow = savedInstanceState.getInt(KEY_WINDOW);
            playbackPosition = savedInstanceState.getLong(KEY_POSITION);
        }
        shouldAutoPlay = true;
        this.dataSourceFactory = dataSourceFactory;
    }

    public void initializePlayer() {
        assert playerView != null;
        playerView.requestFocus();
        player = new ExoPlayer.Builder(context).build();
        player.addAnalyticsListener(new EventLogger(trackSelector));
        playerView.setPlayer(player);
        player.setRepeatMode(Player.REPEAT_MODE_ONE);
        player.setPlayWhenReady(shouldAutoPlay);
        String path = "";
        ArrayList<String> listFiles = new ArrayList<>();
//        isBadInternet = true;
        for (int i = 0; i < list.size(); i++) {
            if (!isBadInternet) {
                path = list.get(i).getCoverVideoPlan().getVideoCover();
                Log.d("EmmTest", "path 0--->" + path);
                MediaSource mediaSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(path));
                player.addMediaSource(mediaSource);
                if (i == 6) {
//                    MediaSource mediaSource = new ExtractorMediaSource.Factory(dataSourceFactory)
//                            .setExtractorsFactory(new DefaultExtractorsFactory())
//                            .createMediaSource(Uri.parse(path));
//                    mediaSources[i] = mediaSource;
                }
            } else {
                if (checkIfFileExists(list.get(i).getCoverVideoPlan().getVideoCover())) {
                    path = pathExistingVideo;
                    Log.d("EmmTest", "path--->" + path);
                } else {
                    path = list.get(i).getCoverVideoPlan().getVideoCover();
                    Log.d("EmmTest", "path 2--->" + path);
                }
                MediaSource mediaSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(path));
                player.addMediaSource(mediaSource);
                listFiles.add(i, list.get(i).getCoverVideoPlan().getVideoCover());
            }
        }
        if (listFiles.size() > 0 && isBadInternet)
            new DownloaderManager(context, this, listFiles).startDownload();

        boolean haveStartPosition = currentWindow != C.INDEX_UNSET;
        if (haveStartPosition) player.seekTo(currentWindow, playbackPosition);
        player.prepare();
        player.setVolume(0F);
        player.addListener(new Player.Listener() {
            @Override
            public void onPlayerError(PlaybackException error) {
                validFormat = false;
//                releasePlayer();
//                initializePlayer();
                player.stop();
                player.prepare();
                player.setPlayWhenReady(shouldAutoPlay);
                Player.Listener.super.onPlayerError(error);
                if (error.errorCode == ERROR_CODE_IO_NETWORK_CONNECTION_FAILED || error.errorCode == ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT) {
                    Toast.makeText(context, error.getMessage(), Toast.LENGTH_SHORT).show();
                }
                Log.d("TestError", "error player-->" + error.getMessage());
            }

            @Override
            public void onPlaybackStateChanged(int playbackState) {
                Player.Listener.super.onPlaybackStateChanged(playbackState);
                Log.d("TestPlayer", "Clicked state->" + playbackState);
            }
        });
    }

    private boolean checkIfFileExists(String nameVideo) {
        int lastPosition = nameVideo.lastIndexOf("/") + 1;

        String video = nameVideo.substring(lastPosition);

        String path = Utils.getVideoCacheDir(context) + "/" + video;

        File file = new File(path);

        if (file.exists()) {
            pathExistingVideo = path;
            return true;
        } else return false;
    }


    public ExoPlayer getPlayer() {
        return this.player;
    }

    public void seekToPrev() {
        if (currentWindow - 1 >= 0) {
            currentWindow -= 1;
            player.seekTo(currentWindow, C.TIME_UNSET);
        }
    }

    public void seekToNext() {
        if (currentWindow + 1 < list.size()) {
            currentWindow += 1;
            player.seekTo(currentWindow, C.TIME_UNSET);
        }
    }

    public int getCurrentPosition() {
        return currentWindow;
    }

    public void setToPosition(int position) {
        if (position < list.size() && player != null)
            player.seekTo(position, C.TIME_UNSET);
    }

    public void releasePlayer() {
        if (player != null) {
            updateStartPosition();
            shouldAutoPlay = player.getPlayWhenReady();
            player.stop();
            player.release();
            player = null;
            trackSelector = null;
        }
    }

    public void updateStartPosition() {
        if (player != null) {
            playbackPosition = player.getCurrentPosition();
            currentWindow = player.getCurrentWindowIndex();
        }
    }

    @Override
    public void isSuccess(boolean valid) {
        if (valid) Log.e("SpTest", "isDownloaded");
    }
}
