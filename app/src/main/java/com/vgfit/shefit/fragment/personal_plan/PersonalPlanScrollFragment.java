package com.vgfit.shefit.fragment.personal_plan;

import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;
import static com.vgfit.shefit.util.screen.PresentBottomBar.getHeightBottomBar;
import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.Manifest;
import android.app.Dialog;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.ColorMatrix;
import android.location.LocationManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SnapHelper;

import com.github.rubensousa.gravitysnaphelper.GravitySnapHelper;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.nabinbhandari.android.permissions.PermissionHandler;
import com.nabinbhandari.android.permissions.Permissions;
import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.MenuBottom;
import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.PlansWorkoutScrollBinding;
import com.vgfit.shefit.fragment.calendar.CalendarFragment;
import com.vgfit.shefit.fragment.nutrition.NutritionDayScrollFr;
import com.vgfit.shefit.fragment.personal_plan.adapter.AdapterPlanDay;
import com.vgfit.shefit.fragment.personal_plan.adapter.AdapterWeekDaysScroll;
import com.vgfit.shefit.fragment.personal_plan.callbacks.CalendarClicked;
import com.vgfit.shefit.fragment.personal_plan.callbacks.CurrentPlan;
import com.vgfit.shefit.fragment.personal_plan.callbacks.PlanClicked;
import com.vgfit.shefit.fragment.personal_plan.model.DayOfWeek;
import com.vgfit.shefit.fragment.personal_plan.model.OneDayData;
import com.vgfit.shefit.fragment.personal_plan.structure.PersonalPlanPresenter;
import com.vgfit.shefit.fragment.personal_plan.structure.PersonalPlanView;
import com.vgfit.shefit.fragment.premium.OnSingleClickListener;
import com.vgfit.shefit.fragment.workouts.WorkoutsVideoFr;
import com.vgfit.shefit.realm.DayPlan;
import com.vgfit.shefit.realm.Workout;
import com.vgfit.shefit.realm.WorkoutExercise;
import com.vgfit.shefit.util.AnimatorView;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.RCUtils;
import com.vgfit.shefit.util.SpeedyLinearLayoutManager;
import com.vgfit.shefit.util.Translate;
import com.yarolegovich.discretescrollview.DSVOrientation;
import com.yarolegovich.discretescrollview.DiscreteScrollView;
import com.yarolegovich.discretescrollview.transform.ScaleTransformer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PersonalPlanScrollFragment extends Fragment implements PersonalPlanView, CurrentPlan, DiscreteScrollView.OnItemChangedListener, CalendarClicked, PlanClicked {
    private PlansWorkoutScrollBinding binding;
    private PersonalPlanPresenter personalPlanPresenter;
    private Context context;
    private int posSelDayUser = 0;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private final Handler handler = new Handler();
    private static final String KEY_COUNT_DENIED_PERMISSION = "KEY_COUNT_DENIED_PERMISSION";
    private static final String ACTIVE_DAILY_WORKOUT = "active_daily_workout";
    private int resWeather;
    private int countDeniedPermission = 0;
    private String tempWeather = "-100";
    private String currentDayName;
    private AdapterPlanDay adapterPlanDay;
    private int heightCell;
    private int position = 0;
    private boolean isGrantedWeather = false;
    private boolean isEnabledNot = false;
    private boolean isValidScroll = true;

    private int oldPosition = -1;
    private boolean isAnimateRequest = false;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        context = getContext();
        prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        personalPlanPresenter = new PersonalPlanPresenter(this, prefsUtilsWtContext);
        personalPlanPresenter.getCurrentDay();
        AnimatorView animatorView = new AnimatorView();
        animatorView.initScaleUpAlpha();
        animatorView.initScaleDownAlpha();
        resWeather = context.getResources().getIdentifier("ic_weather_off", "drawable", context.getPackageName());
        DisplayMetrics displaymetrics = new DisplayMetrics();
        if (getActivity() != null)
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(displaymetrics);
        heightCell = displaymetrics.heightPixels - dpToPx(context, 97) - dpToPx(context, 50) - getHeightBottomBar(context); //55 dp top Layout - 40 dp Tablayout
        isEnabledNot = areNotificationsEnabled();
        isGrantedWeather = isGrantedGPS(context);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = PlansWorkoutScrollBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setStatusBar(view);
        new MenuBottom((MainActivity) getActivity(), binding.containerMenu);

        ViewTreeObserver vto = binding.listHorizontal.getViewTreeObserver();
        vto.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                heightCell = binding.listHorizontal.getHeight();
                binding.listHorizontal.getLayoutParams().height = heightCell;
                binding.listHorizontal.requestLayout();
                view.getViewTreeObserver().removeGlobalOnLayoutListener(this);
            }
        });
        if (isGrantedWeather)
            doWeather(true);

        binding.top.menuButton.setOnClickListener(v -> {
            if (getActivity() != null) ((MainActivity) getActivity()).openDrawer();
        });
        binding.top.menuButton.setVisibility(View.INVISIBLE);
        binding.recyclerWeekDay.setLayoutManager(new SpeedyLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false, 100));

        personalPlanPresenter.getDailyWorkout();
        binding.imageWeather.setOnClickListener(v -> {
            setVibrate(v);
            if (!isGrantedWeather)
                doWeatherDialog();
        });
        binding.tempWeather.setText(tempWeather.equals("-100") ? "0" : tempWeather);
        binding.tempWeather.setVisibility(tempWeather.equals("-100") ? View.GONE : View.VISIBLE);
        binding.indicator.setVisibility(View.GONE);
        binding.imageWeather.setBackgroundResource(resWeather);
        binding.currentDay.setText(currentDayName);
        binding.recyclerWeekDay.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE && isValidScroll) {
                    LinearLayoutManager lManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                    if (lManager != null) {
                        int firstElementPosition = lManager.findFirstVisibleItemPosition() - 1;
                        if (firstElementPosition < 0) firstElementPosition = 0;
                        if (adapterPlanDay.getItemCount() > firstElementPosition) {
                            binding.listHorizontal.smoothScrollToPosition(firstElementPosition);
                            oldPosition = firstElementPosition;
                        }
                    }
                }
            }
        });
        binding.notificationActive.setOnClickListener(new OnSingleClickListener() {
            @Override
            public void onSingleClick(View v) {
                setVibrate(v);
                isEnabledNot = areNotificationsEnabled();
                if (!isEnabledNot) pushNotification();
            }
        });

        defaultNotificationState();
        binding.indicator.createIndicators(3, 0);
        animateWeatherRequest(0);
        binding.calendarButton.setOnClickListener(v -> {
            setVibrate(v);
            openCalendar();
        });
    }

    private void animateWeatherRequest(int position) {
        handler.postDelayed(() -> {
            try {
                binding.indicator.animatePageSelected(position);
                int newPosition = position + 1 < 3 ? position + 1 : 0;
                if (isAnimateRequest)
                    animateWeatherRequest(newPosition);
            } catch (Exception ignored) {
            }
        }, 300);
    }

    public void pushNotification() {

        String[] permissions = {Manifest.permission.POST_NOTIFICATIONS};
        Permissions.check(getActivity(), permissions, Translate.getValue("allow_geolocation_access")/*rationale*/, null/*options*/, new PermissionHandler() {
            @Override
            public void onGranted() {
                defaultNotificationState();
            }

            @Override
            public void onDenied(Context context, ArrayList<String> deniedPermissions) {
                super.onDenied(context, deniedPermissions);
                defaultNotificationState();
            }
        });
    }

    public boolean isGrantedGPS(Context context) {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED;
    }

    public void defaultNotificationState() {
        isEnabledNot = areNotificationsEnabled();
        if (isEnabledNot)
            binding.notificationActive.setImageResource(R.drawable.notifications_on);
        else {
            binding.notificationActive.setImageResource(R.drawable.notifications_off);
        }
    }

    @Override
    public void planWeek(ArrayList<OneDayData> listWeekPlan, int currentDay) {
        int pos = posSelDayUser != -1 ? posSelDayUser : currentDay;
        adapterPlanDay = new AdapterPlanDay(listWeekPlan, this, this);
        binding.listHorizontal.setOrientation(DSVOrientation.HORIZONTAL);
        binding.listHorizontal.addOnItemChangedListener(this);
        binding.listHorizontal.setAdapter(adapterPlanDay);
        binding.listHorizontal.setItemTransitionTimeMillis(100);
        binding.listHorizontal.setItemTransformer(new ScaleTransformer.Builder()
                .setMinScale(0.92f)
                .build());
        binding.listHorizontal.setOffscreenItems(2);
        binding.listHorizontal.addScrollStateChangeListener(new DiscreteScrollView.ScrollStateChangeListener<RecyclerView.ViewHolder>() {
            @Override
            public void onScrollStart(@NonNull RecyclerView.ViewHolder currentItemHolder, int adapterPosition) {
                isValidScroll = false;
            }

            @Override
            public void onScrollEnd(@NonNull RecyclerView.ViewHolder currentItemHolder, int adapterPosition) {
                isValidScroll = true;
            }

            @Override
            public void onScroll(float scrollPosition, int currentPosition, int newPosition, @Nullable RecyclerView.ViewHolder currentHolder, @Nullable RecyclerView.ViewHolder newCurrent) {
                //not used
            }
        });
        binding.listHorizontal.addScrollListener((scrollPosition, currentPosition, newPosition, currentHolder, newCurrent) -> {
            try {
                if (oldPosition != newPosition) {
                    oldPosition = newPosition;
                    animateScroll(newPosition);
                }
            } catch (Exception ignored) {
                //ignored
            }
        });
        try {
            binding.listHorizontal.scrollToPosition(pos);
        } catch (Exception ignored) {
            //ignored
        }
    }

    private void animateScroll(int daySelected) {
        int posN = daySelected + 3;
        if (daySelected < position)
            posN = daySelected + 1;
        try {
            position = daySelected;
            binding.recyclerWeekDay.smoothScrollToPosition(posN);
        } catch (Exception ignored) {
            //ignored
        }
    }


    @Override
    public void openListExerciseWorkout(Workout workout, String supersetName, Long timeLong) {
        if (workout != null) {
            changeFragment(new ArrayList<>(workout.getWorkoutExercises()), DayPlan.class.getName(), workout.getId(), workout.getName(), timeLong);
        }
    }

    private void changeFragment(ArrayList<WorkoutExercise> workoutExerciseList, String typeWorkout, String idWorkout, String nameWorkout, Long timeLong) {
        FragmentTransaction fragmentTransaction;
        fragmentTransaction = getParentFragmentManager().beginTransaction();
        fragmentTransaction.addToBackStack(null);
        WorkoutsVideoFr workoutsVideoFr = new WorkoutsVideoFr();
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList("workout", workoutExerciseList);
        bundle.putString("typeWorkout", typeWorkout);
        bundle.putString("idWorkout", idWorkout);
        bundle.putString("nameWorkout", nameWorkout);
        bundle.putLong("timeLong", timeLong);
        bundle.putString("level", getLevelWorkout());
        workoutsVideoFr.setArguments(bundle);
        fragmentTransaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
        fragmentTransaction.replace(R.id.root_fragment, workoutsVideoFr);
        fragmentTransaction.commitAllowingStateLoss(); //not ".commit" 'cause Handling IllegalStateException: Can not perform this action after onSaveInstanceState
    }
    private String getLevelWorkout() {
        String levelWorkout = prefsUtilsWtContext.getStringPreferenceProfile(ACTIVE_DAILY_WORKOUT, "");
        if (!levelWorkout.isEmpty()) {
            if (levelWorkout.equals("2")) {
                levelWorkout = "tone_my_body";
            } else if (levelWorkout.equals("3")) {
                levelWorkout = "gain_muscles";
            } else if (levelWorkout.equals("4")) {
                levelWorkout = "be_more_active";
            } else if (levelWorkout.equals("1")) {
                levelWorkout = "lose_weightt";
            }
        }
        Log.d("TestLevelWorkout", "levelWorkout==" + levelWorkout);
        return levelWorkout;
    }

    private void openCalendar() {
        FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
        CalendarFragment fragment = CalendarFragment.newInstance();
        transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
        transaction.replace(R.id.root_fragment, fragment).addToBackStack(null).commit();
    }

    private void openMainSubscribe() {
        RCUtils rcUtils = new RCUtils(getContext());
        rcUtils.getMainPaywallAndroidOffer(this);
    }

    @Override
    public void currentDay(int pos) {
        posSelDayUser = pos;
    }

    @Override
    public void currentDayName(String currentDayName) {
        this.currentDayName = currentDayName;
    }

    @Override
    public void daysCalendar(ArrayList<DayOfWeek> dayOfWeeks) {
        AdapterWeekDaysScroll adapterWeekDays = new AdapterWeekDaysScroll(dayOfWeeks, this, binding.recyclerWeekDay);
        binding.recyclerWeekDay.setAdapter(adapterWeekDays);
        SnapHelper snapHelper = new GravitySnapHelper(Gravity.START);
        snapHelper.attachToRecyclerView(binding.recyclerWeekDay);

    }

    @Override
    public void setImageWeather(String imageWeather) {
        this.resWeather = context.getResources().getIdentifier(imageWeather, "drawable", context.getPackageName());
        this.binding.imageWeather.setBackgroundResource(resWeather);
    }

    @Override
    public void startAnimRequest() {
        isAnimateRequest = true;
        binding.indicator.setVisibility(View.VISIBLE);
        animateWeatherRequest(0);
    }

    @Override
    public void setTempWeather(String tempWeather) {
        this.tempWeather = tempWeather;
        binding.tempWeather.setText(tempWeather);
        isAnimateRequest = false;
        binding.tempWeather.setVisibility(View.VISIBLE);
        binding.indicator.setVisibility(View.GONE);
    }

    public void animateTextView(TextView textview) {
        textview.animate().scaleX(1.25f).scaleY(1.25f).setDuration(150).withEndAction(() -> textview.animate().scaleX(1f).scaleY(1f).setDuration(150));
    }

    @Override
    public void daySelected(int position) {
        //not used
    }

    @Override
    public void setHideWeather() {
        binding.imageWeather.setVisibility(View.INVISIBLE);
        binding.indicator.setVisibility(View.GONE);
        isAnimateRequest = false;

    }

    private void buildAlertMessageNoGps() {
        final AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setMessage("Your GPS seems to be disabled, do you want to enable it?").setCancelable(false).setPositiveButton("Yes", (dialog, id) -> startActivity(new Intent(android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS))).setNegativeButton("No", (dialog, id) -> dialog.cancel());
        final AlertDialog alert = builder.create();
        alert.show();
    }

    public void doWeather(boolean isClick) {

        String[] permissions = {Manifest.permission.ACCESS_COARSE_LOCATION};
        Permissions.check(getActivity(), permissions, Translate.getValue("allow_geolocation_access")/*rationale*/, null/*options*/, new PermissionHandler() {
            @Override
            public void onGranted() {
                isGrantedWeather = true;
                prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_COUNT_DENIED_PERMISSION, 0);
                if (isClick) {
                    statusCheck();
                } else
                    locationInit();
            }

            @Override
            public void onDenied(Context context, ArrayList<String> deniedPermissions) {
                super.onDenied(context, deniedPermissions);
                countDeniedPermission = prefsUtilsWtContext.getIntegerPreferenceProfile(KEY_COUNT_DENIED_PERMISSION, 0) + 1;
                prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_COUNT_DENIED_PERMISSION, countDeniedPermission);
            }
        });
    }

    public void statusCheck() {
        final LocationManager manager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);

        if (!manager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
            buildAlertMessageNoGps();
        } else {
            locationInit();
        }
    }

    public boolean areNotificationsEnabled() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            if (!manager.areNotificationsEnabled()) {
                return false;
            }
            List<NotificationChannel> channels = manager.getNotificationChannels();
            for (NotificationChannel channel : channels) {
                if (channel.getImportance() == NotificationManager.IMPORTANCE_NONE) {
                    return false;
                }
            }
            return true;
        } else {
            return NotificationManagerCompat.from(context).areNotificationsEnabled();
        }
    }

    private void locationInit() {
        createLocationRequest();
    }

    private void createLocationRequest() {

        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED
                && ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            return;
        }

        FusedLocationProviderClient fusedLocationClient = LocationServices.getFusedLocationProviderClient(context);
        fusedLocationClient.getLastLocation().addOnSuccessListener(location -> {
            if (location != null) {
                double latitude = location.getLatitude();
                double longitude = location.getLongitude();
                if (personalPlanPresenter != null) {
                    if (longitude != 0) {
                        binding.imageWeather.setEnabled(false);
                    } else
                        setHideWeather();
                    personalPlanPresenter.getWeather(latitude, longitude);
                }
            }
        });
    }

    @Override
    public void onCurrentItemChanged(@Nullable RecyclerView.ViewHolder viewHolder, int i) {
        adapterPlanDay.currentPlan(i);
        animateScroll(i);
        if (getView() != null)
            setVibrate(getView());
    }

    @Override
    public void dayData(OneDayData oneDayData, int posDay) {
        binding.dayCurrent.setText(oneDayData.getDayOfWeek().getNameShort());
        animateTextView(binding.dayCurrent);
        posSelDayUser = posDay;
        if (!oneDayData.isShowDay()) {
            ColorMatrix matrix = new ColorMatrix();
            matrix.setSaturation(0);
            binding.dayCurrentContainer.setBackgroundResource(R.drawable.current_item_background_);
            binding.dayCurrentInfo.setText(Translate.getValueLine("keep_calm_and_rest", Collections.singletonList(2), false));
        } else {
            binding.dayCurrentContainer.setBackgroundResource(R.drawable.current_item_background);
            binding.dayCurrentInfo.setText(Translate.getValueLine("it’s_time_for_sport!", Collections.singletonList(2), false));
        }
    }

    @Override
    public void positionCalendar(int position) {
        if (adapterPlanDay.getItemCount() > position) {
            binding.listHorizontal.smoothScrollToPosition(position);
        }
    }

    @Override
    public void planAccessed(OneDayData oneDayData, int position) {
        if (Constant.premium) {
            if (oneDayData.isShowDay())
                personalPlanPresenter.castToNormalWorkout(oneDayData);
        } else
            openMainSubscribe();
    }

    @Override
    public void mealAccessed(OneDayData oneDayData, String nameMeal, int position) {
        if (Constant.premium) {
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            Fragment fragment = NutritionDayScrollFr.newInstance(posSelDayUser, nameMeal);
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragment).addToBackStack(null).commit();
        } else openMainSubscribe();
    }

    public void doWeatherDialog() {


        final Dialog d = new Dialog(context, R.style.Theme_AppCompat_Light_Dialog_Alert);
        if (d.getWindow() != null) {
            d.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
            d.setContentView(R.layout.dialog_weather);
            d.setCanceledOnTouchOutside(true);
            d.setCancelable(true);
            final TextView titleWeather = d.findViewById(R.id.titleWeather);
            titleWeather.setText(Translate.getValue("weather_location"));
            final TextView shortDescription = d.findViewById(R.id.shortDescription);
            shortDescription.setText(Translate.getValue("allow_geolocation_access"));


            final Button btnOk = d.findViewById(R.id.btnDone);
            btnOk.setText(Translate.getValue("allow"));
            btnOk.setOnClickListener(arg0 -> {
                setVibrate(arg0);
                int resWeather = context.getResources().getIdentifier("ic_weather_off", "drawable", context.getPackageName());
                binding.tempWeather.setVisibility(View.GONE);
                binding.imageWeather.setBackgroundResource(resWeather);
                doWeather(true);
                d.dismiss();
            });

            d.show();
        }
    }
}
