package com.vgfit.shefit.fragment.more.service;

import android.content.Context;

import com.vgfit.shefit.fragment.more.model.ItemMore;

import java.util.ArrayList;

public class MoreMainData {

    Context context;

    public MoreMainData(Context context) {
        this.context = context;
    }

    public ArrayList<ItemMore> createMainListMore() {

        ArrayList<ItemMore> list = new ArrayList<>();

        ItemMore item1 = new ItemMore("stay_connected.png", "Stay connected with VGFIT");
        ItemMore item2 = new ItemMore("settings.png", "Settings");
        ItemMore item3 = new ItemMore("rate_us.png", "Rate us");
        ItemMore item4 = new ItemMore("send_feedback.png", "Send Feedback");
        ItemMore item5 = new ItemMore("tell_a_friend.png", "Tell a Friend");
        ItemMore item6 = new ItemMore("facebook.png", "Share app on Facebook");
        ItemMore item7 = new ItemMore("twitter.png", "Share app on Twitter");
        ItemMore item8 = new ItemMore("share.png", "Our other apps");

        list.add(item1);
        list.add(item2);
        list.add(item3);
        list.add(item4);
        list.add(item5);
        list.add(item6);
        list.add(item7);
        list.add(item8);

        return list;
    }
}
