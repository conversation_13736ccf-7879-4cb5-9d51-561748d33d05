package com.vgfit.shefit.fragment.workouts.service;

import static com.vgfit.shefit.util.TextUtils.convertDpToPx;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.content.Context;
import android.util.DisplayMetrics;
import android.view.View;
import android.widget.RelativeLayout;

public class NextViewAnimation {
    private Context context;
    private int onePartSize;

    public NextViewAnimation(Context context) {
        this.context = context;
    }

    public Animator getViewScaleAnimatorBigger(final View target, final View maskBackground, View containerText) {
        AnimatorSet animatorSet = new AnimatorSet();
        int needMargin = 0;
        int haveMargin = onePartSize;
        setSizesContainerText(containerText);
        setSizesVideo(target);
        setSizesMask(maskBackground);


        ValueAnimator widthAnimator = ValueAnimator.ofInt(haveMargin, needMargin);
        widthAnimator.addUpdateListener(animation -> {
            RelativeLayout.MarginLayoutParams params = (RelativeLayout.MarginLayoutParams) target.getLayoutParams();
            params.setMargins(0, 0, -(int) animation.getAnimatedValue(), 0);
            target.setLayoutParams(params);
        });
        animatorSet.play(widthAnimator);
        return animatorSet;
    }

    private void setSizesVideo(View target) {
//        target.getLayoutParams().height = onePartSize+onePartSize/3;
        target.getLayoutParams().width = onePartSize + convertDpToPx(context, 10);
    }

    private void setSizesMask(View target) {
//        target.getLayoutParams().height = onePartSize;
        target.getLayoutParams().width = onePartSize + convertDpToPx(context, 5);
    }

    private void setSizesContainerText(View target) {
        target.getLayoutParams().width = onePartSize;
    }

    public Animator getViewScaleAnimatorSmaller(final View target, final View maskBackground, View containerText) {
        AnimatorSet animatorSet = new AnimatorSet();
        int needMargin = onePartSize * 2;
        int haveMargin = 0;

        setSizesContainerText(containerText);
        setSizesVideo(target);
        setSizesMask(maskBackground);

        ValueAnimator widthAnimator = ValueAnimator.ofInt(haveMargin, needMargin);
        widthAnimator.addUpdateListener(animation -> {
            RelativeLayout.MarginLayoutParams params = (RelativeLayout.MarginLayoutParams) target.getLayoutParams();
            params.setMargins(0, 0, -(int) animation.getAnimatedValue(), 0);
            target.setLayoutParams(params);
        });
        animatorSet.play(widthAnimator);
        return animatorSet;
    }

    public void countMetrics() {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        onePartSize = (int) (metrics.widthPixels / 2.2);
    }
}