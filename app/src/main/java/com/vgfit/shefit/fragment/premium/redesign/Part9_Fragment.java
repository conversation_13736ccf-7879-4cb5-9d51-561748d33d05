package com.vgfit.shefit.fragment.premium.redesign;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.os.Bundle;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.billing.BillingProcessor;
import com.vgfit.shefit.databinding.Part9SubscribeBinding;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.VibrateHepatic;
import com.vgfit.shefit.util.dialog.DialogHelper;
import com.vgfit.shefit.util.dialog.callback.DialogHelperCallback;

import java.util.List;


public class Part9_Fragment extends Fragment {
    private Part9SubscribeBinding binding;

    public static Part9_Fragment newInstance() {

        Bundle args = new Bundle();

        Part9_Fragment fragment = new Part9_Fragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = Part9SubscribeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        binding.restorePurchase.setOnClickListener(VibrateHepatic::setVibrate);
        binding.infoSubscribe.setMovementMethod(LinkMovementMethod.getInstance());
        binding.restorePurchase.setText(Translate.getValue("restore_purchase"));
        binding.restorePurchase.setOnClickListener(v -> {
            setVibrate(v);
            restorePurchase();
        });
    }

    private void restorePurchase() {
        if (getActivity() != null && MainActivity.readyToPurchase && ((MainActivity) getActivity()).bp != null) {
            BillingProcessor bp = ((MainActivity) getActivity()).bp;
            List<String> listPurchase = bp.listOwnedProducts();
            List<String> listSubscriptions = bp.listOwnedSubscriptions();
            if (listPurchase.isEmpty() && listSubscriptions.isEmpty()) {
                new DialogHelper(getContext()).showRestorePurchaseFailed(null);
            }else {
               new DialogHelper(getContext()).showRestorePurchaseSuccess(isConfirmed -> Constant.setPremium(true));
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

}
