package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentSleepRedesignBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;

import java.util.Arrays;

public class SleepRedesignFragment extends Fragment {
    private FragmentSleepRedesignBinding binding;
    private boolean isFt = false;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;

    public static SleepRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        SleepRedesignFragment fragment = new SleepRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View] Hours sleep View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentSleepRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();

        binding.rlContinue.setOnClickListener(view1 -> {
            isValidPush = false;
            PlanIsReadyRedesignFragment fragmentC = PlanIsReadyRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.animation_fade_in, R.anim.animation_fade_out, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_sleep").commitAllowingStateLoss();
            setVibrate(view1);
        });
        binding.chooseHours1.setOnClickListener(v -> {
            saveStatusHours("chooseHours1");
            getStatusHours("chooseHours1", binding.chooseHours1, binding.chooseHours1Txt);
            prefsUtilsWtContext.setBooleanPreference("chooseHours2", false);
            prefsUtilsWtContext.setBooleanPreference("chooseHours3", false);
            getStatusHours("chooseHours2", binding.chooseHours2, binding.chooseHours2Txt);
            getStatusHours("chooseHours3", binding.chooseHours3, binding.chooseHours3Txt);
            setVibrate(v);
        });
        binding.chooseHours2.setOnClickListener(v -> {
            saveStatusHours("chooseHours2");
            getStatusHours("chooseHours2", binding.chooseHours2, binding.chooseHours2Txt);
            prefsUtilsWtContext.setBooleanPreference("chooseHours1", false);
            prefsUtilsWtContext.setBooleanPreference("chooseHours3", false);
            getStatusHours("chooseHours1", binding.chooseHours1, binding.chooseHours1Txt);
            getStatusHours("chooseHours3", binding.chooseHours3, binding.chooseHours3Txt);
            setVibrate(v);
        });
        binding.chooseHours3.setOnClickListener(v -> {
            saveStatusHours("chooseHours3");
            getStatusHours("chooseHours3", binding.chooseHours3, binding.chooseHours3Txt);
            prefsUtilsWtContext.setBooleanPreference("chooseHours1", false);
            prefsUtilsWtContext.setBooleanPreference("chooseHours2", false);
            getStatusHours("chooseHours1", binding.chooseHours1, binding.chooseHours1Txt);
            getStatusHours("chooseHours2", binding.chooseHours2, binding.chooseHours2Txt);
            setVibrate(v);
        });
        getStatusHours("chooseHours1", binding.chooseHours1, binding.chooseHours1Txt);
        getStatusHours("chooseHours2", binding.chooseHours2, binding.chooseHours2Txt);
        getStatusHours("chooseHours3", binding.chooseHours3, binding.chooseHours3Txt);
        binding.backLayout.setOnClickListener(view12 -> toBack());
        binding.backTxt.getPaint().setUnderlineText(true);
        setParamsFragment();
    }

    private void saveStatusHours(String keyMeal) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyMeal, false);
        prefsUtilsWtContext.setBooleanPreference(keyMeal, !isSelected);
    }

    private void getStatusHours(String keyMeal, RelativeLayout layout, TextView textView) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyMeal, layout == binding.chooseHours2);
        layout.setSelected(isSelected);
        textView.setSelected(isSelected);
    }

    public void setParamsFragment() {
        Log.d("TestWeight", "weight Accessed isLbs-->" + isFt);
        binding.textView16.setText(Translate.getValueLine("how_many_hours_do_you_sleep", Arrays.asList(2, 4), false));
        binding.chooseHours1Txt.setText(Translate.getValue("5_7_hours"));
        binding.chooseHours2Txt.setText(Translate.getValue("7_10_hours"));
        binding.chooseHours3Txt.setText(Translate.getValue("over_10_hours"));
        binding.btnContinue.setText(Translate.getValue("next"));
        binding.backTxt.setText(Translate.getValue("go_back"));
        binding.linearProgress.setProgress(68);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
//                if (!lunchFirstTime)
                toBack();
                return true; // pretend we've processed it
            } else
                return false; // pass on to be processed as normal
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void toBack() {
        isValidPush = false;
        if (getActivity() != null)
            getActivity().onBackPressed();
    }
}
