package com.vgfit.shefit.fragment.composeui;

import android.animation.FloatEvaluator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.DecelerateInterpolator;
import android.widget.ScrollView;

/**
 * Universal Elastic Parallax Effect
 *
 * Poate fi aplicat pe orice 2 View-uri/containere pentru efectul elastic parallax
 * Similar cu ParallaxListView_new dar universal și configurabil
 */
public class UniversalElasticParallax {

    private ScrollView scrollView;
    private View primaryView;    // View-ul principal (se întinde când gesture în jos)
    private View secondaryView;  // View-ul secundar (se comprimă când gesture în jos)
    
    // Dimensiuni originale
    private int originalPrimaryHeight;
    private int originalSecondaryHeight;
    
    // Configurări
    private float elasticFactor = 4.0f;           // Factor de reducere pentru smoothness
    private float primaryMaxStretch = 1.5f;       // Max 150% pentru primary
    private float primaryMinCompress = 0.5f;      // Min 50% pentru primary
    private float secondaryMaxStretch = 1.33f;    // Max 133% pentru secondary
    private float secondaryMinCompress = 0.5f;    // Min 50% pentru secondary
    private float secondaryReductionFactor = 3.0f; // Factorul de reducere pentru secondary
    
    // Animații
    private int primaryAnimationDuration = 300;
    private int secondaryAnimationDuration = 280;
    private float primaryDecelerationFactor = 3.0f;
    private float secondaryDecelerationFactor = 2.5f;
    
    // State
    private boolean isUserTouching = false;
    
    /**
     * Constructor pentru Universal Elastic Parallax
     *
     * @param scrollView ScrollView-ul care va fi controlat
     * @param primaryView View-ul principal (se întinde când gesture în jos)
     * @param secondaryView View-ul secundar (se comprimă când gesture în jos)
     */
    public UniversalElasticParallax(ScrollView scrollView, View primaryView, View secondaryView) {
        this.scrollView = scrollView;
        this.primaryView = primaryView;
        this.secondaryView = secondaryView;

        initializeViews();
        setupElasticParallax();
    }
    
    /**
     * Inițializare view-uri și salvarea dimensiunilor originale
     */
    private void initializeViews() {
        if (primaryView != null) {
            primaryView.post(() -> {
                originalPrimaryHeight = primaryView.getHeight();
            });
        }
        
        if (secondaryView != null) {
            secondaryView.post(() -> {
                originalSecondaryHeight = secondaryView.getHeight();
            });
        }
    }
    
    /**
     * Setup pentru efectul elastic parallax
     */
    @SuppressLint("ClickableViewAccessibility")
    private void setupElasticParallax() {
        if (scrollView == null) return;

        scrollView.setOnTouchListener(new View.OnTouchListener() {
            private float startY = 0;
            
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        isUserTouching = true;
                        startY = event.getY();
                        Log.d("TestingScroll","Accessed ACTION_DOWN");
                        // Resetează scroll-ul la 0 pentru a preveni scroll-ul real
                        scrollView.scrollTo(0, 0);
                        break;

                    case MotionEvent.ACTION_MOVE:
                        Log.d("TestingScroll","Accessed ACTION_MOVE");
                        float currentY = event.getY();
                        float deltaY = startY - currentY; // Calculează din punctul de start

                        // Aplică efectul elastic în timpul gesture-ului
                        applyElasticEffect(deltaY);

                        // Forțează ScrollView să rămână la poziția 0
                        scrollView.scrollTo(0, 0);
                        break;

                    case MotionEvent.ACTION_UP:
                    case MotionEvent.ACTION_CANCEL:
                        Log.d("TestingScroll","Accessed ACTION_CANCEL");
                        isUserTouching = false;
                        // Animație de revenire la dimensiunile originale
                        animateBackToOriginalSize();
                        // Asigură-te că ScrollView rămâne la poziția 0
                        scrollView.scrollTo(0, 0);
                        break;
                }
                return true; // Consumă toate touch events pentru a bloca scroll-ul
            }
        });

        // Dezactivează complet scroll-ul pentru ScrollView
        scrollView.setOnScrollChangeListener((v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            // Forțează revenirea la poziția 0 dacă cumva se întâmplă scroll
            if (scrollY != 0) {
                scrollView.scrollTo(0, 0);
            }
        });
    }
    
    /**
     * Aplică efectul elastic pe view-uri în timpul gesture-ului
     */
    private void applyElasticEffect(float deltaY) {
        if (primaryView == null || secondaryView == null || 
            originalPrimaryHeight == 0 || originalSecondaryHeight == 0) return;
        
        // Calculează factorul elastic
        int elasticAmount = Math.abs((int) deltaY) / (int) elasticFactor;
        
        if (deltaY < 0) {
            // Gesture în jos - stretch primary, compress secondary
            int newPrimaryHeight = originalPrimaryHeight + elasticAmount;
            int maxPrimaryHeight = (int) (originalPrimaryHeight * primaryMaxStretch);
            
            if (newPrimaryHeight <= maxPrimaryHeight) {
                primaryView.getLayoutParams().height = newPrimaryHeight;
                primaryView.requestLayout();
            }
            
            // Compress secondary proporțional
            int secondaryReduction = (int) (elasticAmount / secondaryReductionFactor);
            int newSecondaryHeight = originalSecondaryHeight - secondaryReduction;
            int minSecondaryHeight = (int) (originalSecondaryHeight * secondaryMinCompress);
            
            if (newSecondaryHeight >= minSecondaryHeight) {
                secondaryView.getLayoutParams().height = newSecondaryHeight;
                secondaryView.requestLayout();
            }
            
        } else if (deltaY > 0) {
            // Gesture în sus - compress primary, stretch secondary
            int primaryReduction = elasticAmount;
            int newPrimaryHeight = originalPrimaryHeight - primaryReduction;
            int minPrimaryHeight = (int) (originalPrimaryHeight * primaryMinCompress);
            
            if (newPrimaryHeight >= minPrimaryHeight) {
                primaryView.getLayoutParams().height = newPrimaryHeight;
                primaryView.requestLayout();
            }
            
            // Stretch secondary ușor
            int secondaryIncrease = (int) (elasticAmount / secondaryReductionFactor);
            int newSecondaryHeight = originalSecondaryHeight + secondaryIncrease;
            int maxSecondaryHeight = (int) (originalSecondaryHeight * secondaryMaxStretch);
            
            if (newSecondaryHeight <= maxSecondaryHeight) {
                secondaryView.getLayoutParams().height = newSecondaryHeight;
                secondaryView.requestLayout();
            }
        } else {
            // deltaY == 0 - revine la dimensiunile originale
            primaryView.getLayoutParams().height = originalPrimaryHeight;
            primaryView.requestLayout();
            
            secondaryView.getLayoutParams().height = originalSecondaryHeight;
            secondaryView.requestLayout();
        }
    }
    
    /**
     * Animație de revenire la dimensiunile originale
     */
    private void animateBackToOriginalSize() {
        if (primaryView == null || secondaryView == null) return;
        
        // Animație pentru primary view
        final int startPrimaryHeight = primaryView.getHeight();
        ValueAnimator primaryAnimator = ValueAnimator.ofInt(1);
        primaryAnimator.addUpdateListener(animation -> {
            float fraction = animation.getAnimatedFraction();
            Float evaluate = new FloatEvaluator().evaluate(fraction, 
                (float) startPrimaryHeight, (float) originalPrimaryHeight);
            
            primaryView.getLayoutParams().height = evaluate.intValue();
            primaryView.requestLayout();
        });
        primaryAnimator.setDuration(primaryAnimationDuration);
        primaryAnimator.setInterpolator(new DecelerateInterpolator(primaryDecelerationFactor));
        primaryAnimator.start();
        
        // Animație pentru secondary view
        final int startSecondaryHeight = secondaryView.getHeight();
        ValueAnimator secondaryAnimator = ValueAnimator.ofInt(1);
        secondaryAnimator.addUpdateListener(animation -> {
            float fraction = animation.getAnimatedFraction();
            Float evaluate = new FloatEvaluator().evaluate(fraction, 
                (float) startSecondaryHeight, (float) originalSecondaryHeight);
            
            secondaryView.getLayoutParams().height = evaluate.intValue();
            secondaryView.requestLayout();
        });
        secondaryAnimator.setDuration(secondaryAnimationDuration);
        secondaryAnimator.setInterpolator(new DecelerateInterpolator(secondaryDecelerationFactor));
        secondaryAnimator.start();
    }

    // Metode pentru configurare
    
    public UniversalElasticParallax setElasticFactor(float factor) {
        this.elasticFactor = factor;
        return this;
    }
    
    public UniversalElasticParallax setPrimaryStretchLimits(float maxStretch, float minCompress) {
        this.primaryMaxStretch = maxStretch;
        this.primaryMinCompress = minCompress;
        return this;
    }
    
    public UniversalElasticParallax setSecondaryStretchLimits(float maxStretch, float minCompress) {
        this.secondaryMaxStretch = maxStretch;
        this.secondaryMinCompress = minCompress;
        return this;
    }
    
    public UniversalElasticParallax setAnimationDurations(int primaryDuration, int secondaryDuration) {
        this.primaryAnimationDuration = primaryDuration;
        this.secondaryAnimationDuration = secondaryDuration;
        return this;
    }
    
    public UniversalElasticParallax setDecelerationFactors(float primaryFactor, float secondaryFactor) {
        this.primaryDecelerationFactor = primaryFactor;
        this.secondaryDecelerationFactor = secondaryFactor;
        return this;
    }
}
