package com.vgfit.shefit.fragment.premium;

import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.os.Build;
import android.os.Bundle;
import android.text.Html;
import android.text.method.LinkMovementMethod;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.databinding.SubscribeTimerBinding;
import com.vgfit.shefit.fragment.premium.callback.PremiumPurchase;
import com.vgfit.shefit.fragment.premium.callback.Response_RestoreP;
import com.vgfit.shefit.fragment.premium.callback.Return_time;
import com.vgfit.shefit.fragment.workouts.Counter;
import com.vgfit.shefit.fragment.workouts.callbacks.OnFinishListener;
import com.vgfit.shefit.fragment.workouts.callbacks.ReturnTimeAmount;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.Translate;

import java.util.List;
import java.util.Objects;


//public class SubscribeTimer extends Fragment  {
public class SubscribeTimer extends Fragment implements Response_RestoreP, PremiumPurchase, Return_time, OnFinishListener, ReturnTimeAmount {
    private SubscribeTimerBinding binding;
    private RestoreDialog restoreDialog;
    private Counter counter;
    private TimeFormat timeFormat;
    private boolean finishedTime = false;
    private String infoSubs;
    private int savePercent;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getActivity() != null)
            ((MainActivity) getActivity()).premiumPurchase = this;
        restoreDialog = new RestoreDialog();
        timeFormat = new TimeFormat();
        infoSubs = Translate.getValue("subscription_automatically_renews").replace("https://vgfit.com/terms", "<a href=\"https://vgfit.com/terms\">Terms</a>").replace("https://vgfit.com/privacy", "<a href=\"https://vgfit.com/privacy\">Privacy policy</a>");
        infoSubs = infoSubs.replace("http://support.apple.com/kb/ht4098", "<a href=\"http://play.google.com/store/account/subscriptions\">Manage subscriptions.</a>");
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = SubscribeTimerBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setStatusBar(binding.contentSubscribe);
        binding.continueNotNow.setOnClickListener(new OnSingleClickListener() {
            @Override
            public void onSingleClick(View v) {
                toBack();
            }
        });

        binding.continueSubscribe.setOnClickListener(new OnSingleClickListener() {
            @Override
            public void onSingleClick(View v) {
                if (getActivity() != null && MainActivity.readyToPurchase && ((MainActivity) getActivity()).bp != null) {
                    ((MainActivity) getActivity()).bp.subscribe(getActivity(), Constant.SUBSCRIBE_ONE_TIME_WEEK);
                }
            }
        });
        binding.restoreButton.setOnClickListener(new OnSingleClickListener() {
            @Override
            public void onSingleClick(View v) {
                verifyPremium();
            }
        });

        setText();
        setTypeFace();
        setPrice();
    }

    private void setPrice() {
        try {
            if (getActivity() != null) {
                ((MainActivity) getActivity()).bp.getSubscriptionListingDetails(Constant.SUBSCRIBE_ONE_TIME_WEEK, skuDetailOneTime -> {
                    try {

                        setTextThread(binding.priceSmall, String.format("%s/%s", skuDetailOneTime.priceText, Translate.getValue("week")));
                        setTextThread(binding.priceBig, skuDetailOneTime.priceText);
                    } catch (Exception ignored) {
                    }
                    if (getActivity() != null)
                        ((MainActivity) getActivity()).bp.getSubscriptionListingDetails(Constant.SUBSCRIBE_WEEKLY, skuDetailWeekly -> {
                            try {
                                setTextThread(binding.oldPrice, String.format(" (%s/%s)", skuDetailWeekly.priceText, Translate.getValue("week")));
                                double priceSpecial = skuDetailOneTime.priceValue;
                                double priceOld = skuDetailWeekly.priceValue;
                                double pp = priceSpecial / (priceOld / 100);
                                Log.e("TestPrice", "pp==>" + pp + " priceSpecial=>" + priceSpecial + " priceOld==>" + priceOld);
                                savePercent = 100 - (int) pp;
                                Log.d("PriceHHHHH", " skudetails-->" + skuDetailWeekly);
                                Log.d("PriceHHHHH", " price long-->" + skuDetailWeekly.priceLong);
                                Log.d("PriceHHHHH", " price text-->" + skuDetailWeekly.priceText);
                                Log.d("PriceHHHHH", " price value-->" + skuDetailWeekly.priceValue);
                                Log.d("PriceHHHHH", " price currency-->" + skuDetailWeekly.currency);
                                Log.d("PriceHHHHH", " price subscriptionFreeTrialPeriod-->" + skuDetailWeekly.subscriptionFreeTrialPeriod);
                                Log.d("PriceHHHHH", " price haveTrialPeriod-->" + skuDetailWeekly.haveTrialPeriod);
//                                savePercentLabel.setText(savePercent + "%");
                                setTextThread(binding.savePercentLabel, savePercent + "%");
                            } catch (Exception ignored) {
                            }
                        });
                });
            }
        } catch (Exception ignored) {
        }

    }

    private void setTextThread(final TextView text, final String value) {
        if (getActivity() != null)
            getActivity().runOnUiThread(() -> text.setText(value));
    }

    private void setText() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            binding.infoSubscribe.setText(Html.fromHtml(infoSubs.replace(Objects.requireNonNull(System.getProperty("line.separator")), "<br />"), Html.FROM_HTML_MODE_LEGACY));
        } else {
            binding.infoSubscribe.setText(Html.fromHtml(infoSubs));
        }
        binding.infoSubscribe.setMovementMethod(LinkMovementMethod.getInstance());
        binding.restoreButton.setText(Translate.getValue("restore_purchase"));
        binding.saveLabel.setText(Translate.getValue("save_offer"));
        binding.option1.setText(Translate.getValue("only_now_one_time_offer"));
//        option2.setText(Translate.getValue("one_time_offer!"));
        binding.option3.setText(Translate.getValue("join_with_our_best_offer_ever"));
        binding.continueLabel.setText(Translate.getValue("continue"));
        binding.notNowLabel.setText(Translate.getValue("not_now"));
        binding.monthlyPlanLabel.setText(Translate.getValue("weekly_plan"));
        binding.weekLabel.setText(Translate.getValue("week"));

    }

    private void setTypeFace() {

    }

    @Override
    public void onResume() {
        super.onResume();
        if (counter == null)
            setCounter();

        if (finishedTime) {
            try {
                toBack();
            } catch (Exception ignored) {
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (counter != null)
            counter.stop();
    }

    public void verifyPremium() {
        if (getActivity() != null) {
            List<String> my_list = ((MainActivity) getActivity()).bp.listOwnedProducts();
            boolean isPremium = my_list.size() > 0;

            if (!isPremium && !Constant.premium) {
                Constant.setPremium(true);
                Constant.setAdsPremium(false);
            }
        }
        processFinishRestore(true);
    }

    @Override
    public void isPurchased(Boolean output) {

    }

    @Override
    public void processFinishRestore(Boolean output) {
        restoreDialog.DestroyDialog();
    }

    private void setCounter() {
        counter = new Counter();
        counter.setOnTickListener(SubscribeTimer.this, 1);
        counter.setOnFinishListener(SubscribeTimer.this);
        counter.start(60000);
    }

    @Override
    public void onFinish(int indetity) {
        try {
            toBack();
            finishedTime = true;
        } catch (Exception ignored) {
        }

    }


    @Override
    public void my_time(long second, int identy) {
        binding.timerLabel.setText(String.format("00:00:%s", timeFormat.format5(second)));
    }

    private void toBack() {
        FragmentManager fm = getParentFragmentManager();
        if (!fm.isStateSaved())
            fm.popBackStack("frag_upgrade", FragmentManager.POP_BACK_STACK_INCLUSIVE);
    }

    @Override
    public void havingTime(long second, int identy) {
        binding.timerLabel.setText(String.format("00:00:%s", timeFormat.format5(second)));
    }


}
