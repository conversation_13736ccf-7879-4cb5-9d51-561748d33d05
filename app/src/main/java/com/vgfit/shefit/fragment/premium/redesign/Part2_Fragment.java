package com.vgfit.shefit.fragment.premium.redesign;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.vgfit.shefit.databinding.Part2SubscribeBinding;
import com.vgfit.shefit.util.Translate;

import java.util.Arrays;

public class Part2_Fragment extends Fragment {
    private Part2SubscribeBinding binding;
    public static Part2_Fragment newInstance() {

        Bundle args = new Bundle();

        Part2_Fragment fragment = new Part2_Fragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding=Part2SubscribeBinding.inflate(inflater,container,false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        binding.topTitle.setText(Translate.getValue("at_home_workouts_for_all_levels"));
        binding.textView23.setText(Translate.getValueLine("make_sports_a_daily_habit_with", Arrays.asList(2), false));
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

}
