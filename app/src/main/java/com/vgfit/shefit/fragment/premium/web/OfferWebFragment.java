package com.vgfit.shefit.fragment.premium.web;


import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.CapitalizeFirstLetter.capitaliseOnlyFirstLetter;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.vgfit.shefit.databinding.OfferWebFragmentBinding;
import com.vgfit.shefit.fragment.premium.OnSingleClickListener;
import com.vgfit.shefit.util.Translate;

import java.util.Collections;


public class OfferWebFragment extends Fragment {
    private OfferWebFragmentBinding binding;
    private View view;

    public static OfferWebFragment newInstance(boolean isFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", isFirstTime);
        OfferWebFragment fragment = new OfferWebFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        sendAmplitude("[View] Web Offer appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = OfferWebFragmentBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();
        binding.backButton.setOnClickListener(new OnSingleClickListener() {
            @Override
            public void onSingleClick(View v) {
                toBack();
            }
        });
        setText();
    }

    private void toBack() {
        try {
            FragmentManager fm = getParentFragmentManager();
            if (!fm.isStateSaved())
                fm.popBackStack("web_offer", FragmentManager.POP_BACK_STACK_INCLUSIVE);
        } catch (Exception ignored) {
            //ignored
        }
    }


    private void setText() {
        if (getContext() != null) {
            binding.joinMillion.setText(Translate.getValue("get_unlimited_access_to_your_personal_plan"));
            binding.premiumPlansTxt.setText(Translate.getValue("premium_plans:"));
            binding.monthlyTxt.setText(Translate.getValue("weekly"));
            binding.yearlyTxt1.setText(Translate.getValue("weekly"));
            binding.yearlyTxt2.setText(capitaliseOnlyFirstLetter(Translate.getValue("free_trial")));
            binding.threeTxt1.setText(Translate.getValue("monthly"));
            binding.threeTxt2.setText(capitaliseOnlyFirstLetter(Translate.getValue("popular")));
            binding.sixTxt1.setText(Translate.getValue("three_months"));
            binding.sixTxt2.setText(capitaliseOnlyFirstLetter(Translate.getValue("best_value")));
            binding.infoReach.setText(Translate.getValue("to_reach_your_goal,_you’ll_receive_access_to:"));
            String unlimited = Translate.getValue("get_unlimited_access_to") + " ";
            String trainingTxt = Translate.getValue("80+_trainings") + " ";
            String workoutLibrary = unlimited + trainingTxt;
            binding.item1.setText(workoutLibrary);
            binding.item2.setText(Translate.getValue("at_home_workouts_for_all_levels"));
            binding.item3.setText(Translate.getValueLine("workouts_adapted_to_your_fitness_level,_goals_and_sсhedule", Collections.singletonList(5), false));
            String areaPart1 = Translate.getValue("programs_for_specific_areas");
            String areaPart2 = Translate.getValue("80+_step_by_step_instructive_videos_for_every_muscle_group");
            String finalArea = areaPart1 + ". " + areaPart2;
            binding.item4.setText(finalArea);
            String dietP1 = Translate.getValue("lose_weight_with_no_diet");
            String dietP2 = Translate.getValue("150+_easy_and_yummy_recipes_based_on_your_food_preferences");
            String dietFinal = dietP1 + ". " + dietP2;
            binding.item5.setText(dietFinal);
            binding.youCant.setText(Translate.getValue("you_can’t_upgrage_to_premium_in_the_app"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
                toBack();
                return true; // pretend we've processed it
            } else
                return false; // pass on to be processed as normal
        });
    }
}
