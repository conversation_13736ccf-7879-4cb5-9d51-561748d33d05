package com.vgfit.shefit.fragment.userprofile.settings;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class ConverterMeasure {
    private static final float CONST_FT = 2.54f;
    private static final float CONST_CM = 0.3937007874f;
    private static final float CONST_LBS = 2.20462f;

    private ConverterMeasure() {
    }

    public static float getImperialHeight(float valueMetric) {
        String value = BigDecimal.valueOf(valueMetric)
                .multiply(BigDecimal.valueOf(CONST_CM))
                .setScale(8, RoundingMode.HALF_UP)
                .toString();
        return Float.parseFloat(value);
    }

    public static float getMetricHeight(float valueImperial) {
        String value = BigDecimal.valueOf(valueImperial)
                .multiply(BigDecimal.valueOf(CONST_FT))
                .setScale(8, RoundingMode.HALF_UP)
                .toString();
        return Float.parseFloat(value);
    }

    public static float getImperialWeight(float valueMetric) {
        return valueMetric * CONST_LBS;
    }

    public static float getMetricWeight(float valueImperial) {
        return valueImperial / CONST_LBS;
    }

    public static float getCurrentMetricHeight(float value, boolean isImperial) {
        if (isImperial) {
            return getMetricHeight(value);
        }
        return value;
    }
    public static float getCurrentImperialHeight(float value, boolean isImperial) {
        if (isImperial) {
            return getImperialHeight(value);
        }
        return value;
    }

    public static float getCurrentMetricWeight(float value, boolean isImperial) {
        if (isImperial) {
            return getMetricWeight(value);
        }
        return value;
    }
    public static float getCurrentImperialWeight(float value, boolean isImperial) {
        if (isImperial) {
            return getImperialWeight(value);
        }
        return value;
    }
}
