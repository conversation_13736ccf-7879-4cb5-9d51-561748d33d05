package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;
import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.Manifest;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.nabinbhandari.android.permissions.PermissionHandler;
import com.nabinbhandari.android.permissions.Permissions;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.vgfit.shefit.R;
import com.vgfit.shefit.StartScreen;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentNotificationTurnBinding;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.screen.ImageUtils;

import java.util.ArrayList;
import java.util.Arrays;


public class NotificationTurnFragment extends Fragment {
    private FragmentNotificationTurnBinding binding;
    private boolean lunchFirstTime = false;
    private View view;
    private PrefsUtilsWtContext prefsUtilsWtContext;

    public static NotificationTurnFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        NotificationTurnFragment fragment = new NotificationTurnFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        Context context = getContext();
        prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        sendAmplitude("[View] Notification View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentNotificationTurnBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();

        binding.startOnboarding.setOnClickListener(view1 -> pushNotification());
        if (getContext() != null)
            ImageLoader.getInstance().displayImage("assets://onboardingImage/not.jpg", binding.imageView, ImageUtils.getRoundDisplayImageOptions(dpToPx(getContext(), 30)), null);
        String textFirst = Translate.getValue("to_establish_the_habit_and_ensure_you_don’t_miss_your_workout");
        binding.shortDescription.setText(textFirst);
        binding.textView21.setText(Translate.getValueLine("turn_on_notifications", Arrays.asList(2), false));
        binding.startOnboarding.setText(Translate.getValue("allow"));
        binding.dontAllow.setText(Translate.getValue("dont_allow"));
        binding.dontAllow.getPaint().setUnderlineText(true);
        binding.dontAllow.setOnClickListener(v -> {
            toNextFragment(true);
            setVibrate(v);
        });
    }

    private void toNextFragment(boolean isAnimationDown) {
        String email = prefsUtilsWtContext.getStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB);
        Log.d("TestEmail", "Start Shapy email-->" + email);
        if (email != null && !email.isEmpty() && getActivity() != null) {
            if (getActivity() != null) {
                getActivity().onBackPressed();
                ((StartScreen) getActivity()).userLogged();
            }
        } else
            readyPlanFragment(isAnimationDown);
    }


    public void pushNotification() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            String[] permissions = {Manifest.permission.POST_NOTIFICATIONS};
            Permissions.check(getActivity(), permissions, Translate.getValue("allow_geolocation_access")/*rationale*/, null/*options*/, new PermissionHandler() {
                @Override
                public void onGranted() {
                    toNextFragment(false);
                }

                @Override
                public void onDenied(Context context, ArrayList<String> deniedPermissions) {
                    super.onDenied(context, deniedPermissions);
                    toNextFragment(false);
                }
            });
        } else {
            toNextFragment(false);
        }


    }

    private void readyPlanFragment(boolean isAnimationDown) {
        ReadyPlanRedesignFragment fragmentC = ReadyPlanRedesignFragment.newInstance(lunchFirstTime);
        FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
        if (isAnimationDown)
            transaction.setCustomAnimations(0, R.anim.slide_down, R.anim.slide_in_left, R.anim.slide_out_right);
        else
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
        transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_").commitAllowingStateLoss();
    }

    @Override
    public void onResume() {
        super.onResume();
        view.setOnKeyListener((v, keyCode, event) -> {
            return keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN; // pretend we've processed it
        });
    }

}
