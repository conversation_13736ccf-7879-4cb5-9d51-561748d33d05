package com.vgfit.shefit.fragment.workouts;

import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.TextUtils.convertDpToPx;
import static com.vgfit.shefit.util.TextUtils.spToPx;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;
import static com.vgfit.shefit.util.eventCollect.CollectEvent.doneWorkout;
import static com.vgfit.shefit.util.eventCollect.CollectEvent.startWorkout;

import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.github.rubensousa.gravitysnaphelper.GravitySnapHelper;
import com.google.android.exoplayer2.C;
import com.google.android.exoplayer2.offline.DownloadManager;
import com.google.android.exoplayer2.ui.AspectRatioFrameLayout;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultDataSource;
import com.google.android.exoplayer2.upstream.cache.Cache;
import com.google.android.exoplayer2.upstream.cache.CacheDataSink;
import com.google.android.exoplayer2.upstream.cache.CacheDataSource;
import com.google.android.exoplayer2.util.Util;
import com.natasa.progressviews.CircleProgressBar;
import com.vgfit.shefit.BaseApplication;
import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.R;
import com.vgfit.shefit.apprate.rateimplementation.RateImplementation;
import com.vgfit.shefit.apprate.rateimplementation.RateInit;
import com.vgfit.shefit.apprate.rateimplementation.callback.FinishingRate;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentWorkoutsVideoBinding;
import com.vgfit.shefit.fragment.premium.OnSingleClickListener;
import com.vgfit.shefit.fragment.workouts.adapter.AdapterExerciseVideo;
import com.vgfit.shefit.fragment.workouts.callbacks.ExerciseItemClick;
import com.vgfit.shefit.fragment.workouts.callbacks.OnFinishListener;
import com.vgfit.shefit.fragment.workouts.callbacks.ReturnTimeAmount;
import com.vgfit.shefit.fragment.workouts.helpers.WorkoutProgressHelper;
import com.vgfit.shefit.fragment.workouts.model.ItemExercise;
import com.vgfit.shefit.fragment.workouts.player.MainPlayer;
import com.vgfit.shefit.fragment.workouts.player.NextPlayer;
import com.vgfit.shefit.fragment.workouts.progressbar.ProgressDrawable;
import com.vgfit.shefit.fragment.workouts.service.NextViewAnimation;
import com.vgfit.shefit.realm.Achievement;
import com.vgfit.shefit.realm.WorkoutExercise;
import com.vgfit.shefit.util.AchievementDialogHelper;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.WorkoutBackPressHelper;
import com.vgfit.shefit.util.eventCollect.CollectEvent;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;

import io.realm.Realm;

public class WorkoutsVideoFr extends Fragment implements OnFinishListener, ReturnTimeAmount, ExerciseItemClick, FinishingRate, WorkoutBackPressHelper.WorkoutBackPressListener {
    private FragmentWorkoutsVideoBinding binding;
    private static final String KEY_PLAY_WHEN_READY = "play_when_ready";
    private static final String KEY_WINDOW = "window";
    private static final String KEY_POSITION = "position";
    ArrayList<ItemExercise> list;
    ArrayList<Long> intervalList;
    AdapterExerciseVideo adapter;
    RecyclerView recyclerView;
    String oldTextNameOrSeconds;
    ProgressBar progressBar;
    List<WorkoutExercise> workoutExerciseList;


    HashMap<String, String> map;
    private int identifier = Identity.PREPARE;
    private int position;
    private int secondsPreview = 0;
    private int secondsRest = 0;
    private int secondsWork = 0;
    private View view;
    private float progressRound;
    private boolean isPlaying = true, isNameVideo = false;
    private int secPrevious = 0;
    private boolean previewGo = false;
    private Counter counter;
    private CircleProgressBar progressBarCircle;
    private int secondsProgressRound, currentWindow, lastIdentifier, lastPosition;
    private long timerSecond;
    private boolean isShowingPreviewNext;
    private TextToSpeech ttobj;
    private MediaPlayer clockTickPlayer;
    private NextViewAnimation nextViewAnimation;
    private NextPlayer nextPlayer;
    private MainPlayer mainPlayer;
    private int screenWidth;
    private boolean isBadInternet;
    private String languageTranslation = "";
    private PrefsUtilsWtContext prefsUtilsWtContext;

    // Helper for managing back press and achievements
    private WorkoutBackPressHelper backPressHelper;
    private Typeface typeFaceHelveticaBold;
    private boolean isClickable = true;
    private DataSource.Factory dataSourceFactory;
    private String idWorkout;
    private LinearLayoutManager layoutManager;
    private Set<Integer> completedExercises = new HashSet<>();
    private String typeWorkout;
    private Long currentTimeLong;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        dataSourceFactory = createDataSource(getContext());
        workoutExerciseList = new ArrayList<>();
        Bundle bundle = this.getArguments();
        if (bundle != null) {
            workoutExerciseList = bundle.getParcelableArrayList("workout");
            idWorkout = bundle.getString("idWorkout", "");
            typeWorkout = bundle.getString("typeWorkout", "");
            currentTimeLong = bundle.getLong("timeLong", System.currentTimeMillis());
            String nameWorkout = bundle.getString("nameWorkout", "");
            String levelWorkout = bundle.getString("level", "");

            isBadInternet = Constant.isBadInternet;
            WorkoutProgressHelper.initWorkoutHistoryAsync(idWorkout, typeWorkout, nameWorkout, new Date(currentTimeLong), levelWorkout);
        }
        languageTranslation = Translate.getLanguage();
        list = new ArrayList<>(prepareList());

        // Set workout data in back press helper after list is initialized
        if (backPressHelper != null) {
            backPressHelper.setWorkoutData(idWorkout, typeWorkout, completedExercises, list, prefsUtilsWtContext);
        }
        intervalList = getIntervals(list);
        Constant.initArrayLang();
        map = new HashMap<>();
        map.put(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, "7456");

        ttobj = new TextToSpeech(getActivity(), status -> {
            if (status != TextToSpeech.ERROR) {
                if (!Constant.getLanguageTTSexist(ttobj)) {
                    if (!Constant.aviableEnglishTTS(ttobj)) {
                        ttobj = null;
                    } else {
                        ttobj.setLanguage(new Locale(languageTranslation));
                    }
                } else {
                    ttobj.setLanguage(Locale.getDefault());
                    //maybe u need to set language here
                }
            } else {
                ttobj = null;
            }
        });
        ttobj.setOnUtteranceProgressListener(new UtteranceProgressListener() {
            @Override
            public void onStart(String utteranceId) {

            }

            @Override
            public void onDone(String utteranceId) {
            }

            @Override
            public void onError(String utteranceId) {

            }
        });
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        if (getActivity() != null)
            typeFaceHelveticaBold = Typeface.createFromAsset(getActivity().getAssets(), "fonts/helvetica_bold_font.ttf");
        startWorkout(prefsUtilsWtContext);
        sendAmplitude("[View] Workout Started");
        isTablet();
        if (backPressHelper != null) {
            backPressHelper.preventFragmentClosing();
        }
    }

    private ArrayList<ItemExercise> prepareList() {
        ArrayList<ItemExercise> list = new ArrayList<>();
        ItemExercise itemExercise;
        String nameVideo;
        String nameVideoHD;
        String nameVideoLowQ;
        String playlist;
        if (workoutExerciseList != null)
            for (int i = 0; i < workoutExerciseList.size(); i++) {
                nameVideo = workoutExerciseList.get(i).getExercise().getVideo();
                nameVideoHD = workoutExerciseList.get(i).getExercise().getVideoHD();
                nameVideoLowQ = workoutExerciseList.get(i).getExercise().getVideoLowQ();
                playlist = workoutExerciseList.get(i).getExercise().getPlaylist();

                itemExercise = new ItemExercise();
                itemExercise.setExerciseTime(workoutExerciseList.get(i).getDuration() * 1000L);
                itemExercise.setImage(workoutExerciseList.get(i).getExercise().getImage());
                itemExercise.setNameExercise(Translate.getValue(workoutExerciseList.get(i).getExercise().getName()));
                itemExercise.setNameVideo(nameVideo);
                itemExercise.setNameVideoHD(nameVideoHD);
                itemExercise.setNameVideoLowQ(nameVideoLowQ);
                itemExercise.setPlaylist(playlist);
                if (i < workoutExerciseList.size() - 1)
                    itemExercise.setRestTime(workoutExerciseList.get(i).getRestTime() * 1000L);
                else itemExercise.setRestTime(0);
                list.add(i, itemExercise);
            }
        return list;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public View onCreateView(@NonNull final LayoutInflater inflater, final ViewGroup container, Bundle savedInstanceState) {

        binding = FragmentWorkoutsVideoBinding.inflate(inflater, container, false);
        view = binding.getRoot();
        createYourOwnProgressBar();
        position = 0;
        isShowingPreviewNext = false;
        progressBarCircle = view.findViewById(R.id.circle_progress);
        progressBarCircle.setStartPositionInDegrees(-90);
        progressBarCircle.setRoundEdgeProgress(true);
        progressBarCircle.setProgressColor(getResources().getColor(R.color.white));
        if (typeFaceHelveticaBold != null)
            progressBarCircle.setTypeFace(typeFaceHelveticaBold);
        if (workoutExerciseList != null && workoutExerciseList.get(0) != null)
            progressBarCircle.setText(String.valueOf(workoutExerciseList.get(0).getDuration()));
        if (getContext() != null)
            progressBarCircle.setTextSize(spToPx(48, getContext()));
        nextViewAnimation = new NextViewAnimation(getContext());
        nextViewAnimation.countMetrics();
        nextPlayer = new NextPlayer(getContext(), list, binding.playerViewNext, isBadInternet);
        mainPlayer = new MainPlayer(getContext(), list, binding.playerView);
        mainPlayer.setBindingView(binding);

        recyclerView = view.findViewById(R.id.recyclerView_Exercises);
        recyclerView.setHasFixedSize(true);
        layoutManager = new LinearLayoutManager(getContext());
        recyclerView.setLayoutManager(layoutManager);
        adapter = new AdapterExerciseVideo(list, getContext(), this);
        recyclerView.setAdapter(adapter);
        GravitySnapHelper snapHelper = new GravitySnapHelper(Gravity.TOP); // or START if horizontal
        snapHelper.attachToRecyclerView(recyclerView);

        adapter.setPositionSelected(position);
        if (counter == null) setCounter(countTimeForName(), identifier);

        binding.btnStartStop.setOnClickListener(new OnSingleClickListener() {
            @Override
            public void onSingleClick(View v) {
                setVibrate(view);
                startOrStop();
            }
        });

        binding.btnPrevious.setOnClickListener(this::goToPreviousExercise);

        binding.btnNext.setOnClickListener(this::goToNextExercise);

        binding.restContainer.setOnClickListener(this::goToNextExercise);

        binding.btnGoBack.setOnClickListener(lll -> {
            setVibrate(lll);
            Log.d("TestBackPress", "btnGoBack clicked");
            if (backPressHelper != null) {
                backPressHelper.triggerBackPress();
            }
        });
        binding.btnGoBackOverlay.setOnClickListener(lll -> {
            setVibrate(lll);
            Log.d("TestBackPress", "btnGoBackOverlay clicked");
            if (backPressHelper != null) {
                backPressHelper.triggerBackPress();
            }
        });

        isFirstOrLast();

        binding.btnDone.setOnClickListener(v -> {
            setVibrate(v);
            if (new RateInit(getContext()).isValidShowRate())
                new RateImplementation(getContext(), getActivity()).setRateApp();
            Log.d("TestBackPress", "btnDone clicked");
            if (backPressHelper != null) {
                backPressHelper.triggerBackPress();
            }
        });

        binding.rlNutritionVideo.setOnClickListener(new OnSingleClickListener() {
            @Override
            public void onSingleClick(View v) {
                setVibrate(v);
                if (binding.rlDone.getVisibility() == View.GONE) startOrStop();
            }
        });

        binding.rlNextVideoPreview.setOnClickListener(v -> {
            setVibrate(v);
            if (binding.rlOverlay.getVisibility() == View.GONE) goToNextExercise(v);
            else startOrStop();
        });

        if (mainPlayer.getPlayer() == null) {
            mainPlayer.initPlayer(savedInstanceState, position, dataSourceFactory);
            nextPlayer.initPlayer(savedInstanceState, dataSourceFactory);
        }

        binding.playerView.setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_ZOOM);
        binding.workPaused.setText(Translate.getValue("workout_paused"));
        binding.tvWorkoutDone.setText(Translate.getValue("workout_done"));
        binding.textDone.setText(Translate.getValue("close"));
        String restTT = Translate.getValue("rest") + " ";
        binding.restName.setText(restTT);
        String nextTxt = Translate.getValue("next") + ":";
        binding.nextInfo.setText(nextTxt);
        return view;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        Log.d("TestBackPress", "onAttach");

        // Initialize back press helper
        backPressHelper = new WorkoutBackPressHelper(this, this);
    }

    // Implementation of WorkoutBackPressListener interface
    @Override
    public void onWorkoutPaused() {
        pauseWorkout();
    }

    @Override
    public void onFragmentShouldClose() {
        if (getActivity() != null) {
            getActivity().onBackPressed();
        }
    }

    @Override
    public Context getFragmentContext() {
        return getContext();
    }

    @Override
    public void onShowAchievementCelebration(Achievement achievement) {
        showAchievementCelebrationLayout(achievement);
    }


    private void goToPreviousExercise(View v) {
        if (getAvailablePosition(position - 1)) {
            setVibrate(v);
            position--;
            changeExercise();
            if (mainPlayer != null) mainPlayer.getPlayer().seekTo(position, C.TIME_UNSET);
        }
    }

    private void goToNextExercise(View v) {
        if (getAvailablePosition(position + 1)) {
            setVibrate(v);
            position++;
            changeExercise();
            if (mainPlayer.getPlayer() != null)
                mainPlayer.getPlayer().seekTo(position, C.TIME_UNSET);
        }
    }

    private boolean isTablet() {
        DisplayMetrics metrics = new DisplayMetrics();
        if (getActivity() != null)
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(metrics);
        screenWidth = metrics.widthPixels;
        float yInches = metrics.heightPixels / metrics.ydpi;
        float xInches = screenWidth / metrics.xdpi;
        return Math.sqrt(xInches * xInches + yInches * yInches) >= 6.5;
    }

    private void createYourOwnProgressBar() {
        binding.llHorizontalProgress.setOrientation(LinearLayout.VERTICAL);

        progressBar = new ProgressBar(getContext(), null, android.R.attr.progressBarStyleHorizontal);
        ProgressDrawable d = new ProgressDrawable(getResources().getColor(R.color.white), getResources().getColor(R.color.bg_color_), getResources().getColor(R.color.another_color));
        d.setNumberOfSegments(list.size());

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, convertDpToPx(getContext(), 40));
        progressBar.setLayoutParams(params);
        progressBar.setProgressDrawable(d);
        progressBar.setMax(1000);
        progressBar.setPadding(0, 20, 0, 0);
        binding.llHorizontalProgress.addView(progressBar);
    }

    private ArrayList<Long> getIntervals(ArrayList<ItemExercise> list) {
        ArrayList<Long> intervals = new ArrayList<>();
        intervals.add((long) 0);
        long previous = 0;
        for (int i = 0; i < list.size(); i++) {
            previous = previous + list.get(i).getExerciseTime();
            intervals.add(previous);
        }
        return intervals;
    }

    private void startOrStop() {
        if (!isPlaying) {
            playClockTicking(true);
            setCounter(timerSecond, identifier);
            speakText(Translate.getValue("resuming_workout"));
            isClickable = false;
        } else {
            playClockTicking(false);
            counter.stop();
            speakText(Translate.getValue("pausing_workout"));
            isClickable = true;
        }
        isPlaying = !isPlaying;
        setStatusPlay(isPlaying);
        if (mainPlayer.getPlayer() != null) mainPlayer.getPlayer().setPlayWhenReady(isPlaying);
    }

    private void pauseWorkout() {
        if (isPlaying) {
            playClockTicking(false);
            counter.stop();
            isPlaying = false;
            if (mainPlayer.getPlayer() != null) mainPlayer.getPlayer().setPlayWhenReady(false);
        }
    }

    private void speakText(String text) {
        try {
            ttobj.speak(text, TextToSpeech.QUEUE_FLUSH, map);
        } catch (Exception ignored) {
        }
    }

    private void changeExercise() {
        counter.stop();
        resetProgressData();
        isFirstOrLast();
        identifier = Identity.PREPARE;
        setCounter(countTimeForName(), identifier);
        progressBar.setProgress(getProgressSegmented());
        if (mainPlayer != null && mainPlayer.getPlayer() != null)
            mainPlayer.getPlayer().setPlayWhenReady(isPlaying);
        adapter.setPositionSelected(position);
        secPrevious = 0;
        nextViewAnimation.getViewScaleAnimatorSmaller(binding.rlNextVideoPreview, binding.maskBackground, binding.textContainer).setDuration(0).start();
        isShowingPreviewNext = false;
    }

    private void isFirstOrLast() {
        if (position == 0) {
            binding.btnPrevious.setAlpha(0.5f);
            binding.btnNext.setAlpha(1f);
        } else if (position == workoutExerciseList.size() - 1) {
            binding.btnPrevious.setAlpha(1f);
            binding.btnNext.setAlpha(0.5f);
        } else {
            binding.btnNext.setAlpha(1f);
            binding.btnPrevious.setAlpha(1f);
        }
    }

    private void resetProgressData() {
        progressBarCircle.setText("0");
        progressRound = 0;
        progressBarCircle.setProgress(progressRound);
        binding.tvSeconds.setVisibility(View.VISIBLE);
        binding.tvSeconds.setText("");
        binding.restContainer.setVisibility(View.GONE);
        binding.secondsContainer.setVisibility(View.VISIBLE);
        binding.tvNameExercise.setVisibility(View.GONE);
        secPrevious = -1;
        isPlaying = true;
        setStatusPlay(true);
        playClockTicking(false);
        progressBarCircle.setVisibility(View.VISIBLE);
    }

    private void setStatusPlay(boolean isPlaying) {
        if (isPlaying) {
            binding.rlOverlay.setVisibility(View.GONE);
            if (isShowingPreviewNext) nextPlayer.startPreviewVideo(position);
        } else {
            binding.rlOverlay.setVisibility(View.VISIBLE);
            recyclerView.scrollToPosition(adapter.getPositionSelected());
            if (isShowingPreviewNext) nextPlayer.stopPreviewVideo();
        }
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
    }

    @Override
    public void onFinish(int identity) {
        switch (identity) {
            case Identity.PREPARE:
                prepareOnFinish();
                break;
            case Identity.WORK:
                workOnFinish();
                break;
            case Identity.REST:
                restOnFinish();
                break;
        }
    }

    private void prepareOnFinish() {
        identifier = Identity.WORK;
        setCounter(list.get(position).getExerciseTime(), identifier);
        binding.tvSeconds.setVisibility(View.GONE);
        binding.secondsContainer.setVisibility(View.GONE);
        previewGo = false;
        binding.tvSeconds.setTextSize(250);
    }

    private void workOnFinish() {
        completedExercises.add(position);
        registerProgressWorkout();
        identifier = Identity.REST;
        if (list.get(position).getRestTime() == 0 && (position + 1 < list.size())) {
            goToNextExercise(binding.rlNextVideoPreview);
            return;
        }
        setCounter(list.get(position).getRestTime(), identifier);
        resetProgressData();
        if (position < list.size() - 1) playClockTicking(true);
    }

    private void restOnFinish() {
        if (getAvailablePosition(position + 1)) {
            identifier = Identity.PREPARE;
            position++;
            setCounter(countTimeForName(), identifier);
            adapter.setPositionSelected(position);
            if (mainPlayer.getPlayer() != null)
                mainPlayer.getPlayer().seekTo(position, C.TIME_UNSET);
            binding.restContainer.setVisibility(View.GONE);
        } else {
            doneWorkout(prefsUtilsWtContext);
            binding.rlDone.setVisibility(View.VISIBLE);
            Log.d("TestSuperset", "Save idWorkout--->" + idWorkout);
            prefsUtilsWtContext.setBooleanPreferenceProfile(idWorkout, true);
            speakText(Translate.getValue("workout_done") + "!");
            prefsUtilsWtContext.setBooleanPreferenceProfile(Constant.KEY_FINISHED_WORKOUT, true);
            if (mainPlayer != null && mainPlayer.getPlayer() != null)
                mainPlayer.getPlayer().release();
            ((MainActivity) getActivity()).statusTest(true);
        }
        binding.tvSeconds.setText("");
        playClockTicking(false);
    }

    private long countTimeForName() {
        if (list.size() > position && list.get(position) != null && list.get(position).getNameExercise().length() > 0) {
            long timeName = list.get(position).getNameExercise().length() * list.get(position).getTimeSymbol();
            return timeName + list.get(position).getPreviewTime();
        } else return 0;
    }

    private void setCounter(long milliseconds, int identifier) {
        counter = new Counter();
        counter.setOnTickListener(this, identifier);
        counter.setOnFinishListener(this);
        counter.start(milliseconds);
    }

    @Override
    public void havingTime(long second, int identifier) {
        switch (identifier) {
            case Identity.PREPARE:
                timerSecond = second;
                setPreviewActions(second);
                break;
            case Identity.WORK:
                timerSecond = second;
                setProgressRound(second);
                setProgressHorizontal();
                setWorkActions(second);
                break;
            case Identity.REST:
                timerSecond = second;
                setRestActions(second);
                break;
        }
    }

    private void setPreviewActions(long milliseconds) {
        secondsPreview = (int) (milliseconds / 1000);
        if (milliseconds < 1000 && !previewGo) {
            secondsAnimator(Translate.getValue("go").toUpperCase());
            speakText(Translate.getValue("go") + "!");
            previewGo = true;
        } else if (milliseconds > 1001 && milliseconds < 3999 && secPrevious != secondsPreview) {
            secPrevious = secondsPreview;
            binding.tvNameExercise.setVisibility(View.GONE);
            secondsAnimator("" + secondsPreview);
            speakText("" + secondsPreview);
        } else if (milliseconds > 5000 && milliseconds < countTimeForName() - 1000 && secPrevious != -1) {
            secPrevious = -1;
            speakText(list.get(position).getNameExercise());
            binding.tvNameExercise.setVisibility(View.VISIBLE);
            String name = list.get(position).getNameExercise() + " ";
            binding.tvNameExercise.setText(name);
        } else {
            if (progressBarCircle.getVisibility() != View.VISIBLE) {
                progressBarCircle.setVisibility(View.VISIBLE);
            }
            progressBarCircle.setText(String.valueOf(workoutExerciseList.get(position).getDuration()));
        }
    }

    private void setWorkActions(long milliseconds) {
        secondsWork = (int) (milliseconds / 1000);
        if (list.get(position).getExerciseTime() / 2000 == secondsWork && secPrevious != secondsWork) {
            secPrevious = secondsWork;
        } else if (secondsWork == 10 && secPrevious != secondsWork) {
            secPrevious = secondsWork;
            speakText(Translate.getValue("10_more_seconds_key") + "!");
        } else if (secondsWork == 6 && position < list.size() - 1 && !isShowingPreviewNext) {
            isShowingPreviewNext = true;
            nextViewAnimation.getViewScaleAnimatorBigger(binding.rlNextVideoPreview, binding.maskBackground, binding.textContainer).setDuration(1000).start();
            binding.tvNextExercise.setText(list.get(position + 1).getNameExercise());
            nextPlayer.startPreviewVideo(position);
        } else if (secondsWork < 6 && secondsWork > 0 && secPrevious != secondsWork) {
            secPrevious = secondsWork;
            speakText("" + secondsWork);
        } else if (secondsWork == 0 && secPrevious != secondsWork) {
            secPrevious = secondsWork;
            if (isShowingPreviewNext) {
                nextPlayer.stopPreviewVideo();
                nextViewAnimation.getViewScaleAnimatorSmaller(binding.rlNextVideoPreview, binding.maskBackground, binding.textContainer).setDuration(1000).start();
                isShowingPreviewNext = false;
            }
        }
    }

    private void setRestActions(long milliseconds) {
        if (binding.restContainer.getVisibility() != View.VISIBLE) {
            binding.restContainer.setVisibility(View.VISIBLE);
            progressBarCircle.setVisibility(View.INVISIBLE);
        }
        secondsRest = (int) (milliseconds / 1000);
        if (secPrevious != secondsRest) {
            secPrevious = secondsRest;
            String secFinal = String.valueOf(secondsRest);
            binding.secondsRest.setText(secFinal);
        }
    }

    private boolean getAvailablePosition(int position) {
        return position >= 0 && position < list.size();
    }

    public void setProgressRound(long milliseconds) {
        if (progressBarCircle.getVisibility() != View.VISIBLE) {
            progressBarCircle.setVisibility(View.VISIBLE);
        }
        progressRound = (1000 - (milliseconds / (list.get(position).getExerciseTime() / 1000)));
        if (identifier == Identity.WORK) {
            progressBarCircle.setProgress(progressRound);
            secondsProgressRound = (int) (milliseconds / 1000);
            progressBarCircle.setText("" + secondsProgressRound);
        } else {
            progressBarCircle.setText(String.valueOf(workoutExerciseList.get(position).getDuration()));
            progressBarCircle.setProgress(0);
        }
    }

    public void setProgressHorizontal() {
        if (identifier == Identity.REST || identifier == Identity.WORK)
            progressBar.setProgress(getProgressSegmented());
    }

    private int getProgressSegmented() {
        int listSize = workoutExerciseList.size();
        int percentAmountForOne = 1000 / listSize;
        int positionDonePercent = percentAmountForOne * position;
        return (int) (positionDonePercent + (progressRound / listSize));
    }

    public void secondsAnimator(String text) {
        ValueAnimator valueAnimator = ValueAnimator.ofFloat(92, 750, 92);
        valueAnimator.setDuration(600);
        valueAnimator.addUpdateListener(animation -> binding.tvSeconds.setTextSize((Float) valueAnimator.getAnimatedValue()));
        binding.tvSeconds.setText(text);
        valueAnimator.start();
    }

    public void restorePlayer() {
        try {
            if (mainPlayer.getPlayer() != null) {
                adapter.setPositionSelected(position);
                mainPlayer.getPlayer().seekTo(position, C.TIME_UNSET);
                mainPlayer.getPlayer().setPlayWhenReady(isPlaying);
            }

            if (nextPlayer.getPlayer() != null) {
                nextPlayer.getPlayer().seekTo(position + 1, C.TIME_UNSET);
                nextPlayer.getPlayer().setPlayWhenReady(isPlaying);
            }
        } catch (Exception ignored) {
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        if (Util.SDK_INT > 23) {
            mainPlayer.initializePlayer();
            nextPlayer.initializePlayer();
        }
        if (counter != null && timerSecond != 0) {
            identifier = lastIdentifier;
            position = lastPosition;
            setCounter(timerSecond, identifier);
            if (position == list.size() - 1 && identifier == Identity.REST)
                binding.rlDone.setVisibility(View.VISIBLE);
            else setStatusPlay(isPlaying);
            setProgressRound(timerSecond);
            setProgressHorizontal();
            if (nextPlayer.getPlayer() == null) nextPlayer.initializePlayer();
            restorePlayer();
            if (identifier == Identity.PREPARE || identifier == Identity.REST) {
                if (isNameVideo) binding.tvNameExercise.setText(oldTextNameOrSeconds);
                else binding.tvSeconds.setText(oldTextNameOrSeconds);
            }
            counter.stop();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if ((Util.SDK_INT <= 23 || mainPlayer.getPlayer() == null)) {
            mainPlayer.initializePlayer();
            nextPlayer.initializePlayer();
            restorePlayer();
        }
        isClickable = true;
        resumeAllDownload();

        // Add back press handling
        if (view != null) {
            view.setFocusableInTouchMode(true);
            view.requestFocus();
            view.setOnKeyListener((v, keyCode, event) -> {
                if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
                    Log.d("TestBackPress", "Back pressed detected via KeyListener");
                    if (backPressHelper != null) {
                        backPressHelper.triggerBackPress();
                    }
                    return true; // pretend we've processed it
                } else {
                    return false; // pass on to be processed as normal
                }
            });
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (Util.SDK_INT <= 23) {
            mainPlayer.releasePlayer();
            nextPlayer.releasePlayer();
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        registerProgressWorkout();
        if (Util.SDK_INT > 23) {
            mainPlayer.releasePlayer();
            nextPlayer.releasePlayer();
        }
        lastIdentifier = identifier;
        lastPosition = position;
        if (counter != null) counter.stop();
        isPlaying = false;
        playClockTicking(false);
        if (ttobj != null)
            ttobj.stop();
        if (binding.tvNameExercise.getVisibility() == View.GONE || identifier == Identity.REST) {
            oldTextNameOrSeconds = binding.tvSeconds.getText().toString();
            isNameVideo = false;
        } else {
            oldTextNameOrSeconds = binding.tvNameExercise.getText().toString();
            isNameVideo = true;
        }

    }


    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        if (mainPlayer != null)
            mainPlayer.updateStartPosition();
        outState.putBoolean(KEY_PLAY_WHEN_READY, true);
        outState.putInt(KEY_WINDOW, currentWindow);
        outState.putLong(KEY_POSITION, C.TIME_UNSET);
        super.onSaveInstanceState(outState);
    }

    private void beepSound() {
        if (getContext() != null) {
            if (clockTickPlayer != null) clockTickPlayer.reset();
            clockTickPlayer = MediaPlayer.create(getContext(), R.raw.beep_sound);
            clockTickPlayer.start();
        }
    }

    private void playClockTicking(boolean shouldPlay) {
        if (shouldPlay && identifier == Identity.REST) {
            if (clockTickPlayer != null) clockTickPlayer.reset();
            clockTickPlayer = MediaPlayer.create(getContext(), R.raw.clock_tick);
            clockTickPlayer.start();
        } else if (clockTickPlayer != null && clockTickPlayer.isPlaying()) clockTickPlayer.stop();
    }

    @Override
    public void onItemClick(int pos) {
        if (isClickable)
            if (!binding.btnNext.isPressed() && !binding.btnPrevious.isPressed()) {
                isClickable = false;
                resetProgressData();
                position = pos;
                changeExercise();
                currentWindow = pos;
                if (mainPlayer != null && mainPlayer.getPlayer() != null)
                    mainPlayer.getPlayer().seekTo(currentWindow, C.TIME_UNSET);
            }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // Cleanup all resources to prevent memory leaks and background activities
        cleanupAllResources();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        pauseAllDownload();
        registerProgressWorkout();

        // Cleanup back press helper
        if (backPressHelper != null) {
            backPressHelper.cleanup();
        }

        // Final cleanup
        cleanupAllResources();

        counter = null;
        lastIdentifier = 0;
        lastPosition = 0;
        oldTextNameOrSeconds = "";
        isShowingPreviewNext = false;
    }

    private void registerProgressWorkout() {
        try (Realm realm = Realm.getDefaultInstance()) {
            realm.executeTransactionAsync(bgRealm -> WorkoutProgressHelper.saveOrUpdateWorkoutHistory(
                    bgRealm, idWorkout, typeWorkout, completedExercises
            ), () -> {
                Log.d("WorkoutProgress", "Workout progress saved successfully");
                updatePreferences();
            }, error -> Log.e("WorkoutProgress", "Error saving workout progress: " + error.getMessage()));
        }
    }

    private void updatePreferences() {
        int listSize = list.size();
        int completedCount = completedExercises.size();
        float percentAmountForOne = (float) 100 / listSize;
        int realProgressPercent = (int) (percentAmountForOne * completedCount);

        int recentProgress = prefsUtilsWtContext.getIntegerPreferenceProfile(idWorkout + "_progress", 0);
        if (realProgressPercent > recentProgress) {
            prefsUtilsWtContext.setIntegerPreferenceProfile(idWorkout + "_progress", realProgressPercent);
        }
        prefsUtilsWtContext.setStringPreferenceProfile(idWorkout + "_completed_exercises",
                TextUtils.join(",", completedExercises));

        if (completedCount == listSize) {
            prefsUtilsWtContext.setBooleanPreferenceProfile(idWorkout + "_completed", true);
            CollectEvent.doneWorkout(prefsUtilsWtContext);
        }
    }

    private void pauseAllDownload() {
        DownloadManager downloadManager = ((BaseApplication) requireContext().getApplicationContext()).getDownloadManager();
        downloadManager.pauseDownloads();
        if (mainPlayer != null)
            mainPlayer.getDownloadHLS().cancelDownloadNotification();
    }

    private void resumeAllDownload() {
        DownloadManager downloadManager = ((BaseApplication) requireContext().getApplicationContext()).getDownloadManager();
        downloadManager.resumeDownloads();
    }


    @Override
    public void finishRate() {
        //not use
    }

    /**
     * Cleanup all resources to prevent memory leaks and background activities
     */
    private void cleanupAllResources() {
        try {
            // 1. Stop and cleanup counter/timer
            if (counter != null) {
                counter.stop();
                counter = null;
            }

            // 2. Stop and cleanup MediaPlayer for clock ticking
            playClockTicking(false); // Stop clock ticking through existing method
            if (clockTickPlayer != null) {
                try {
                    if (clockTickPlayer.isPlaying()) {
                        clockTickPlayer.stop();
                    }
                    clockTickPlayer.reset();
                    clockTickPlayer.release();
                } catch (Exception e) {
                    // Silent cleanup
                }
                clockTickPlayer = null;
            }

            // 3. Stop and cleanup TextToSpeech
            if (ttobj != null) {
                try {
                    ttobj.stop();
                    ttobj.shutdown();
                } catch (Exception e) {
                    // Silent cleanup
                }
                ttobj = null;
            }

            // 4. Cleanup ExoPlayer instances
            if (mainPlayer != null) {
                try {
                    mainPlayer.releasePlayer();
                } catch (Exception e) {
                    // Silent cleanup
                }
                mainPlayer = null;
            }

            if (nextPlayer != null) {
                try {
                    nextPlayer.releasePlayer();
                } catch (Exception e) {
                    // Silent cleanup
                }
                nextPlayer = null;
            }

            // 5. Cleanup animations and view animations
            if (nextViewAnimation != null) {
                // Stop any running animations
                nextViewAnimation = null;
            }

            // 6. Reset flags and state
            isPlaying = false;
            isClickable = true;
            isShowingPreviewNext = false;
            previewGo = false;
            timerSecond = 0;

            // 7. Cleanup binding references to prevent memory leaks
            if (binding != null) {
                // Clear listeners to prevent memory leaks
                binding.btnStartStop.setOnClickListener(null);
                binding.btnPrevious.setOnClickListener(null);
                binding.btnNext.setOnClickListener(null);
                binding.rlNutritionVideo.setOnClickListener(null);
                binding.rlNextVideoPreview.setOnClickListener(null);
            }

        } catch (Exception e) {
            // Silent cleanup
        }
    }

    private DataSource.Factory createDataSource(Context context) {
        Cache cache = BaseApplication.getCashExoplayer(context);
        DataSource.Factory upstreamFactory = new DefaultDataSource.Factory(context);
        CacheDataSink.Factory cacheWriteDataSinkFactory = new CacheDataSink.Factory().setCache(cache).setFragmentSize(C.LENGTH_UNSET);
        return new CacheDataSource.Factory().setCache(cache).setUpstreamDataSourceFactory(upstreamFactory).setCacheWriteDataSinkFactory(cacheWriteDataSinkFactory).setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR);
    }

    /**
     * Show achievement celebration layout
     */
    private void showAchievementCelebrationLayout(Achievement achievement) {
        try {
            Log.d("AchievementCelebration", "Showing celebration layout for: " + achievement.getBadgeNameKey());

            // Inflate the celebration layout
            View celebrationView = LayoutInflater.from(getContext()).inflate(R.layout.layout_achievement_celebration, null);

            // Set up the celebration layout
            setupCelebrationLayout(celebrationView, achievement);

            // Add to the main container (assuming there's a main container in the fragment)
            if (binding != null && binding.getRoot() instanceof ViewGroup) {
                ViewGroup mainContainer = (ViewGroup) binding.getRoot();
                celebrationView.setLayoutParams(new ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT));
                mainContainer.addView(celebrationView);
            }

        } catch (Exception e) {
            Log.e("AchievementCelebration", "Error showing celebration layout: " + e.getMessage());
            // If there's an error, just close the fragment
            if (getActivity() != null) {
                getActivity().onBackPressed();
            }
        }
    }

    /**
     * Setup celebration layout content and listeners
     */
    private void setupCelebrationLayout(View celebrationView, Achievement achievement) {
        // Get views from layout
        TextView caloriesNumber = celebrationView.findViewById(R.id.tv_calories_number);
        TextView youBurned = celebrationView.findViewById(R.id.tv_you_burned);
        TextView viewAchievementText = celebrationView.findViewById(R.id.btn_view_achievement_text);
        TextView achievementMessage = celebrationView.findViewById(R.id.tv_achievement_message);
        TextView kcal = celebrationView.findViewById(R.id.tv_kcal);
        TextView tvComparison = celebrationView.findViewById(R.id.tv_comparison);
        LinearLayout viewAchievementButton = celebrationView.findViewById(R.id.btn_view_achievement);
        ImageView backgroundCelebration = celebrationView.findViewById(R.id.backgroundCelebration);
        TextView doneButton = celebrationView.findViewById(R.id.btn_done_celebration);
        ImageView achievementFire = celebrationView.findViewById(R.id.fire_icon);
        // Set calories data
        caloriesNumber.setText(String.valueOf(achievement.getCalories()));
        kcal.setText(Translate.getValue("kcal").toLowerCase());
        youBurned.setText(Translate.getValue("you_burned"));
        viewAchievementText.setText(Translate.getValue("view_reward"));
        tvComparison.setText(Translate.getValue(achievement.getBadgeNameKey()));

        // Set achievement message
        String message = Translate.getValue(achievement.getDescriptionKey());
        achievementMessage.setText(message);

        // Set button listeners
        viewAchievementButton.setOnClickListener(v -> {
            setVibrate(v);
            Log.d("AchievementCelebration", "View achievement button clicked");
            // Show the actual achievement dialog
            AchievementDialogHelper.showAchievementDialog(getContext(), this, achievement,
                    new AchievementDialogHelper.OnAchievementDialogListener() {
                        @Override
                        public void onAchievementDialogClosed() {
                            // Dialog closed, celebration layout remains visible
                        }

                        @Override
                        public void onShowCelebrationLayout(Achievement achievement) {
                            // Not used in this context
                        }
                    });
        });

        doneButton.setOnClickListener(v -> {
            Log.d("AchievementCelebration", "Done celebration button clicked");
            setVibrate(v);
            // Remove celebration layout and close fragment
            if (celebrationView.getParent() instanceof ViewGroup) {
                ((ViewGroup) celebrationView.getParent()).removeView(celebrationView);
            }
            if (getActivity() != null) {
                getActivity().onBackPressed();
            }
        });
        doneButton.setPaintFlags(doneButton.getPaintFlags() | Paint.UNDERLINE_TEXT_FLAG);
        doneButton.setText(Translate.getValue("done"));
        setAchievementBadge(backgroundCelebration, achievement.getId());
        setAchievementFire(achievementFire, achievement.getId());
    }

    private void setAchievementBadge(ImageView imageView, int backgroundId) {
        // Build the asset path for the badge
        String badgeFileName = "bg_" + backgroundId + ".webp";
        String assetPath = "file:///android_asset/achievements/background/" + badgeFileName;

        // Load image with Glide (blur effect will be applied via ColorFilter)
        if (getContext() != null) {
            Glide.with(getContext())
                    .load(assetPath)
                    .centerCrop()
                    .into(imageView);
        }

        // Set transparent background to show the badge image properly
    }

    private void setAchievementFire(ImageView imageView, int achievementId) {
        // Build the asset path for the fire
        String badgeFileName = "iconfire_" + achievementId + ".webp";
        String assetPath = "file:///android_asset/achievements/iconFire/" + badgeFileName;

        // Load image with Glide (blur effect will be applied via ColorFilter)
        if (getContext() != null) {
            Glide.with(getContext())
                    .load(assetPath)
                    .fitCenter()
                    .into(imageView);
        }

        // Set transparent background to show the badge image properly
    }

}