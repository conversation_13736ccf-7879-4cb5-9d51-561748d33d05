package com.vgfit.shefit.fragment.nutrition.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.ItemVerticalDayWorkoutBinding;
import com.vgfit.shefit.fragment.nutrition.callbacks.NutrItemClick;
import com.vgfit.shefit.realm.NutritionDay;

import java.util.ArrayList;

import me.everything.android.ui.overscroll.OverScrollDecoratorHelper;

public class AdapterVertNutrition extends RecyclerView.Adapter {
    private final Context context;
    private final NutrItemClick listener;
    private final int layoutWidth;
    private final int layoutHeight;
    private final ArrayList<NutritionDay> listNutritionDay;

    public AdapterVertNutrition(Context context, ArrayList<NutritionDay> listNutritionDay, NutrItemClick listener, int layoutWidth, int layoutHeight) {
        this.context = context;
        this.listNutritionDay = listNutritionDay;
        this.listener = listener;
        this.layoutWidth = layoutWidth;
        this.layoutHeight = layoutHeight;
    }

    @Override
    public int getItemCount() {
        return listNutritionDay.size();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ItemVerticalDayWorkoutBinding binding = ItemVerticalDayWorkoutBinding.inflate(inflater, parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {
        String orderDay = String.valueOf(listNutritionDay.get(position).getOrder());
        ViewHolder holder = ((ViewHolder) viewHolder);
        holder.tvDay.setTag(position);
        holder.tvDay.setText("Day " + orderDay);
        holder.horizontalAdapter = new AdapterHorNutrition(listNutritionDay.get(position).getMeals(), context, listener, layoutWidth, layoutHeight - (int) context.getResources().getDimension(R.dimen.height_day));
        holder.horizontalRV.setAdapter(holder.horizontalAdapter);
        OverScrollDecoratorHelper.setUpOverScroll(holder.horizontalRV, OverScrollDecoratorHelper.ORIENTATION_HORIZONTAL);
    }

    public class ViewHolder extends RecyclerView.ViewHolder {

        TextView tvDay;
        AdapterHorNutrition horizontalAdapter;
        RecyclerView horizontalRV;

        public ViewHolder(ItemVerticalDayWorkoutBinding binding) {
            super(binding.getRoot());
            horizontalRV = binding.rvNutritionHorizontal;
            tvDay = binding.tvNutritionDay;
            horizontalRV.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false));
        }
    }
}
