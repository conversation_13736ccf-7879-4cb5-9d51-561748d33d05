package com.vgfit.shefit.fragment.workouts;

import android.os.CountDownTimer;

import com.vgfit.shefit.fragment.workouts.callbacks.OnFinishListener;
import com.vgfit.shefit.fragment.workouts.callbacks.ReturnTimeAmount;


public class Counter {

    int identifier;
    ReturnTimeAmount returnTime;
    OnFinishListener onFinishListener;
    private CountDownTimer counter;

    public void setOnTickListener(ReturnTimeAmount returnTime, int identifier) {
        this.returnTime = returnTime;
        this.identifier = identifier;
    }

    public void setOnFinishListener(OnFinishListener onFinishListener) {
        this.onFinishListener = onFinishListener;
    }

    public void start(long millisecondsAmount) {
        long countDownInterval = 10;
        counter = new CountDownTimer(millisecondsAmount, countDownInterval) {
            @Override
            public void onTick(long millisUntilFinished) {
                returnTime.havingTime(millisUntilFinished, identifier);
            }

            @Override
            public void onFinish() {
                onFinishListener.onFinish(identifier);
            }
        };
        counter.start();
    }

    public void stop() {
        counter.cancel();
    }
}
