package com.vgfit.shefit.fragment.workouts.model;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class ItemExercise implements Parcelable {

    private long previewTime = 5000;
    private long timeSymbol = 220;
    private String nameExercise;
    private String nameVideo;
    private String nameVideoHD;
    private String nameVideoLowQ;
    private long exerciseTime;
    private long restTime;
    private String image;
    private String playlist;

    public ItemExercise() {
    }

    public ItemExercise(String nameExercise, String nameVideo, String nameVideoHD, String nameVideoLowQ, long exerciseTime, long restTime, String image, String playlist) {
        this.setNameExercise(nameExercise);
        this.setNameVideo(nameVideo);
        this.setNameVideoHD(nameVideoHD);
        this.setNameVideoLowQ(nameVideoLowQ);
        this.setExerciseTime(exerciseTime);
        this.setRestTime(restTime);
        this.setImage(image);
        this.setPlaylist(playlist);
    }

    protected ItemExercise(Parcel in) {
        previewTime = in.readLong();
        timeSymbol = in.readLong();
        nameExercise = in.readString();
        nameVideo = in.readString();
        nameVideoHD = in.readString();
        nameVideoLowQ = in.readString();
        exerciseTime = in.readLong();
        restTime = in.readLong();
        image = in.readString();
        playlist = in.readString();
    }

    public static final Creator<ItemExercise> CREATOR = new Creator<ItemExercise>() {
        @Override
        public ItemExercise createFromParcel(Parcel in) {
            return new ItemExercise(in);
        }

        @Override
        public ItemExercise[] newArray(int size) {
            return new ItemExercise[size];
        }
    };

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getNameExercise() {
        return nameExercise;
    }

    public void setNameExercise(String nameExercise) {
        this.nameExercise = nameExercise;
    }

    public long getPreviewTime() {
        return previewTime;
    }

    public void setPreviewTime(long previewTime) {
        this.previewTime = previewTime;
    }

    public long getTimeSymbol() {
        return timeSymbol;
    }

    public void setTimeSymbol(long timeSymbol) {
        this.timeSymbol = timeSymbol;
    }

    public String getNameVideo() {
        return nameVideo;
    }

    public void setNameVideo(String nameVideo) {
        this.nameVideo = nameVideo;
    }

    public String getNameVideoLowQ() {
        return nameVideoLowQ;
    }

    public void setNameVideoLowQ(String nameVideoLowQ) {
        this.nameVideoLowQ = nameVideoLowQ;
    }

    public long getExerciseTime() {
        return exerciseTime;
    }

    public void setExerciseTime(long exerciseTime) {
        this.exerciseTime = exerciseTime;
    }

    public long getRestTime() {
        return restTime;
    }

    public void setRestTime(long restTime) {
        this.restTime = restTime;
    }

    public String getNameVideoHD() {
        return nameVideoHD;
    }

    public String getPlaylist() {
        return playlist;
    }

    public void setPlaylist(String playlist) {
        this.playlist = playlist;
    }

    public void setNameVideoHD(String nameVideoHD) {
        this.nameVideoHD = nameVideoHD;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        dest.writeLong(previewTime);
        dest.writeLong(timeSymbol);
        dest.writeString(nameExercise);
        dest.writeString(nameVideo);
        dest.writeString(nameVideoHD);
        dest.writeString(nameVideoLowQ);
        dest.writeLong(exerciseTime);
        dest.writeLong(restTime);
        dest.writeString(image);
        dest.writeString(playlist);
    }
}
