package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentCrushingRedesignBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;

import java.util.Arrays;

public class CrushingGoalRedesignFragment extends Fragment {
    private FragmentCrushingRedesignBinding binding;
    private boolean isFt = false;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;

    public static CrushingGoalRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        CrushingGoalRedesignFragment fragment = new CrushingGoalRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View]Crushing your goal View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentCrushingRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();

        binding.rlContinue.setOnClickListener(view1 -> {
            isValidPush = false;

//            prefsUtilsWtContext.setIntegerPreference(heightValueSave, currentValue);
            NotificationTurnFragment fragmentC = NotificationTurnFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.animation_fade_in, R.anim.animation_fade_out, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_crushing").commitAllowingStateLoss();
            setVibrate(view1);
        });
        binding.chooseCrush1.setOnClickListener(v -> {
            saveStatusCrush("chooseCrush1");
            getStatusCrush("chooseCrush1", binding.chooseCrush1, binding.chooseCrush1Txt);
            prefsUtilsWtContext.setBooleanPreference("chooseCrush2", false);
            prefsUtilsWtContext.setBooleanPreference("chooseCrush3", false);
            prefsUtilsWtContext.setBooleanPreference("chooseCrush4", false);
            getStatusCrush("chooseCrush2", binding.chooseCrush2, binding.chooseCrush2Txt);
            getStatusCrush("chooseCrush3", binding.chooseCrush3, binding.chooseCrush3Txt);
            getStatusCrush("chooseCrush4", binding.chooseCrush4, binding.chooseCrush4Txt);
            setVibrate(v);
        });
        binding.chooseCrush2.setOnClickListener(v -> {
            saveStatusCrush("chooseCrush2");
            getStatusCrush("chooseCrush2", binding.chooseCrush2, binding.chooseCrush2Txt);
            prefsUtilsWtContext.setBooleanPreference("chooseCrush1", false);
            prefsUtilsWtContext.setBooleanPreference("chooseCrush3", false);
            prefsUtilsWtContext.setBooleanPreference("chooseCrush4", false);
            getStatusCrush("chooseCrush1", binding.chooseCrush1, binding.chooseCrush1Txt);
            getStatusCrush("chooseCrush3", binding.chooseCrush3, binding.chooseCrush3Txt);
            getStatusCrush("chooseCrush4", binding.chooseCrush4, binding.chooseCrush4Txt);
            setVibrate(v);
        });
        binding.chooseCrush3.setOnClickListener(v -> {
            saveStatusCrush("chooseCrush3");
            getStatusCrush("chooseCrush3", binding.chooseCrush3, binding.chooseCrush3Txt);
            prefsUtilsWtContext.setBooleanPreference("chooseCrush1", false);
            prefsUtilsWtContext.setBooleanPreference("chooseCrush2", false);
            prefsUtilsWtContext.setBooleanPreference("chooseCrush4", false);
            getStatusCrush("chooseCrush1", binding.chooseCrush1, binding.chooseCrush1Txt);
            getStatusCrush("chooseCrush2", binding.chooseCrush2, binding.chooseCrush2Txt);
            getStatusCrush("chooseCrush4", binding.chooseCrush4, binding.chooseCrush4Txt);
            setVibrate(v);
        });
        binding.chooseCrush4.setOnClickListener(v -> {
            saveStatusCrush("chooseCrush4");
            getStatusCrush("chooseCrush4", binding.chooseCrush4, binding.chooseCrush4Txt);
            prefsUtilsWtContext.setBooleanPreference("chooseCrush1", false);
            prefsUtilsWtContext.setBooleanPreference("chooseCrush2", false);
            prefsUtilsWtContext.setBooleanPreference("chooseCrush3", false);
            getStatusCrush("chooseCrush1", binding.chooseCrush1, binding.chooseCrush1Txt);
            getStatusCrush("chooseCrush2", binding.chooseCrush2, binding.chooseCrush2Txt);
            getStatusCrush("chooseCrush3", binding.chooseCrush3, binding.chooseCrush3Txt);
            setVibrate(v);
        });
        getStatusCrush("chooseCrush1", binding.chooseCrush1, binding.chooseCrush1Txt);
        getStatusCrush("chooseCrush2", binding.chooseCrush2, binding.chooseCrush2Txt);
        getStatusCrush("chooseCrush3", binding.chooseCrush3, binding.chooseCrush3Txt);
        getStatusCrush("chooseCrush4", binding.chooseCrush4, binding.chooseCrush4Txt);
        binding.backLayout.setOnClickListener(view12 -> toBack());
        binding.backTxt.getPaint().setUnderlineText(true);
        setParamsFragment();
    }

    private void saveStatusCrush(String keyMeal) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyMeal, false);
        prefsUtilsWtContext.setBooleanPreference(keyMeal, !isSelected);
    }

    private void getStatusCrush(String keyMeal, RelativeLayout layout, TextView textView) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyMeal, layout == binding.chooseCrush2);
        layout.setSelected(isSelected);
        textView.setSelected(isSelected);
    }

    public void setParamsFragment() {
        Log.d("TestWeight", "weight Accessed isLbs-->" + isFt);
//        titleFragment.setText(Translate.getValue("how’ll_you_reward_yourself_for_crushing_your_goals"));
        binding.textView16.setText(Translate.getValueLine("how’ll_you_reward_yourself_for_crushing_your_goals", Arrays.asList(2, 4, 6), false));
        binding.chooseCrush1Txt.setText(Translate.getValue("i’ll_buy_myself_a_nice_present"));
        binding.chooseCrush2Txt.setText(Translate.getValue("i’m_going_on_a_short_vacation"));
        binding.chooseCrush3Txt.setText(Translate.getValue("i’m_going_to_have_a_spa_day"));
        binding.chooseCrush4Txt.setText(Translate.getValue(""));
        binding.btnContinue.setText(Translate.getValue("next"));
        binding.backTxt.setText(Translate.getValue("go_back"));
        binding.linearProgress.setProgress(92);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
//                if (!lunchFirstTime)
                toBack();
                return true; // pretend we've processed it
            } else
                return false; // pass on to be processed as normal
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void toBack() {
        isValidPush = false;
        if (getActivity() != null)
            getActivity().onBackPressed();
    }
}
