package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;

import android.os.Bundle;
import android.view.HapticFeedbackConstants;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentWorksRedesignBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;

public class SelectorWorksRedesignFragment extends Fragment {
    private FragmentWorksRedesignBinding binding;
    private View view;
    private PrefsUtilsWtContext pf;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;

    public static SelectorWorksRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        SelectorWorksRedesignFragment fragment = new SelectorWorksRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        pf = new PrefsUtilsWtContext(getContext());
        Bundle args = getArguments();
        if (args != null)
            lunchFirstTime = args.getBoolean("lunchFirstTime");
        else
            lunchFirstTime = false;
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View] Fitness Goal View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentWorksRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();
        binding.rlContinue.setOnClickListener(view1 -> {
            isValidPush = false;
            if (pf.getStringPreferenceProfile("active_daily_workout") == null)
                pf.setStringPreferenceProfile("active_daily_workout", "4");
            AreasLifeRedesignFragment fragmentC = AreasLifeRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_").commitAllowingStateLoss();
            setVibrate(view1);
        });
        binding.bt1.setOnClickListener(view12 -> {
            selectorWorkout(2);
            pf.setStringPreferenceProfile("active_daily_workout", "2");
            setVibrate(view12);
        });
        binding.bt2.setOnClickListener(view13 -> {
            selectorWorkout(3);
            pf.setStringPreferenceProfile("active_daily_workout", "3");
            setVibrate(view13);
        });
        binding.bt3.setOnClickListener(view14 -> {
            selectorWorkout(4);
            pf.setStringPreferenceProfile("active_daily_workout", "4");
            setVibrate(view14);
        });
        binding.bt4.setOnClickListener(view15 -> {
            selectorWorkout(1);
            pf.setStringPreferenceProfile("active_daily_workout", "1");
            setVibrate(view15);
        });
        binding.backLayout.setOnClickListener(view12 -> toBack());
        binding.backTxt.getPaint().setUnderlineText(true);
        defaultSelected();
    }


    public void selectorWorkout(int numb) {
        binding.bt1.setSelected(numb == 2);
        binding.bt2.setSelected(numb == 3);
        binding.bt3.setSelected(numb == 4);
        binding.bt4.setSelected(numb == 1);
        binding.bt1.setSelected(false);
        binding.bt2.setSelected(false);
        binding.bt3.setSelected(false);
        binding.bt4.setSelected(false);
        binding.bt1Txt.setSelected(false);
        binding.bt2Txt.setSelected(false);
        binding.bt3Txt.setSelected(false);
        binding.bt4Txt.setSelected(false);

        switch (numb) {
            case 2:
                binding.bt1.setSelected(true);
                binding.bt1Txt.setSelected(true);
                break;
            case 3:
                binding.bt2.setSelected(true);
                binding.bt2Txt.setSelected(true);
                break;
            case 4:
                binding.bt3.setSelected(true);
                binding.bt3Txt.setSelected(true);
                break;
            case 1:
                binding.bt4.setSelected(true);
                binding.bt4Txt.setSelected(true);
                break;
        }
    }

    public void defaultSelected() {
        String selected = pf.getStringPreferenceProfile("active_daily_workout");
        selectorWorkout(selected != null ? Integer.parseInt(selected) : 4);
        binding.textView16.setText(Translate.getValue("what’s_your_fitness_goal"));
        binding.bt1Txt.setText(Translate.getValue("tone_my_body"));
        binding.bt2Txt.setText(Translate.getValue("gain_muscles"));
        binding.bt3Txt.setText(Translate.getValue("be_more_active"));
        binding.bt4Txt.setText(Translate.getValue("lose_weightt"));
        binding.btnContinue.setText(Translate.getValue("next"));
        binding.backTxt.setText(Translate.getValue("go_back"));
    }

    private void setVibrate(View view) {
        view.setHapticFeedbackEnabled(true);
        view.performHapticFeedback(HapticFeedbackConstants.KEYBOARD_TAP);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
                if (!lunchFirstTime)
                    toBack();
                return true; // pretend we've processed it
            } else
                return false; // pass on to be processed as normal
        });
        isValidPush = true;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void toBack() {
        isValidPush = false;
        if (getActivity() != null)
            getActivity().onBackPressed();
    }
}
