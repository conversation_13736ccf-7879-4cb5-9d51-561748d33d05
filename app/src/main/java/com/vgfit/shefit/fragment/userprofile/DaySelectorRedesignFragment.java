package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.androidbolts.topsheet.TopSheetBehavior;
import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentDayRedesignBinding;
import com.vgfit.shefit.fragment.personal_plan.structure.PersonalPlanPresenter;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;

import java.util.ArrayList;

public class DaySelectorRedesignFragment extends Fragment {
    private static final String TAG_MONDAY = "mondayDay";
    private static final String TAG_TUESDAY = "tuesdayDay";
    private static final String TAG_WEDNESDAY = "wednesdayDay";
    private static final String TAG_THURSDAY = "thursdayDay";
    private static final String TAG_FRIDAY = "fridayDay";
    private static final String TAG_SATURDAY = "satDay";
    private static final String TAG_SUNDAY = "sunDay";
    private FragmentDayRedesignBinding binding;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;
    private SaveProfileServer saveProfileServer;

    public static DaySelectorRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        DaySelectorRedesignFragment fragment = new DaySelectorRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View]Training days View appeared");
        saveProfileServer = new SaveProfileServer(prefsUtilsWtContext, null);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentDayRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();
        binding.mondayDay.setOnClickListener(v -> {
            saveStatusMeal(TAG_MONDAY, binding.mondayDay);
            getStatusMeal(TAG_MONDAY, binding.mondayDay, binding.mondayDayTxt);
            isActiveNext();
            setVibrate(v);
        });
        binding.tuesdayDay.setOnClickListener(v -> {
            saveStatusMeal(TAG_TUESDAY, binding.tuesdayDay);
            getStatusMeal(TAG_TUESDAY, binding.tuesdayDay, binding.tuesdayDayTxt);
            isActiveNext();
            setVibrate(v);
        });
        binding.wednesdayDay.setOnClickListener(v -> {
            saveStatusMeal(TAG_WEDNESDAY, binding.wednesdayDay);
            getStatusMeal(TAG_WEDNESDAY, binding.wednesdayDay, binding.wednesdayDayTxt);
            isActiveNext();
            setVibrate(v);
        });
        binding.thursdayDay.setOnClickListener(v -> {
            saveStatusMeal(TAG_THURSDAY, binding.thursdayDay);
            getStatusMeal(TAG_THURSDAY, binding.thursdayDay, binding.thursdayDayTxt);
            isActiveNext();
            setVibrate(v);
        });
        binding.fridayDay.setOnClickListener(v -> {
            saveStatusMeal(TAG_FRIDAY, binding.fridayDay);
            getStatusMeal(TAG_FRIDAY, binding.fridayDay, binding.fridayDayTxt);
            isActiveNext();
            setVibrate(v);
        });
        binding.satDay.setOnClickListener(v -> {
            saveStatusMeal(TAG_SATURDAY, binding.satDay);
            getStatusMeal(TAG_SATURDAY, binding.satDay, binding.satDayTxt);
            isActiveNext();
            setVibrate(v);
        });
        binding.sunDay.setOnClickListener(v -> {
            saveStatusMeal(TAG_SUNDAY, binding.sunDay);
            getStatusMeal(TAG_SUNDAY, binding.sunDay, binding.sunDayTxt);
            isActiveNext();
            setVibrate(v);
        });
        getStatusMeal(TAG_MONDAY, binding.mondayDay, binding.mondayDayTxt);
        getStatusMeal(TAG_TUESDAY, binding.tuesdayDay, binding.tuesdayDayTxt);
        getStatusMeal(TAG_WEDNESDAY, binding.wednesdayDay, binding.wednesdayDayTxt);
        getStatusMeal(TAG_THURSDAY, binding.thursdayDay, binding.thursdayDayTxt);
        getStatusMeal(TAG_FRIDAY, binding.fridayDay, binding.fridayDayTxt);
        getStatusMeal(TAG_SATURDAY, binding.satDay, binding.satDayTxt);
        getStatusMeal(TAG_SUNDAY, binding.sunDay, binding.sunDayTxt);
        isActiveNext();

        binding.rlContinue.setOnClickListener(view1 -> {
            setVibrate(view1);
            saveDaySelected();
        });
        binding.backLayout.setOnClickListener(view12 -> toBack());
        binding.backTxt.getPaint().setUnderlineText(true);
        setParamsFragment();
    }

    public void saveDaySelected() {
        ArrayList<Boolean> listDay = new ArrayList<>();
        listDay.add(getStatusMeal(TAG_MONDAY, binding.mondayDay, binding.mondayDayTxt));
        listDay.add(getStatusMeal(TAG_TUESDAY, binding.tuesdayDay, binding.tuesdayDayTxt));
        listDay.add(getStatusMeal(TAG_WEDNESDAY, binding.wednesdayDay, binding.wednesdayDayTxt));
        listDay.add(getStatusMeal(TAG_THURSDAY, binding.thursdayDay, binding.thursdayDayTxt));
        listDay.add(getStatusMeal(TAG_FRIDAY, binding.fridayDay, binding.fridayDayTxt));
        listDay.add(getStatusMeal(TAG_SATURDAY, binding.satDay, binding.satDayTxt));
        listDay.add(getStatusMeal(TAG_SUNDAY, binding.sunDay, binding.sunDayTxt));

        int countDay = 0;
        StringBuilder daySelected = new StringBuilder();
        for (int i = 0; i < listDay.size(); i++) {
            boolean isSelectedDay = listDay.get(i);
            if (isSelectedDay) {
                if (daySelected.length() > 0) daySelected.append(",");
                daySelected.append(i);
                countDay++;
            }
        }
        if (countDay < 2) {
            dialogCountDay();
        } else if (countDay > 6) {
            dialogCountDay();
        } else {
            prefsUtilsWtContext.setStringPreferenceProfile("workout_days", daySelected.toString());
            saveProfileServer.saveFullProfileToServer(PersonalPlanPresenter.getUserProfile(prefsUtilsWtContext));
            isValidPush = false;
            LikeSelectorRedesignFragment fragmentC = LikeSelectorRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_").commitAllowingStateLoss();
        }
    }

    private void isActiveNext() {
        boolean value = checkSelectedDay();
        if (getContext() != null) {
            binding.rlContinue.setBackgroundResource(value ? R.drawable.selector_start : R.drawable.bg_start2_deactivate);
            binding.rlContinue.setEnabled(value);
        }
    }

    private boolean checkSelectedDay() {
        ArrayList<Boolean> listDay = new ArrayList<>();
        listDay.add(getStatusMeal(TAG_MONDAY, binding.mondayDay, binding.mondayDayTxt));
        listDay.add(getStatusMeal(TAG_TUESDAY, binding.tuesdayDay, binding.tuesdayDayTxt));
        listDay.add(getStatusMeal(TAG_WEDNESDAY, binding.wednesdayDay, binding.wednesdayDayTxt));
        listDay.add(getStatusMeal(TAG_THURSDAY, binding.thursdayDay, binding.thursdayDayTxt));
        listDay.add(getStatusMeal(TAG_FRIDAY, binding.fridayDay, binding.fridayDayTxt));
        listDay.add(getStatusMeal(TAG_SATURDAY, binding.satDay, binding.satDayTxt));
        listDay.add(getStatusMeal(TAG_SUNDAY, binding.sunDay, binding.sunDayTxt));

        int countDay = 0;
        StringBuilder daySelected = new StringBuilder();
        for (int i = 0; i < listDay.size(); i++) {
            boolean isSelectedDay = listDay.get(i);
            if (isSelectedDay) {
                if (daySelected.length() > 0) daySelected.append(",");
                daySelected.append(i);
                countDay++;
            }
        }
        return countDay < 7 && countDay > 1;
    }

    public void dialogCountDay() {
        //warning
        binding.titleAlert.setText(Translate.getValue("warning"));
        binding.messageAlert.setText(Translate.getValue("you_must_select_minimum_2_and_maximum_6_days"));
        binding.messageAlert.setSelected(true);
        TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_EXPANDED);
        binding.topSheet.setOnClickListener(v -> TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_COLLAPSED));

        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(
            () -> TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_COLLAPSED),
            3000
        );
    }

    private void saveStatusMeal(String keyMeal, RelativeLayout layout) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyMeal, layout == binding.mondayDay || layout == binding.wednesdayDay || layout == binding.fridayDay);
        prefsUtilsWtContext.setBooleanPreference(keyMeal, !isSelected);
    }

    private boolean getStatusMeal(String keyMeal, RelativeLayout layout, TextView textView) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyMeal, layout == binding.mondayDay || layout == binding.wednesdayDay || layout == binding.fridayDay);
        layout.setSelected(isSelected);
        textView.setSelected(isSelected);
        return isSelected;
    }

    public void setParamsFragment() {
        binding.textView16.setText(Translate.getValue("pick_training_days"));
        binding.mondayDayTxt.setText(Translate.getValue("monday"));
        binding.tuesdayDayTxt.setText(Translate.getValue("tuesday"));
        binding.wednesdayDayTxt.setText(Translate.getValue("wednesday"));
        binding.thursdayDayTxt.setText(Translate.getValue("thursday"));
        binding.fridayDayTxt.setText(Translate.getValue("friday"));
        String satTxt = Translate.getValue("saturday");
        if (satTxt.length() >= 3) {
            satTxt = satTxt.substring(0, 3);
        }
        binding.satDayTxt.setText(satTxt);
        String sunTxt = Translate.getValue("sunday");
        if (sunTxt.length() >= 3) {
            sunTxt = sunTxt.substring(0, 3);
        }
        binding.sunDayTxt.setText(sunTxt);

        binding.btnContinue.setText(Translate.getValue("next"));
        binding.backTxt.setText(Translate.getValue("go_back"));
        binding.linearProgress.setProgress(76);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
                toBack();
                return true; // pretend we've processed it
            } else return false; // pass on to be processed as normal
        });
    }

    private void toBack() {
        isValidPush = false;
        FragmentManager fm = getParentFragmentManager();
        fm.popBackStack("frag_sleep", FragmentManager.POP_BACK_STACK_INCLUSIVE);
    }

}
