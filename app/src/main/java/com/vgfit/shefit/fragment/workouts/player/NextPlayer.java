package com.vgfit.shefit.fragment.workouts.player;

import static com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED;
import static com.google.android.exoplayer2.PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.widget.FrameLayout;
import android.widget.Toast;

import com.google.android.exoplayer2.C;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.source.hls.HlsMediaSource;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.exoplayer2.upstream.DataSource;
import com.vgfit.shefit.fragment.workouts.model.ItemExercise;

import java.util.ArrayList;

public class NextPlayer {
    private static final String KEY_PLAY_WHEN_READY = "play_when_ready";
    private static final String KEY_WINDOW = "window";
    private static final String KEY_POSITION = "position";
    private Context context;
    private ArrayList<ItemExercise> list;
    private ExoPlayer player;
    private PlayerView playerView;
    private boolean playWhenReady = true;
    private int currentWindow;
    private long playbackPosition;
    private boolean shouldAutoPlay;
    private boolean validFormat = true;
    private boolean isBadInternet;
    private DataSource.Factory dataSourceFactory;

    public NextPlayer(Context context, ArrayList<ItemExercise> list, PlayerView playerView, boolean isBadInternet) {
        this.context = context;
        this.list = list;
        this.playerView = playerView;
        this.isBadInternet = isBadInternet;
    }

    public void initPlayer(Bundle savedInstanceState, DataSource.Factory dataSourceFactory) {
        if (savedInstanceState == null) {
            playWhenReady = true;
            currentWindow = 0;
            playbackPosition = 0;
        } else {
            playWhenReady = savedInstanceState.getBoolean(KEY_PLAY_WHEN_READY);
            currentWindow = savedInstanceState.getInt(KEY_WINDOW);
            playbackPosition = savedInstanceState.getLong(KEY_POSITION);
        }
        shouldAutoPlay = true;
        this.dataSourceFactory = dataSourceFactory;
    }

    public void initializePlayer() {
        assert playerView != null;
        String proxyUrl;
        playerView.requestFocus();
        player = new ExoPlayer.Builder(context).build();
        playerView.setPlayer(player);
        player.setRepeatMode(Player.REPEAT_MODE_ONE);
        player.setPlayWhenReady(shouldAutoPlay);
        MediaSource[] mediaSources = new MediaSource[list.size()];
        String path;
        for (int i = 0; i < list.size(); i++) {
            path = list.get(i).getPlaylist();
            String uri = path;
            Log.d("TestNameHLS", "nameVideoHls-->" + uri);
            HlsMediaSource hlsMediaSource = new HlsMediaSource.Factory(dataSourceFactory)
                    .createMediaSource(MediaItem.fromUri(uri));
            player.addMediaSource(hlsMediaSource);
//            if (isBadInternet) {
//                path = list.get(i).getNameVideoLowQ();
//                MediaSource mediaSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(path));
//                player.addMediaSource(mediaSource);
//            } else {
//                if (validFormat) {
//                    path = list.get(i).getNameVideo();
//                } else {
//                    path = list.get(i).getNameVideoHD();
//                }
//                MediaSource mediaSource = new ProgressiveMediaSource.Factory(dataSourceFactory).createMediaSource(MediaItem.fromUri(path));
//                player.addMediaSource(mediaSource);
//            }
        }
        boolean haveStartPosition = currentWindow != C.INDEX_UNSET;
        if (haveStartPosition) player.seekTo(currentWindow, playbackPosition);
        player.prepare();
        player.setVolume(0F);
        player.addListener(new Player.Listener() {
            @Override
            public void onPlayerError(PlaybackException error) {
                validFormat = false;
//                releasePlayer();
//                initializePlayer();
                if (player != null) {
                    player.stop();
                    player.prepare();
                    player.setPlayWhenReady(shouldAutoPlay);
                }
                Player.Listener.super.onPlayerError(error);
                if (error.errorCode == ERROR_CODE_IO_NETWORK_CONNECTION_FAILED || error.errorCode == ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT) {
                    Toast.makeText(context, error.getMessage(), Toast.LENGTH_SHORT).show();
            }
                Log.d("TestError","error player-->"+error.getMessage());
        }

        @Override
        public void onPlaybackStateChanged ( int playbackState){
            Player.Listener.super.onPlaybackStateChanged(playbackState);
            Log.d("TestPlayer", "Clicked state->" + playbackState);
        }
    });

    setPlayersSize();

}

    public void releasePlayer() {
        if (player != null) {
            updateStartPosition();
            shouldAutoPlay = player.getPlayWhenReady();
            player.stop();
            player.release();
            player = null;
        }
    }

    private void updateStartPosition() {
        if (player != null) {
            playbackPosition = player.getCurrentPosition();
            currentWindow = player.getCurrentWindowIndex();
            playWhenReady = player.getPlayWhenReady();
        }
    }

    public void startPreviewVideo(int position) {
        if (position + 1 < list.size()) {
            position += 1;
            if (player != null) {
                player.seekTo(position, C.TIME_UNSET);
                player.setPlayWhenReady(playWhenReady);
            }
        }
    }

    public void stopPreviewVideo() {
        if (player != null) player.setPlayWhenReady(false);
    }

    private void setPlayersSize() {
        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) playerView.getLayoutParams();
        params.setMargins(0, -180, 0, -200);
        playerView.setLayoutParams(params);
    }

    public ExoPlayer getPlayer() {
        return this.player;
    }
}