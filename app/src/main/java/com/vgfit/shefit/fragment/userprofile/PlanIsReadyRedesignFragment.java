package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;
import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.nostra13.universalimageloader.core.ImageLoader;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.FragmentPlanReadyRBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.screen.ImageUtils;

public class PlanIsReadyRedesignFragment extends Fragment {
    private FragmentPlanReadyRBinding binding;
//    @BindView(R.id.startOnboarding)
//    Button startOnboarding;
//    @BindView(R.id.imageView)
//    ImageView imageView;
//    @BindView(R.id.shortDescription)
//    TextView shortDescription;
//    @BindView(R.id.textView21)
//    TextView textView21;


    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;

    public static PlanIsReadyRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        PlanIsReadyRedesignFragment fragment = new PlanIsReadyRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View]Plans is almost ready View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentPlanReadyRBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();

        binding.startOnboarding.setOnClickListener(view1 -> {
            isValidPush = false;
            Log.d("TestClick", "next activate");
            DaySelectorRedesignFragment fragmentC = DaySelectorRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(0, R.anim.slide_down, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_").commitAllowingStateLoss();
            setVibrate(view1);
        });
        ImageLoader.getInstance().displayImage("assets://onboardingImage/15.webp", binding.imageView, ImageUtils.getRoundDisplayImageOptions(dpToPx(getContext(), 30)), null);
        TextView title = (TextView) binding.textView21;
        title.setText(Translate.getValue("your_personal_plan_is_almost_ready!"));
        binding.startOnboarding.setText(Translate.getValue("got_it"));
        String word = Translate.getValue("lack_of_sleep_can_often_result_in_low_energy_and_morning_anxiety");
        TextView shortDescription= (TextView) binding.shortDescription;
        shortDescription.setText(Translate.colorizeSubString(word, Color.parseColor("#ff5d21")));
    }


    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener((v, keyCode, event) -> {
            return keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN; // pretend we've processed it
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

}
