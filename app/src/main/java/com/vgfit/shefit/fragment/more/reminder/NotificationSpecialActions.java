package com.vgfit.shefit.fragment.more.reminder;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;

import com.vgfit.shefit.fragment.more.service.SharedPreferencesData;

import java.util.Calendar;

class NotificationSpecialActions {
    private Context context;
    private SharedPreferencesData sharedPreferencesData;

    NotificationSpecialActions(Context context, SharedPreferencesData sharedPreferencesData) {
        this.context = context;
        this.sharedPreferencesData = sharedPreferencesData;
    }

    void dailyReminderSpecialActions(String key) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, sharedPreferencesData.getHour(key));
        calendar.set(Calendar.MINUTE, sharedPreferencesData.getMinute(key));
        calendar.set(Calendar.SECOND, 0);
        Intent intent1 = new Intent(context, DailyAlarmReceiver.class);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, 0, intent1, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        assert am != null;
        am.setInexactRepeating(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), AlarmManager.INTERVAL_DAY, pendingIntent);
    }

    void inactivitySpecialActions(String key) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, sharedPreferencesData.getHour(key));
        calendar.set(Calendar.MINUTE, sharedPreferencesData.getMinute(key));
        calendar.set(Calendar.SECOND, 0);
        Intent intent1 = new Intent(context, InactivityAlarmReceiver.class);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, 0, intent1, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        assert am != null;
        am.setInexactRepeating(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), AlarmManager.INTERVAL_DAY, pendingIntent);
    }
}