package com.vgfit.shefit.fragment.workouts.widjetworkout.adapter;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.app.Dialog;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.skydoves.progressview.ProgressView;
import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.ItemViewpagerInfoBinding;
import com.vgfit.shefit.fragment.premium.OnSingleClickListener;
import com.vgfit.shefit.fragment.workouts.widjetworkout.callbacks.ShowWorkExercise;
import com.vgfit.shefit.fragment.workouts.widjetworkout.callbacks.StartWorkoutCliced;
import com.vgfit.shefit.realm.Workout;
import com.vgfit.shefit.util.Translate;

import java.util.List;


public class AdapterWidgetLevel extends RecyclerView.Adapter<AdapterWidgetLevel.LevelViewHolder> {
    private final List<Workout> listLevel;
    private final StartWorkoutCliced startWorkoutCliced;
    private final ShowWorkExercise showWorkExercise;
    private final PrefsUtilsWtContext prefsUtilsWtContext;

    public AdapterWidgetLevel(List<Workout> listLevel, Context context, StartWorkoutCliced startWorkoutCliced, ShowWorkExercise showWorkExercise) {
        this.listLevel = listLevel;
        this.startWorkoutCliced = startWorkoutCliced;
        this.showWorkExercise = showWorkExercise;
        prefsUtilsWtContext = new PrefsUtilsWtContext(context);
    }

    @NonNull
    @Override
    public LevelViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ItemViewpagerInfoBinding binding = ItemViewpagerInfoBinding.inflate(inflater, parent, false);
        return new LevelViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull LevelViewHolder levelViewHolder, int position) {
        Workout itemWorkout = listLevel.get(position);
        String nameWorkout = "";
        if (itemWorkout.getName() != null) {
            nameWorkout = Translate.getValue(itemWorkout.getName());
            Log.d("TestWorkout", "nameWorkout-->" + nameWorkout+" key--->"+itemWorkout.getName());
            levelViewHolder.workoutName.setText(nameWorkout);
        }
        if (itemWorkout.getLevel() != null && itemWorkout.getLevel().getName() != null) {
            String nameLevel = Translate.getValue(itemWorkout.getLevel().getName());
            levelViewHolder.levelLabel.setText(nameLevel);
        }

        String timeSpend = itemWorkout.getDuration() + " min";
        levelViewHolder.timeSpend.setText(timeSpend);

        String kcalSpend = itemWorkout.getKcal() + " kcal";
        levelViewHolder.labelKcal.setText(kcalSpend);

        levelViewHolder.buttonBackground.setOnClickListener(v -> {
            setVibrate(v);
            startWorkoutCliced.init(itemWorkout);
        });
        levelViewHolder.btnContinue.setOnClickListener(v -> {
            setVibrate(v);
            startWorkoutCliced.init(itemWorkout);
        });
        levelViewHolder.llProgress.setOnClickListener(new OnSingleClickListener() {
            @Override
            public void onSingleClick(View v) {
                setVibrate(v);
                startWorkoutCliced.init(itemWorkout);
            }
        });
        levelViewHolder.btnReset.setOnClickListener(v -> {
            setVibrate(v);
            showResetDialog(v.getContext(), itemWorkout);
        });
        levelViewHolder.btnShowExercises.setText(Translate.getValue("show_exercises"));
        levelViewHolder.btnShowExercises.getPaint().setUnderlineText(true);
        String finalNameWorkout = nameWorkout;
        levelViewHolder.btnShowExercises.setOnClickListener(view -> {
            setVibrate(view);
            showWorkExercise.init(itemWorkout, finalNameWorkout);
        });

        int progress = getProgressWorkout(itemWorkout.getId());
        levelViewHolder.workoutProgressBar.setProgress(progress);


        levelViewHolder.workoutProgressBar.setVisibility(progress == 100 || progress == 0 ? View.GONE : View.VISIBLE);
        levelViewHolder.btnContinue.setVisibility(View.GONE);
        levelViewHolder.btnReset.setVisibility(View.GONE);
        if (progress == 100) {
            levelViewHolder.btnContinue.setVisibility(View.VISIBLE);
            levelViewHolder.btnReset.setVisibility(View.VISIBLE);
            levelViewHolder.btnContinue.setText(Translate.getValue("workout_done"));
            Drawable drawable = ContextCompat.getDrawable(
                    levelViewHolder.btnContinue.getContext(),
                    R.drawable.current_day_background
            );
            levelViewHolder.btnContinue.setBackground(drawable);
            levelViewHolder.btnContinue.setTextColor(ContextCompat.getColor(levelViewHolder.btnContinue.getContext(), R.color.white));
        } else if (progress > 0) {
            String progressText = progress + "% ";
            levelViewHolder.progressPercentage.setText(progressText);
            levelViewHolder.progressLabel.setText(Translate.getValue("continue"));

        } else {
            levelViewHolder.btnContinue.setVisibility(View.VISIBLE);
            levelViewHolder.btnContinue.setText(Translate.getValue("start_workout"));
        }
    }

    private int getProgressWorkout(String idWorkout) {
        return prefsUtilsWtContext.getIntegerPreferenceProfile(idWorkout + "_progress", 0);
    }

    private void showResetDialog(Context context, Workout workout) {
        final Dialog dialog = new Dialog(context, R.style.Theme_AppCompat_Light_Dialog_Alert);
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
            dialog.setContentView(R.layout.dialog_reset_workout);
            dialog.setCanceledOnTouchOutside(true);
            dialog.setCancelable(true);

            TextView dialogTitle = dialog.findViewById(R.id.dialog_title);
            TextView dialogMessage = dialog.findViewById(R.id.dialog_message);
            TextView btnYes = dialog.findViewById(R.id.btnYes);
            TextView btnNo = dialog.findViewById(R.id.btnNo);

            // Set translated texts
            dialogTitle.setText(Translate.getValue("reset_workout_progress"));
            dialogMessage.setText(Translate.getValue("are_you_sure_you_want_to_reset"));
            btnYes.setText(Translate.getValue("yes"));
            btnNo.setText(Translate.getValue("no"));

            btnYes.setOnClickListener(v -> {
                setVibrate(v);
                // Reset the workout progress
                prefsUtilsWtContext.setIntegerPreferenceProfile(workout.getId() + "_progress", 0);
                notifyDataSetChanged();
                dialog.dismiss();
            });

            btnNo.setOnClickListener(v -> {
                setVibrate(v);
                dialog.dismiss();
            });

            dialog.show();
        }
    }

    @Override

    public int getItemCount() {
        return listLevel.size();
    }

    public static class LevelViewHolder extends RecyclerView.ViewHolder {
        TextView workoutName;
        TextView levelLabel;
        TextView timeSpend;
        TextView labelKcal;
        TextView labelDescription;
        ProgressView workoutProgressBar;
        TextView progressLabel;
        Button btnShowExercises;
        RelativeLayout buttonBackground;
        TextView progressPercentage;
        Button btnContinue;
        ImageButton btnReset;
        LinearLayout llProgress;

        public LevelViewHolder(@NonNull ItemViewpagerInfoBinding binding) {
            super(binding.getRoot());
            workoutName = binding.tvWorkoutNumber;
            levelLabel = binding.levelLabel;
            timeSpend = binding.timeSpend;
            labelKcal = binding.labelKcal;
            labelDescription = binding.labelDescription;
            workoutProgressBar = binding.workoutProgressBar;
            progressLabel = binding.progressLabel;
            btnShowExercises = binding.btnShowExercises;
            buttonBackground = binding.rlContinue;
            progressPercentage = binding.progressPercentage;
            btnContinue = binding.btnContinue;
            btnReset = binding.btnReset;
            llProgress = binding.llProgress;
        }
    }
}
