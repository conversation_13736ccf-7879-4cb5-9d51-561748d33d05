package com.vgfit.shefit.fragment.userprofile;

import android.util.Log;

import androidx.annotation.NonNull;

import com.vgfit.shefit.BaseApplication;
import com.vgfit.shefit.BuildConfig;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.authorization.SecDevice;
import com.vgfit.shefit.authorization.Users;
import com.vgfit.shefit.fragment.userprofile.callback.UpdatedServerProfile;
import com.vgfit.shefit.util.Constant;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SaveProfileServer {
    private final PrefsUtilsWtContext prefsUtilsWtContext;
    private final UpdatedServerProfile updatedServerProfile;
    private static final String TAG = "SaveProfileServer";


    public SaveProfileServer(PrefsUtilsWtContext prefsUtilsWtContext, UpdatedServerProfile updatedServerProfile) {
        this.prefsUtilsWtContext = prefsUtilsWtContext;
        this.updatedServerProfile = updatedServerProfile;
    }

    public void saveFullProfileToServer(Users users) {
        logMessage( "saveFullProfileToServer");
        String token = prefsUtilsWtContext.getStringPreference(Constant.ACCESS_TOKEN);
        logMessage("Token==>" + token);
        BaseApplication.getApiFemale().updateProfileFull(token, users.getFirstName(), users.getGender(), users.getAge(), users.getWeight(), users.getWeightMeasureUnit(), users.getHeight(), users.getHeightMeasureUnit(), users.getExperience(), users.getTimezone(), users.getGoalWeight(), users.getWorkoutDays(), users.getFitnessGoal()).enqueue(new Callback<Users>() {
            @Override
            public void onResponse(@NonNull Call<Users> call, @NonNull Response<Users> response) {
                logMessage( "code==>" + response.code());
                if (response.code() == 200) {
                    if (response.body() != null) {
                        logMessage("Success Update saveFullProfileToServer");
                        prefsUtilsWtContext.setBooleanPreferenceProfile(Constant.KEY_PROFILE_UPDATED, true);
                        if (updatedServerProfile != null)
                            updatedServerProfile.updatedServerProfile(true);
                    }
                } else if (response.code() == 401) {
                    logMessage( "Error Token==>" + response.code());
                    SecDevice.getRefreshToken(prefsUtilsWtContext, token -> saveFullProfileToServer(users));
                } else if (response.code() == 400) {
                    try (okhttp3.Response rawResponse = response.raw()) {
                        logMessage("error 400 with==>" + rawResponse.request());
                    } catch (Exception e) {
                        logMessage("Error accessing raw response: " + e.getMessage());
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<Users> call, @NonNull Throwable t) {
                logMessage( "Error Update saveFullProfileToServer");
            }
        });
    }

    private void logMessage(String message) {
        if (BuildConfig.DEBUG)
            Log.d(TAG, message);
    }

}
