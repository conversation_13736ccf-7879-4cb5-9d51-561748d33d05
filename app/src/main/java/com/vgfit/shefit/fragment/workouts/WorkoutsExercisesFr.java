package com.vgfit.shefit.fragment.workouts;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;
import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.Manifest;
import android.app.Activity;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.FragmentWorkoutsExercisesBinding;
import com.vgfit.shefit.fragment.exercises.ExerciseDetailsFragment;
import com.vgfit.shefit.fragment.exercises.callbacks.ExerciseClicked;
import com.vgfit.shefit.fragment.workouts.adapter.AdapterAllExercises;
import com.vgfit.shefit.realm.Exercise;
import com.vgfit.shefit.realm.WorkoutExercise;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.Translate;

import java.util.ArrayList;
import java.util.Collections;

import io.realm.Realm;

public class WorkoutsExercisesFr extends Fragment implements ExerciseClicked {
    private FragmentWorkoutsExercisesBinding binding;
    Realm realm;
    ArrayList<WorkoutExercise> workoutExerciseList;
    String supersetName = "";
    String[] permissions;
    private int widthMetrics;
    private boolean isBadNetwork;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        isBadNetwork = false;
        Bundle bundle = this.getArguments();
        workoutExerciseList = new ArrayList<>();
        if (bundle != null) {
            workoutExerciseList = bundle.getParcelableArrayList("workout");
            supersetName = bundle.getString("supersetName");
        }

        realm = Realm.getDefaultInstance();

        Collections.sort(workoutExerciseList, (o1, o2) -> o1.getOrder() - o2.getOrder());
        getDataAboutNetwork();
        permissions = new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE};
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentWorkoutsExercisesBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        setStatusBar(view);
        RecyclerView recyclerView = view.findViewById(R.id.rv_exercises);
        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        AdapterAllExercises adapter = new AdapterAllExercises(workoutExerciseList, getContext(), this);
        recyclerView.setAdapter(adapter);
        binding.btnStartExercises.setEnabled(true);

        binding.tvSupersetName.setText(supersetName);
        binding.btnStartExercises.setOnClickListener(v -> {
            setVibrate(v);
            changeFragment();
            binding.btnStartExercises.setEnabled(false);
        });
        binding.btnStartExercises.setText(Translate.getValue("start_workout"));

        binding.backContainer.setOnClickListener(v -> {
            setVibrate(v);
            if (getContext() != null)
                ((Activity) getContext()).onBackPressed();
        });
    }

    private void getDataAboutNetwork() {
        isBadNetwork = Constant.isBadInternet;
    }

    private void changeFragment() {
        FragmentTransaction fragmentTransaction;
        fragmentTransaction = getParentFragmentManager().beginTransaction();
        fragmentTransaction.addToBackStack(null);
        WorkoutsVideoFr workoutsVideoFr = new WorkoutsVideoFr();
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList("workout", workoutExerciseList);
        workoutsVideoFr.setArguments(bundle);
        fragmentTransaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
        fragmentTransaction.replace(R.id.root_fragment, workoutsVideoFr);
        fragmentTransaction.commitAllowingStateLoss(); //not ".commit" 'cause Handling IllegalStateException: Can not perform this action after onSaveInstanceState
    }

    @Override
    public void itemClicked(Exercise exercise) {
        FragmentTransaction transaction;
        transaction = getParentFragmentManager().beginTransaction();
        Fragment fragment = ExerciseDetailsFragment.newInstance(exercise);
        transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
        transaction.replace(R.id.root_fragment, fragment).addToBackStack(null).commit();
    }

}