package com.vgfit.shefit.fragment.premium.redesign;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.vgfit.shefit.databinding.Part6SubscribeBinding;
import com.vgfit.shefit.fragment.premium.redesign.adapter.AdapterReviewItem;
import com.vgfit.shefit.fragment.premium.redesign.adapter.AdapterReviewLocation;
import com.vgfit.shefit.fragment.premium.redesign.callback.SelectedLocation;
import com.vgfit.shefit.fragment.premium.redesign.model.ReviewOfferModel;
import com.vgfit.shefit.util.Translate;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.util.ArrayList;


public class Part6_Fragment extends Fragment implements SelectedLocation {
    private Part6SubscribeBinding binding;
    ArrayList<ReviewOfferModel> listReviewOffer = new ArrayList<>();

    public static Part6_Fragment newInstance() {

        Bundle args = new Bundle();

        Part6_Fragment fragment = new Part6_Fragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getContext() != null)
            listReviewOffer = getReviewList(getContext());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = Part6SubscribeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        AdapterReviewLocation adapterReviewLocation = new AdapterReviewLocation(listReviewOffer, this);
        binding.listRegion.setAdapter(adapterReviewLocation);
        binding.topTitle.setText(Translate.getValue("what_do_our_users_say"));
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    private ArrayList<ReviewOfferModel> getReviewList(Context context) {
        Gson gson = new Gson();
        ArrayList<ReviewOfferModel> listReview = new ArrayList<>();
        try (InputStream stream = context.getAssets().open("json_files/user_reviews.json");
             InputStreamReader reader = new InputStreamReader(stream)) {
            Type type = new TypeToken<ArrayList<ReviewOfferModel>>() {
            }.getType();
            listReview.addAll(gson.fromJson(reader, type));
        } catch (IOException ignored) {
        }
        return listReview;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void selectedPosition(int position) {
        AdapterReviewItem adapterReviewItem = new AdapterReviewItem(listReviewOffer.get(position).getListReview());
        binding.listReview.setAdapter(adapterReviewItem);
    }
}
