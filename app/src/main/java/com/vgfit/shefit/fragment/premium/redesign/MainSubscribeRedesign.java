package com.vgfit.shefit.fragment.premium.redesign;

import static com.vgfit.shefit.util.screen.UtilsStatusBar.getStatusBarHeight;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.animation.AccelerateDecelerateInterpolator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.R;
import com.vgfit.shefit.StartScreen;
import com.vgfit.shefit.databinding.LayoutBigSubscribeBinding;
import com.vgfit.shefit.fragment.premium.BillingAndRemoteConfig;
import com.vgfit.shefit.fragment.premium.callback.PremiumPurchase;
import com.vgfit.shefit.fragment.premium.callback.Response_RestoreP;
import com.vgfit.shefit.fragment.premium.redesign.model.PurchasedProduct;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.RCUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class MainSubscribeRedesign extends Fragment implements Response_RestoreP, PremiumPurchase {
    private LayoutBigSubscribeBinding binding;
    private static String key_checkKMinimumFeature = "key_checkKMinimumFeature";
    private static String key_isMainPaywall = "key_isMainPaywall";
    private boolean checkKMinimumFeature;
    private boolean isMainPaywall;

    private final int scrollToPosition = 400;
    private boolean isValidAnimation = true;
    private int heightEndPart1 = 0;
    private int heightEndPart2 = 0;
    private int heightEndPart3 = 0;
    private int heightEndPart4 = 0;
    private int heightEndPart5 = 0;
    private int heightEndPart6 = 0;
    private int heightEndPart7 = 0;
    private int heightEndPart8 = 0;
    private View mainView;
    private boolean isMainActivity;

    public static MainSubscribeRedesign newInstance(boolean checkKMinimumFeature, boolean isMainPaywall) {

        Bundle args = new Bundle();
        MainSubscribeRedesign fragment = new MainSubscribeRedesign();
        args.putBoolean(key_checkKMinimumFeature, checkKMinimumFeature);
        args.putBoolean(key_isMainPaywall, isMainPaywall);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initActivity();
        RCUtils rcUtils = new RCUtils(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            checkKMinimumFeature = arg.getBoolean(key_checkKMinimumFeature, false);
            isMainPaywall = arg.getBoolean(key_isMainPaywall, false);
        }
        rcUtils.sendOfferEventAmplitude(checkKMinimumFeature, isMainPaywall);
    }

    private void initActivity() {
        Activity activity = (Activity) getContext();
        if (activity instanceof BillingAndRemoteConfig) {
            if (getActivity() != null) {
                ((BillingAndRemoteConfig) getActivity()).premiumPurchase = this;
            }
        } else {
            if (getActivity() != null) {
                ((MainActivity) getActivity()).premiumPurchase = this;
                isMainActivity = true;
            }
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = LayoutBigSubscribeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        view.setFocusableInTouchMode(true);
        view.requestFocus();
        mainView = view;
//        setStatusBar(view);
        ViewTreeObserver vto = binding.topMargin.getViewTreeObserver();
        vto.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (getContext() != null) {
                    binding.topMargin.getLayoutParams().height = getStatusBarHeight(getContext());
                    binding.topMargin.requestLayout();
                    int heightPart1 = binding.part1Fragment.getHeight();
                    int heightPart2 = binding.part2Fragment.getHeight();
                    int heightPart3 = binding.part3Fragment.getHeight();
                    int heightPart4 = binding.part4Fragment.getHeight();
                    int heightPart5 = binding.part5Fragment.getHeight();
                    int heightPart6 = binding.part6Fragment.getHeight();
                    int heightPart7 = binding.part7Fragment.getHeight();
                    int heightPart8 = binding.part8Fragment.getHeight();
                    heightEndPart1 = heightPart1;
                    heightEndPart2 = heightEndPart1 + heightPart2;
                    heightEndPart3 = heightEndPart2 + heightPart3;
                    heightEndPart4 = heightEndPart3 + heightPart4;
                    heightEndPart5 = heightEndPart4 + heightPart5;
                    heightEndPart6 = heightEndPart5 + heightPart6;
                    heightEndPart7 = heightEndPart6 + heightPart7;
                    heightEndPart8 = heightEndPart7 + heightPart8;


                }
                view.getViewTreeObserver().removeGlobalOnLayoutListener(this);
            }
        });
//        setStatusBar(topMargin);
        Part1_Fragment fragmentC = Part1_Fragment.newInstance(false, checkKMinimumFeature);
        FragmentTransaction transaction = getChildFragmentManager().beginTransaction();
        transaction.replace(R.id.part1_Fragment, fragmentC).addToBackStack(null).commit();

        Part2_Fragment fragmentA = Part2_Fragment.newInstance();
        FragmentTransaction transactionA = getChildFragmentManager().beginTransaction();
        transactionA.replace(R.id.part2_Fragment, fragmentA).addToBackStack(null).commit();

        Part3_Fragment fragmentF = Part3_Fragment.newInstance();
        FragmentTransaction transactionF = getChildFragmentManager().beginTransaction();
        transactionF.replace(R.id.part3_Fragment, fragmentF).addToBackStack(null).commit();

        Part4_Fragment fragmentE = Part4_Fragment.newInstance();
        FragmentTransaction transactionE = getChildFragmentManager().beginTransaction();
        transactionE.replace(R.id.part4_Fragment, fragmentE).addToBackStack(null).commit();

        Part5_Fragment fragmentG = Part5_Fragment.newInstance();
        FragmentTransaction transactionG = getChildFragmentManager().beginTransaction();
        transactionG.replace(R.id.part5_Fragment, fragmentG).addToBackStack(null).commit();

        Part6_Fragment fragmentH = Part6_Fragment.newInstance();
        FragmentTransaction transactionH = getChildFragmentManager().beginTransaction();
        transactionH.replace(R.id.part6_Fragment, fragmentH).addToBackStack(null).commit();

        Part7_Fragment fragmentD = Part7_Fragment.newInstance();
        FragmentTransaction transactionD = getChildFragmentManager().beginTransaction();
        transactionD.replace(R.id.part7_Fragment, fragmentD).addToBackStack(null).commit();

        Part8_Fragment fragmentB = Part8_Fragment.newInstance(false);
        FragmentTransaction transactionB = getChildFragmentManager().beginTransaction();
        transactionB.replace(R.id.part8_Fragment, fragmentB).addToBackStack(null).commit();

        Part9_Fragment fragmentI = Part9_Fragment.newInstance();
        FragmentTransaction transactionI = getChildFragmentManager().beginTransaction();
        transactionI.replace(R.id.part9_Fragment, fragmentI).addToBackStack(null).commit();

        binding.nestedScrollView.setOnScrollChangeListener((View.OnScrollChangeListener) (v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            if (scrollY > 200 && isValidAnimation) {
                isValidAnimation = false;
            }
        });
        animateScroll();
    }

    private void animateScroll() {
        long animationDuration = 1000;
        Handler handler = new Handler();
        handler.postDelayed(() -> {
            if (isValidAnimation) {
                ObjectAnimator animatorUP = ObjectAnimator.ofFloat(this, "phase", 1.0f, 0.0f);
                animatorUP.setDuration(animationDuration);
                animatorUP.setInterpolator(new AccelerateDecelerateInterpolator());

                ObjectAnimator animatorDown = ObjectAnimator.ofFloat(this, "phase", 0.0f, 1.0f);
                animatorDown.setDuration(animationDuration);
                animatorDown.setInterpolator(new AccelerateDecelerateInterpolator());
                animatorDown.addListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(@NonNull Animator animation) {

                    }

                    @Override
                    public void onAnimationEnd(@NonNull Animator animation) {
                        animatorUP.start();
                    }

                    @Override
                    public void onAnimationCancel(@NonNull Animator animation) {

                    }

                    @Override
                    public void onAnimationRepeat(@NonNull Animator animation) {

                    }
                });
                animatorDown.start();
            }
        }, 4_000);
    }

    public void setPhase(float phase) {
        try {
            if (binding.nestedScrollView != null) {
                float percent = scrollToPosition * phase;
                binding.nestedScrollView.smoothScrollTo(0, (int) percent);
            }
        } catch (Exception ignored) {
        }

    }


    public void onStart() {
        super.onStart();
        try {
            EventBus.getDefault().register(this);
        } catch (Exception ignored) {
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        try {
            EventBus.getDefault().unregister(this);
        } catch (Exception ignored) {
        }

    }

    @Override
    public void onResume() {
        super.onResume();
        mainView.setOnKeyListener((v, keyCode, event) -> {
            closeOffer();
            // pass on to be processed as normal
            return keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN; // pretend we've processed it
        });
        if (Constant.premium) {
            closeOffer();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();


    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPurchased(PurchasedProduct event) {
        Log.d("TestClose", "Close purchase after subscribe");
        closeOffer();

    }

    @Override
    public void isPurchased(Boolean output) {
        if (Constant.premium) {
            closeOffer();
        }
    }

    @Override
    public void processFinishRestore(Boolean output) {
        if (Constant.premium) {
            closeOffer();
        }
    }

    private void closeOffer() {
        try {
            if (getActivity() != null) {
                Log.d("TestClose", "Close Offer Main");
                if (isMainActivity)
                    getActivity().onBackPressed();
                else
                    ((StartScreen) getActivity()).closeOfferFromOnBoarding();
//
            }
        } catch (Exception ignored) {
        }

    }
}
