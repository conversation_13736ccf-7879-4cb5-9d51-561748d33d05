package com.vgfit.shefit.fragment.nutrition.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.nostra13.universalimageloader.core.ImageLoader;
import com.vgfit.shefit.databinding.ItemCoverNutritionPlanBinding;
import com.vgfit.shefit.fragment.nutrition.callbacks.NutrItemClick;
import com.vgfit.shefit.realm.Meal;
import com.vgfit.shefit.realm.MealByDay;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.screen.ImageUtils;
import com.vgfit.shefit.util.text.TextTwoRow;

import java.util.ArrayList;

public class AdapterScrollHorizontal extends RecyclerView.Adapter<AdapterScrollHorizontal.NutritionViewHolder> {
    private final ArrayList<MealByDay> listIdDay;
    private ArrayList<String> listMealManifest;
    private final NutrItemClick nutrItemClick;

    public AdapterScrollHorizontal(ArrayList<MealByDay> listIdDay, NutrItemClick nutrItemClick) {
        this.listIdDay = listIdDay;
        this.nutrItemClick = nutrItemClick;
        listMealManifest = new ArrayList<>();
        setListMealManifest();
    }

    @NonNull
    @Override
    public NutritionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ItemCoverNutritionPlanBinding binding = ItemCoverNutritionPlanBinding.inflate(inflater, parent, false);
        return new NutritionViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull NutritionViewHolder holder, int position) {
        Meal meal = listIdDay.get(position).getMeal();
        ImageLoader.getInstance().displayImage(meal.getVerticalImage(), holder.imageNutrition, ImageUtils.getDefaultDisplayImageOptions(), ImageUtils.AnimateFirstDisplayListener);
        String nameMeal = "";
        if (position < listMealManifest.size())
            nameMeal = listMealManifest.get(position);
        TextTwoRow.setText(holder.titleNutrition, holder.titleNutritionSecond, nameMeal);
        String shortDesc = Translate.getValue(meal.getName());
        holder.shortNutrition.setText(shortDesc);
        String kcal = meal.getCalories() + " " + Translate.getValue("kcal");
        String min = meal.getTimeToCook() + " " + Translate.getValue("min");
        holder.calInfo.setText(kcal);
        holder.timeInfo.setText(min);

        holder.openNutrition.setOnClickListener(v -> {
            ViewCompat.setTransitionName(holder.imageNutrition, meal.getVerticalImage());
            ViewCompat.setTransitionName(holder.shortNutrition, shortDesc);
            ViewCompat.setTransitionName(holder.calInfo, kcal);
            ViewCompat.setTransitionName(holder.timeInfo, min);
            ViewCompat.setTransitionName(holder.containerInfoMeal, shortDesc + kcal + min);
            ViewCompat.setTransitionName(holder.view2, shortDesc + kcal + min + meal.getVerticalImage());
            nutrItemClick.onItemClick(meal, holder.imageNutrition, holder.shortNutrition, holder.calInfo, holder.timeInfo, holder.containerInfoMeal, holder.view2);
        });
    }

    private void setListMealManifest() {
        listMealManifest = new ArrayList<>();
        listMealManifest.add(Translate.getValue("breakfast"));
        listMealManifest.add(Translate.getValue("morning_snack"));
        listMealManifest.add(Translate.getValue("lunch"));
        listMealManifest.add(Translate.getValue("afternoon_snack"));
        listMealManifest.add(Translate.getValue("dinner"));
    }

    @Override
    public int getItemCount() {
        return listIdDay.size();
    }

    public static class NutritionViewHolder extends RecyclerView.ViewHolder {

        ImageView imageNutrition;
        TextView titleNutrition;
        TextView titleNutritionSecond;
        TextView shortNutrition;
        TextView calInfo;
        TextView timeInfo;
        RelativeLayout openNutrition;
        LinearLayout containerInfoMeal;
        View view2;

        public NutritionViewHolder(@NonNull ItemCoverNutritionPlanBinding binding) {
            super(binding.getRoot());
            imageNutrition = binding.imageNutrition;
            titleNutrition = binding.titleNutrition;
            titleNutritionSecond = binding.titleNutritionSecond;
            shortNutrition = binding.shortNutrition;
            calInfo = binding.calInfo;
            timeInfo = binding.timeInfo;
            openNutrition = binding.itemNutritionPlan;
            containerInfoMeal = binding.containerInfoMeal;
            view2 = binding.view2;
        }
    }

}
