package com.vgfit.shefit.fragment.personal_plan.structure;

import com.vgfit.shefit.fragment.personal_plan.model.DayOfWeek;
import com.vgfit.shefit.fragment.personal_plan.model.OneDayData;
import com.vgfit.shefit.realm.Workout;

import java.util.ArrayList;

public interface PersonalPlanView {

    void planWeek(ArrayList<OneDayData> listWeekPlan, int currentDay);

    void openListExerciseWorkout(Workout workout, String supersetName, Long timeLong);

    void currentDay(int pos);

    void currentDayName(String currentDayName);

    void daysCalendar(ArrayList<DayOfWeek> dayOfWeeks);

    void setImageWeather(String drawableImage);

    void startAnimRequest();

    void setTempWeather(String tempWeather);

    void daySelected(int position);
    void setHideWeather();
}
