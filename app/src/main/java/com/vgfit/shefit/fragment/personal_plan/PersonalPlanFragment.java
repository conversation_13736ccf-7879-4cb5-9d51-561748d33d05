package com.vgfit.shefit.fragment.personal_plan;

import static com.vgfit.shefit.util.TextUtils.convertDpToPx;
import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.Manifest;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.location.LocationManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.bumptech.glide.Glide;
import com.nabinbhandari.android.permissions.PermissionHandler;
import com.nabinbhandari.android.permissions.Permissions;
import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.PlansWorkoutBinding;
import com.vgfit.shefit.fragment.nutrition.NutritionDayFr;
import com.vgfit.shefit.fragment.personal_plan.adapter.AdapterWeekDays;
import com.vgfit.shefit.fragment.personal_plan.callbacks.Dayclicked;
import com.vgfit.shefit.fragment.personal_plan.callbacks.PlayerSizeView;
import com.vgfit.shefit.fragment.personal_plan.model.DayOfWeek;
import com.vgfit.shefit.fragment.personal_plan.model.OneDayData;
import com.vgfit.shefit.fragment.personal_plan.serviceWeather.GPSTracker;
import com.vgfit.shefit.fragment.personal_plan.structure.PersonalPlanPresenter;
import com.vgfit.shefit.fragment.personal_plan.structure.PersonalPlanView;
import com.vgfit.shefit.MenuBottom;
import com.vgfit.shefit.fragment.workouts.WorkoutsExercisesFr;
import com.vgfit.shefit.realm.Workout;
import com.vgfit.shefit.util.AnimatorView;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.RCUtils;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.animation.DynamicIsland;
import com.vgfit.shefit.util.text.TextTwoRow;

import java.util.ArrayList;
import java.util.Arrays;

import me.everything.android.ui.overscroll.OverScrollDecoratorHelper;

public class PersonalPlanFragment extends Fragment implements PersonalPlanView, Dayclicked, PlayerSizeView {
    private PlansWorkoutBinding binding;
    //    @BindView(R.id.recyclerWeekDay)
//    RecyclerView recyclerWeekDay;
//    @BindView(R.id.coverPhotoMeal)
//    ImageView coverPhotoMeal;
//
//    @BindView(R.id.imagePlan)
//    ImageView imagePlan;
//    @BindView(R.id.titleWorkout)
//    TextView titleWorkout;
//    @BindView(R.id.titleWorkoutSecond)
//    TextView titleWorkoutSecond;
//    @BindView(R.id.titleMeal)
//    TextView titleMeal;
//    @BindView(R.id.calInfo)
//    TextView calInfo;
//    @BindView(R.id.timeInfo)
//    TextView timeInfo;
//    @BindView(R.id.itemWorkout)
//    RelativeLayout itemWorkout;
//    @BindView(R.id.itemMeal)
//    RelativeLayout itemMeal;
//    @BindView(R.id.cardView)
//    CardView cardView;
//    @BindView(R.id.cardViewMeal)
//    CardView cardViewMeal;
//
//    @BindView(R.id.menuButton)
//    ImageButton menuButton;
//    @BindView(R.id.imageWeather)
//    ImageView imageViewWeather;
//
//    @BindView(R.id.tempWeather)
//    TextView tempWeatherLabel;
//    @BindView(R.id.currentDay)
//    TextView currentDayLabel;
//
//    @BindView(R.id.arrowStart)
//    ImageView arrowStart;
//
//    @BindView(R.id.mainCContainer)
//    LinearLayout mainCContainer;
//    @BindView(R.id.dayCurrentContainer)
//    RelativeLayout dayCurrentContainer;
//    @BindView(R.id.dayCurrentInfo)
//    TextView dayCurrentInfo;
//    @BindView(R.id.dayCurrent)
//    TextView dayCurrent;
//    @BindView(R.id.titleRest)
//    TextView titleRest;
//    @BindView(R.id.titleRestSecond)
//    TextView titleRestSecond;
//    @BindView(R.id.containerWork)
//    RelativeLayout containerWork;
//    @BindView(R.id.containerRest)
//    RelativeLayout containerRest;
//    @BindView(R.id.containerMenu)
//    RelativeLayout containerMenu;
    private PersonalPlanPresenter personalPlanPresenter;
    private AdapterWeekDays adapterWeekDays;
    private Context context;
    private int posSelDayUser = 0;
    private final boolean isFirstLunch = true;
    private String nameMeal = "";
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private final Handler handler = new Handler();
    private boolean isVisbileAnimate = true;
    private AnimatorView animatorView;
    private final String KEY_COUNT_WEATHER = "KEY_COUNT_WEATHER";
    private final String KEY_COUNT_DENIED_PERMISSION = "KEY_COUNT_DENIED_PERMISSION";
    private int resWeather;
    private int countDeniedPermission = 0;
    private String tempWeather = "-100";
    private String currentDayName;
    private DynamicIsland dynamicIsland;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        context = getContext();
        prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        personalPlanPresenter = new PersonalPlanPresenter(this, prefsUtilsWtContext);
        if (isFirstLunch) personalPlanPresenter.getCurrentDay();
        animatorView = new AnimatorView();
        animatorView.initScaleUpAlpha();
        animatorView.initScaleDownAlpha();
        resWeather = context.getResources().getIdentifier("ic_weather_off", "drawable", context.getPackageName());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = PlansWorkoutBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        dynamicIsland = new DynamicIsland(context, binding.mainCContainer, binding.dayCurrentInfo);
        setStatusBar(view);
        if (prefsUtilsWtContext.getIntegerPreferenceProfile(KEY_COUNT_DENIED_PERMISSION, 0) <= 1 && validWeatherPermission())
            doWeather(true);
        binding.top.menuButton.setOnClickListener(v -> {
            if (getActivity() != null) ((MainActivity) getActivity()).openDrawer();
        });
        binding.top.menuButton.setVisibility(View.INVISIBLE);
//        nameFragment.setText(Translate.getValue("personal_plan"));
        adapterWeekDays = new AdapterWeekDays(new ArrayList<>(), this);
        binding.recyclerWeekDay.setAdapter(adapterWeekDays);
        binding.recyclerWeekDay.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false));
        ((SimpleItemAnimator) binding.recyclerWeekDay.getItemAnimator()).setSupportsChangeAnimations(false);
        OverScrollDecoratorHelper.setUpOverScroll(binding.recyclerWeekDay, OverScrollDecoratorHelper.ORIENTATION_HORIZONTAL);

        binding.coverPlan.itemWorkout.setOnClickListener(v -> {
            boolean oneTimeSeeWork = prefsUtilsWtContext.getBooleanPreferenceProfile(Constant.KEY_FINISHED_WORKOUT, false);
            if (Constant.premium || !oneTimeSeeWork) {
                if (adapterWeekDays.isWorkout())
                    personalPlanPresenter.castToNormalWorkout(adapterWeekDays.getOneDayData());
            } else openMainSubscribe();
        });
        binding.coverMeal.itemMeal.setOnClickListener(v -> {
            if (Constant.premium) {
                FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
                Fragment fragment = NutritionDayFr.newInstance(posSelDayUser, nameMeal);
                transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
                transaction.replace(R.id.root_fragment, fragment).addToBackStack(null).commit();
            } else openMainSubscribe();
        });
        personalPlanPresenter.getDailyWorkout();
        binding.imageWeather.setOnClickListener(v -> {
            int resWeather = context.getResources().getIdentifier("ic_weather_off", "drawable", context.getPackageName());
            binding.tempWeather.setVisibility(View.GONE);
            if (binding.imageWeather != null) binding.imageWeather.setBackgroundResource(resWeather);
            doWeather(true);
        });
        binding.tempWeather.setText(tempWeather.equals("-100") ? "0" : tempWeather);
        binding.tempWeather.setVisibility(tempWeather.equals("-100") ? View.GONE : View.VISIBLE);
        binding.imageWeather.setBackgroundResource(resWeather);
        binding.currentDay.setText(currentDayName);
        new MenuBottom((MainActivity) getActivity(), binding.containerMenu);
    }

    @Override
    public void onStop() {
        super.onStop();
    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    public void onResume() {
        super.onResume();

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void planWeek(ArrayList<OneDayData> listWeekPlan, int currentDay) {
        int pos = posSelDayUser != -1 ? posSelDayUser : currentDay;
        if (adapterWeekDays != null) {
            adapterWeekDays.setListWeekData(listWeekPlan, pos);
            initializePlayer(listWeekPlan);
            dayData(adapterWeekDays.getOneDayData(), pos, adapterWeekDays.getBackground(pos));
//            animateScroll(pos);
        }
    }

    private void animateScroll(int daySelected) {
        int posN = daySelected + 3;
        posN = Math.min(posN, 6);
        try {
            int finalPosN = posN;
            handler.postDelayed(() -> {
                if (isVisbileAnimate) {
                    adapterWeekDays.selectDay(daySelected);
                    binding.recyclerWeekDay.smoothScrollToPosition(finalPosN);
//                    isVisbileAnimate = false;
                }
            }, 0);
        } catch (Exception ignored) {
        }
    }

    private void initializePlayer(ArrayList<OneDayData> listWeekPlan) {
    }

    @Override
    public void dayData(OneDayData oneDayData, int posDay, Integer drawable) {
        binding.dayCurrent.setText(oneDayData.getDayOfWeek().getNameShort());
        posSelDayUser = posDay;
        setImage(oneDayData.getCoverDayPlan().getOrder(), drawable);
        setTextItems(oneDayData, posDay);
        if (!oneDayData.isShowDay()) {
            ColorMatrix matrix = new ColorMatrix();
            matrix.setSaturation(0);
            binding.coverPlan.imagePlan.setColorFilter(new ColorMatrixColorFilter(matrix));
            binding.dayCurrentContainer.setBackgroundResource(R.drawable.current_item_background_);
            binding.dayCurrentInfo.setText(Translate.getValueLine("keep_calm_and_rest", Arrays.asList(2), false));
        } else {
            binding.coverPlan.imagePlan.clearColorFilter();
            binding.dayCurrentContainer.setBackgroundResource(R.drawable.current_item_background);
            binding.dayCurrentInfo.setText(Translate.getValueLine("it’s_time_for_sport!", Arrays.asList(2), false));
        }
        binding.coverPlan.arrowStart.setVisibility(oneDayData.isShowDay() ? View.VISIBLE : View.GONE);
        binding.coverPlan.containerWork.setVisibility(oneDayData.isShowDay() ? View.VISIBLE : View.GONE);
        binding.coverPlan.containerRest.setVisibility(oneDayData.isShowDay() ? View.GONE : View.VISIBLE);
        animateScroll(posDay);
//        dynamicIsland.startAnim(() -> {
//        });
    }

    private void setTextItems(OneDayData oneDayData, int posDay) {
        TextTwoRow.setText(binding.coverPlan.titleWorkout, binding.coverPlan.titleWorkoutSecond, oneDayData.getDayOfWeek().getLongName());
        TextTwoRow.setText(binding.coverPlan.titleRest, binding.coverPlan.titleRestSecond, oneDayData.getDayOfWeek().getLongName());
        binding.coverMeal.titleMeal.setText(oneDayData.getDayOfWeek().getNameMeal());
        binding.coverPlan.calInfo.setText(!oneDayData.isShowDay() ? "" : oneDayData.getDayPlan().getRecomandations());
        binding.coverPlan.timeInfo.setText(!oneDayData.isShowDay() ? "" : oneDayData.getDayPlan().getWarmUpDescription());
        binding.coverPlan.calInfo.setVisibility(!oneDayData.isShowDay() ? View.INVISIBLE : View.VISIBLE);
        binding.coverPlan.timeInfo.setVisibility(!oneDayData.isShowDay() ? View.INVISIBLE : View.VISIBLE);
        nameMeal = oneDayData.getDayOfWeek().getNameMeal();
    }

    private void setImage(int order, int drawable) {
        animatorView.startScaleDownAlpha(binding.coverPlan.imagePlan);
//        animatorView.startScaleDownAlpha(playerView);
        Glide.with(this).load(Uri.parse("file:///android_asset/imageDay/" + order + ".webp")).centerCrop().into(binding.coverPlan.imagePlan);
        binding.coverMeal.coverPhotoMeal.setBackgroundResource(drawable);

    }


    @Override
    public void openListExerciseWorkout(Workout workout, String supersetName, Long timeLong) {
        FragmentTransaction fragmentTransaction;
        if (workout != null) {
            fragmentTransaction = getParentFragmentManager().beginTransaction();
            fragmentTransaction.addToBackStack(null);
            WorkoutsExercisesFr workoutsExercisesFr = new WorkoutsExercisesFr();
            Bundle bundle = new Bundle();
            bundle.putParcelableArrayList("workout", new ArrayList<>(workout.getWorkoutExercises()));
            bundle.putString("supersetName", supersetName);
            workoutsExercisesFr.setArguments(bundle);
            fragmentTransaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            fragmentTransaction.replace(R.id.root_fragment, workoutsExercisesFr);
            fragmentTransaction.commit();

        }
    }

    private void openMainSubscribe() {
        RCUtils rcUtils = new RCUtils(getContext());
        rcUtils.getMainPaywallAndroidOffer(this);
    }

    @Override
    public void currentDay(int pos) {
        posSelDayUser = pos;
    }

    @Override
    public void currentDayName(String currentDayName) {
        this.currentDayName = currentDayName;
    }

    @Override
    public void daysCalendar(ArrayList<DayOfWeek> dayOfWeeks) {

    }

    @Override
    public void setImageWeather(String imageWeather) {
        this.resWeather = context.getResources().getIdentifier(imageWeather, "drawable", context.getPackageName());
        if (binding.imageWeather != null) binding.imageWeather.setBackgroundResource(resWeather);
        Log.e("TestImageWeather", "image weather-->" + imageWeather + " resWeather-->" + resWeather);
    }

    @Override
    public void startAnimRequest() {

    }

    @Override
    public void setTempWeather(String tempWeather) {
        if (binding.tempWeather != null) {
            this.tempWeather = tempWeather;
            binding.tempWeather.setText(tempWeather);
            binding.tempWeather.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void daySelected(int position) {

    }

    @Override
    public void setHideWeather() {

    }

    @Override
    public void onSizeVideo(int sizeHeight, int sizeWidth) {
        DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();
        int sizeWeightDisplay = displayMetrics.widthPixels - convertDpToPx(context, 10);

        ViewGroup.LayoutParams layoutParams = binding.coverPlan.cardView.getLayoutParams();
        if (sizeWeightDisplay >= sizeWidth) {
            layoutParams.height = sizeHeight - convertDpToPx(context, 10);
            layoutParams.width = sizeWidth - convertDpToPx(context, 10);
        } else layoutParams.height = sizeWeightDisplay - convertDpToPx(context, 70);
        binding.coverPlan.cardView.setLayoutParams(layoutParams);
        binding.coverMeal.cardViewMeal.setLayoutParams(layoutParams);
    }

    private void buildAlertMessageNoGps() {
        final AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setMessage("Your GPS seems to be disabled, do you want to enable it?").setCancelable(false).setPositiveButton("Yes", new DialogInterface.OnClickListener() {
            public void onClick(final DialogInterface dialog, final int id) {
                startActivity(new Intent(android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS));
            }
        }).setNegativeButton("No", new DialogInterface.OnClickListener() {
            public void onClick(final DialogInterface dialog, final int id) {
                dialog.cancel();
            }
        });
        final AlertDialog alert = builder.create();
        alert.show();
    }

    private boolean validWeatherPermission() {
        int count = prefsUtilsWtContext.getIntegerPreferenceProfile(KEY_COUNT_WEATHER, 0);
        if (count > 0) {
            return true;
        } else {
            count++;
            prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_COUNT_WEATHER, count);
            return false;
        }
    }

    public void doWeather(boolean isClick) {

        String[] permissions = {Manifest.permission.ACCESS_COARSE_LOCATION};
        Permissions.check(getActivity(), permissions, Translate.getValue("allow_geolocation_access")/*rationale*/, null/*options*/, new PermissionHandler() {
            @Override
            public void onGranted() {
                prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_COUNT_DENIED_PERMISSION, 0);
                if (isClick) {
                    statusCheck();
                } else locationInit();
            }

            @Override
            public void onDenied(Context context, ArrayList<String> deniedPermissions) {
                super.onDenied(context, deniedPermissions);
                countDeniedPermission = prefsUtilsWtContext.getIntegerPreferenceProfile(KEY_COUNT_DENIED_PERMISSION, 0) + 1;
                Log.e("CountDeniedPermission", "countDeniedPermission==>" + countDeniedPermission);
                prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_COUNT_DENIED_PERMISSION, countDeniedPermission);
            }
        });
    }

    public void statusCheck() {
        final LocationManager manager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);

        if (!manager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
            buildAlertMessageNoGps();
        } else {
            locationInit();
        }
    }

    private void locationInit() {
        GPSTracker gpsTracker = null;
        try {
            gpsTracker = new GPSTracker(getActivity());
        } catch (Exception ignored) {
        }

        if (personalPlanPresenter != null && gpsTracker != null && binding.imageWeather != null) {
            if (gpsTracker.getLongitude() != 0) binding.imageWeather.setEnabled(false);
            personalPlanPresenter.getWeather(gpsTracker.getLatitude(), gpsTracker.getLongitude());
        }
    }
}
