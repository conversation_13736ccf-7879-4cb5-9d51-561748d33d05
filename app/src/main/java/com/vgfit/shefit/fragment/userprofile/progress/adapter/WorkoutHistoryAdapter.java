package com.vgfit.shefit.fragment.userprofile.progress.adapter;

import android.content.Context;
import android.net.Uri;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.ItemWorkoutHistoryBinding;
import com.vgfit.shefit.realm.Superset;
import com.vgfit.shefit.realm.WorkoutHistory;
import com.vgfit.shefit.util.Translate;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import io.realm.Realm;

public class WorkoutHistoryAdapter extends RecyclerView.Adapter<WorkoutHistoryAdapter.ViewHolder> {

    private final Context context;
    private List<WorkoutHistory> workoutHistoryList;
    private final SimpleDateFormat dateFormat;
    private final SimpleDateFormat timeFormat;
    private static final String WEBP = ".webp";

    private final Map<String, Integer> supersetOrderCache = new HashMap<>();

    public WorkoutHistoryAdapter(Context context, List<WorkoutHistory> workoutHistoryList) {
        this.context = context;
        this.workoutHistoryList = workoutHistoryList;
        this.dateFormat = new SimpleDateFormat("dd MMM", Locale.getDefault());
        this.timeFormat = new SimpleDateFormat("HH:mm", Locale.getDefault());
    }

    public void updateData(List<WorkoutHistory> newWorkoutHistoryList) {
        if (this.workoutHistoryList == null || this.workoutHistoryList.isEmpty()) {
            this.workoutHistoryList = newWorkoutHistoryList;
            notifyDataSetChanged();
            return;
        }

        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new WorkoutHistoryDiffCallback(
                this.workoutHistoryList, newWorkoutHistoryList));

        this.workoutHistoryList = newWorkoutHistoryList;
        diffResult.dispatchUpdatesTo(this);

    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemWorkoutHistoryBinding binding = ItemWorkoutHistoryBinding.inflate(
                LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        WorkoutHistory workout = workoutHistoryList.get(position);
        String nameWorkout = Translate.getValue(workout.getWorkoutName());
        if (nameWorkout.isEmpty())
            nameWorkout = workout.getWorkoutName();
        holder.binding.workoutNameText.setText(nameWorkout);
        holder.binding.workoutTimeText.setText(timeFormat.format(workout.getCompletedDate()));
        int durationInMinutes = workout.getDuration() / 60;
        String time = durationInMinutes + " " + Translate.getValue("min");
        holder.binding.workoutDurationText.setText(time);
        holder.binding.workoutCaloriesText.setText(String.valueOf(workout.getKcalBurned()));
        holder.binding.workoutDateText.setText(dateFormat.format(workout.getCompletedDate()));

        if (workout.getWorkoutType().equals(WorkoutHistory.TYPE_DAYPLAN)) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(workout.getCompletedDate());
            int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);

            Glide.with(holder.itemView.getContext())
                    .load(Uri.parse("file:///android_asset/imageDay/" + dayOfMonth + WEBP))
                    .transform(new CenterCrop(), new RoundedCorners(24))
                    .into(holder.binding.workoutImage);
        } else if (workout.getWorkoutType().equals(WorkoutHistory.TYPE_SUPERSET)) {
            int supersetOrder = getSupersetOrderById(workout.getWorkoutId());
            if (supersetOrder > 0) {
                Glide.with(holder.itemView.getContext())
                        .load(Uri.parse("file:///android_asset/imageWidget/superset_" + supersetOrder + WEBP))
                        .transform(new CenterCrop(), new RoundedCorners(24))
                        .into(holder.binding.workoutImage);
            }
        }
        switch (workout.getWorkoutLevel()) {
            case "beginner":
                holder.binding.difficultyIndicator.setText(Translate.getValue("beginner_level_name"));
                holder.binding.indicatorLevel.setImageResource(R.drawable.difficulty_beginner);
                break;
            case "be_more_active":
            case "tone_my_body":
            case "intermediate":
                holder.binding.difficultyIndicator.setText(Translate.getValue("intermediate_level_name"));
                holder.binding.indicatorLevel.setImageResource(R.drawable.difficulty_intermediate);
                break;
            case "lose_weight":
            case "gain_muscles":
            case "advanced":
                holder.binding.difficultyIndicator.setText(Translate.getValue("advanced_level_name"));
                holder.binding.indicatorLevel.setImageResource(R.drawable.difficulty_advanced);
                break;
            default:
                holder.binding.difficultyIndicator.setText("");
                holder.binding.indicatorLevel.setImageResource(0);
                break;
        }
        // Set completion status
        holder.binding.completionStatus.setVisibility(View.VISIBLE);
        holder.binding.verticalProgressBar.setProgress(workout.getProgressPercentage());
        holder.binding.workoutCaloriesUnit.setText(Translate.getValue("kcal").toLowerCase());
        if (workout.getProgressPercentage() == 100) {
            holder.binding.completionStatus.setVisibility(View.VISIBLE);
            holder.binding.progressPercentage.setVisibility(View.GONE);
            holder.binding.verticalProgressBar.setVisibility(View.GONE);
            holder.binding.completionStatusText.setText(Translate.getValue("passed"));
        } else {
            holder.binding.completionStatus.setVisibility(View.INVISIBLE);
            holder.binding.progressPercentage.setVisibility(View.VISIBLE);
            holder.binding.verticalProgressBar.setVisibility(View.VISIBLE);
            String progressText = workout.getProgressPercentage() + "%";
            holder.binding.progressPercentage.setText(progressText);
            holder.binding.completionStatusText.setText(Translate.getValue("in_progress"));
        }
        holder.binding.durationUnit.setText(Translate.getValue("duration").toLowerCase());

    }


    private int getSupersetOrderById(String workoutId) {
        // Verify cache first
        Integer cachedOrder = supersetOrderCache.get(workoutId);
        if (cachedOrder != null) {
            return cachedOrder;
        }

        try (Realm realm = Realm.getDefaultInstance()) {
            Superset superset = realm.where(Superset.class)
                    .equalTo("workouts.id", workoutId)
                    .findFirst();
            int order = superset != null ? superset.getOrder() : 0;

            supersetOrderCache.put(workoutId, order);
            return order;
        } catch (Exception e) {
            Log.e("WorkoutHistoryAdapter", "Error getting superset order: " + e.getMessage());
            supersetOrderCache.put(workoutId, 0);
            return 0;
        }
    }

    @Override
    public int getItemCount() {
        return workoutHistoryList != null ? workoutHistoryList.size() : 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemWorkoutHistoryBinding binding;

        public ViewHolder(ItemWorkoutHistoryBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    /**
     * DiffUtil callback pentru actualizări eficiente ale RecyclerView
     */
    private static class WorkoutHistoryDiffCallback extends DiffUtil.Callback {
        private final List<WorkoutHistory> oldList;
        private final List<WorkoutHistory> newList;

        public WorkoutHistoryDiffCallback(List<WorkoutHistory> oldList, List<WorkoutHistory> newList) {
            this.oldList = oldList;
            this.newList = newList;
        }

        @Override
        public int getOldListSize() {
            return oldList != null ? oldList.size() : 0;
        }

        @Override
        public int getNewListSize() {
            return newList != null ? newList.size() : 0;
        }

        @Override
        public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
            WorkoutHistory oldItem = oldList.get(oldItemPosition);
            WorkoutHistory newItem = newList.get(newItemPosition);
            return oldItem.getId().equals(newItem.getId());
        }

        @Override
        public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
            WorkoutHistory oldItem = oldList.get(oldItemPosition);
            WorkoutHistory newItem = newList.get(newItemPosition);

            return oldItem.getProgressPercentage() == newItem.getProgressPercentage() &&
                    oldItem.getDuration() == newItem.getDuration() &&
                    oldItem.getKcalBurned() == newItem.getKcalBurned() &&
                    oldItem.getCompletedDate().equals(newItem.getCompletedDate()) &&
                    oldItem.getWorkoutName().equals(newItem.getWorkoutName()) &&
                    oldItem.getWorkoutLevel().equals(newItem.getWorkoutLevel()) &&
                    oldItem.getWorkoutType().equals(newItem.getWorkoutType());
        }
    }
}