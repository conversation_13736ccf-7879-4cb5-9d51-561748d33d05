package com.vgfit.shefit.fragment.more;

import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.vgfit.shefit.R;
import com.vgfit.shefit.fragment.more.adapter.AdapterMore;
import com.vgfit.shefit.fragment.more.callbacks.OnItemClickMore;
import com.vgfit.shefit.fragment.more.model.ItemMore;
import com.vgfit.shefit.fragment.more.service.AlertDialogMore;
import com.vgfit.shefit.fragment.more.service.MoreMainData;
import com.vgfit.shefit.fragment.more.socialmedia.OpenAnotherApplication;
import com.vgfit.shefit.fragment.more.socialmedia.ShareFacebook;
import com.vgfit.shefit.fragment.more.socialmedia.ShareTwitter;

import java.util.ArrayList;

public class MoreFr extends Fragment implements OnItemClickMore {

    View view;
    AdapterMore adapterMore;
    ArrayList<ItemMore> mainList;
    MoreMainData moreMainData;
    AlertDialogMore alertDialogMore;
    ShareTwitter shareTwitter;
    ShareFacebook shareFacebook;
    OpenAnotherApplication openAnotherApplication;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        moreMainData = new MoreMainData(getContext());
        mainList = new ArrayList<>(moreMainData.createMainListMore());
        alertDialogMore = new AlertDialogMore(getContext());
        shareTwitter = new ShareTwitter(getContext());
        shareFacebook = new ShareFacebook(getContext());
        openAnotherApplication = new OpenAnotherApplication(getContext());
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup parent, Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.fragment_more, parent, false);

        RecyclerView recyclerView = view.findViewById(R.id.rv_more_main);
        recyclerView.setHasFixedSize(true);
        adapterMore = new AdapterMore(getContext(), mainList, this);
        recyclerView.setAdapter(adapterMore);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));

        return view;
    }

    private void changeFragment() {
        FragmentTransaction fragmentTransaction = getParentFragmentManager().beginTransaction();
        fragmentTransaction.addToBackStack(null);
        fragmentTransaction.replace(R.id.root_fragment, new MoreSettingsFr());
        fragmentTransaction.commit();
    }

    @Override
    public void onItemClickMain(ItemMore itemMore, int position) {
        if (getContext() != null)
            switch (position) {
                case 1: //settings
                    changeFragment();
                    break;
                case 2: //rate us
                    try {
                        startActivity(new Intent(Intent.ACTION_VIEW, Uri
                                .parse("http://play.google.com/store/apps/details?id=" + getContext().getPackageName())));
                    } catch (ActivityNotFoundException e) {
                        Toast.makeText(getContext(), "Could not launch Play Store!",
                                Toast.LENGTH_SHORT).show();
                    }
                    break;
                case 3: //send feedback
                    alertDialogMore.createDialog("Enter text message", "Send Feedback", "feedback");
                    break;
                case 4: //tell a friend
                    alertDialogMore.createDialog("Enter text message", "Tell a Friend", "to friend");
                    break;
                case 5: //share app on facebook
                    try {
                        shareFacebook.shareAppOnFacebook();
                    } catch (ActivityNotFoundException e) {
                        Log.e("Facebook", "Error while sharing app on facebook: " + e);
                    }
                    break;
                case 6: //share app on twitter
                    shareTwitter.shareAppOnTwitter();
                    break;
                case 7: //our other apps
                    try {
                        startActivity(new Intent(Intent.ACTION_VIEW, Uri
                                .parse("market://search?q=pub:VGFIT+LLC")));
                    } catch (ActivityNotFoundException e) {
                        Toast.makeText(getContext(), "Could not launch Play Store!",
                                Toast.LENGTH_SHORT).show();
                    }
                    break;
            }
    }
}