package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getCurrentMetricHeight;
import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getImperialHeight;
import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getMetricHeight;
import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentHeightRedesignBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;


public class RulerHeightRedesignFragment extends Fragment {
    private FragmentHeightRedesignBinding binding;
    private static final String KEY_IS_IN = "KEY_IS_IN";
    private float tempValueMetric = 0.0f;
    private float tempValueImperial = 0.0f;
    private static final String HEIGHT_VALUE_SAVE = "KEY_HEIGHT_VALUE_F";
    private boolean isIn = false;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;
    private float lastValue = 0f;

    public static RulerHeightRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        RulerHeightRedesignFragment fragment = new RulerHeightRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View] Current height ruler View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentHeightRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();
        isIn = prefsUtilsWtContext.getBooleanPreferenceProfile(KEY_IS_IN, false);
        binding.rulerView.setOnValueChangedListener(this::handlerValueChanged);
        binding.switchWeight.setOnCheckedChangeListener((compoundButton, isIn) -> switchHandler(isIn));
        binding.rlContinue.setOnClickListener(this::nextAction);
        binding.backLayout.setOnClickListener(view12 -> toBack());
        binding.backTxt.getPaint().setUnderlineText(true);
        if (getContext() != null) {
            binding.switchTextOn.setTextColor(isIn ? ContextCompat.getColor(getContext(), R.color.black) : ContextCompat.getColor(getContext(), R.color.text_color_lime));
            binding.switchTextOff.setTextColor(isIn ? ContextCompat.getColor(getContext(), R.color.text_color_lime) : ContextCompat.getColor(getContext(), R.color.black));
        }
        setParamsFragment();
    }
    private void handlerValueChanged(float value, boolean isProgrammaticScroll){
        if (lastValue != value) {
            setVibrate(getView());
            lastValue = value;
        }
        if (!isIn)
            binding.weightVal.setText(String.format(Integer.toString((int) value)));
        else {
            int ft = (int) value / 12;
            int inch = (int) (value - (ft * 12));
            String valueFt = ft + "'" + inch + "\"";
            binding.weightVal.setText(valueFt);
        }
        if (!isProgrammaticScroll) {
            tempValueMetric = 0.0f;
            tempValueImperial = 0.0f;
            if (isIn) {
                tempValueImperial = value;
            } else
                tempValueMetric = value;

            if (tempValueMetric == 0.0f) {
                tempValueMetric = getMetricHeight(tempValueImperial);
            }
            if (tempValueImperial == 0.0f) {
                tempValueImperial = getImperialHeight(tempValueMetric);
            }
        }
    }
    private void switchHandler(boolean isIn){
        this.isIn = isIn;
        if (getContext() != null) {
            binding.switchTextOn.setTextColor(isIn ? ContextCompat.getColor(getContext(), R.color.black) : ContextCompat.getColor(getContext(), R.color.text_color_lime));
            binding.switchTextOff.setTextColor(isIn ? ContextCompat.getColor(getContext(), R.color.text_color_lime) : ContextCompat.getColor(getContext(), R.color.black));
        }
        int maxValue;
        float valueHeight = !isIn ? tempValueImperial : tempValueMetric;
        float currentValue = valueHeight != 0 ? valueHeight : binding.rulerView.getCurrentValue();
        if (isIn) {
            currentValue = getImperialHeight(currentValue);
            maxValue = 120;
            binding.rulerView.setGradationUnit(1f);
            binding.rulerView.setNumberPerCount(12);
            binding.rulerView.isFt(true);
        } else {
            currentValue = getMetricHeight(currentValue);
            maxValue = 300;
            binding.rulerView.setNumberPerCount(10);
            binding.rulerView.setGradationUnit(1f);
            binding.rulerView.isFt(false);
        }
        binding.rulerView.setMaxValue(maxValue);
        binding.rulerView.setCurrentValue(Math.round(currentValue));
        binding.weightVal.setText(String.valueOf(Math.round(currentValue)));
        prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_IS_IN, isIn);
        setVibrate(binding.switchWeight);
    }
    private void nextAction(View view1){
        isValidPush = false;
        float currentValue = getCurrentMetricHeight(binding.rulerView.getCurrentValue(), isIn);
        prefsUtilsWtContext.setFloatPreference(HEIGHT_VALUE_SAVE, currentValue);
        RulerWeightRedesignFragment fragmentC = RulerWeightRedesignFragment.newInstance(lunchFirstTime, true);
        FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
        transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
        transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_weight").commitAllowingStateLoss();
        setVibrate(view1);
    }

    public void setParamsFragment() {
        binding.switchWeight.setChecked(isIn);
        float valueWeight = prefsUtilsWtContext.getFloatPreference(HEIGHT_VALUE_SAVE, 165);
        binding.textView16.setText(Translate.getValue("your_height"));
        binding.switchTextOn.setText(Translate.getValue("cm"));
        binding.btnContinue.setText(Translate.getValue("next"));
        binding.backTxt.setText(Translate.getValue("go_back"));
        binding.linearProgress.setProgress(30);

        int maxValue;
        float currentValue = valueWeight;
        if (isIn) {
            currentValue = getImperialHeight(currentValue);
            maxValue = 120;
            binding.rulerView.setGradationUnit(1f);
            binding.rulerView.setNumberPerCount(12);
            binding.rulerView.isFt(true);
        } else {
            maxValue = 300;
            binding.rulerView.setGradationUnit(1f);
            binding.rulerView.setNumberPerCount(10);
            binding.rulerView.isFt(false);
        }
        binding.rulerView.setMaxValue(maxValue);
        binding.rulerView.setCurrentValue(currentValue);
        binding.weightVal.setText(String.valueOf((int) (binding.rulerView.getCurrentValue())));
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
                toBack();
                return true; // pretend we've processed it
            } else
                return false; // pass on to be processed as normal
        });
    }

    private void toBack() {
        isValidPush = false;
        FragmentManager fm = getParentFragmentManager();
        fm.popBackStack("frag_life", FragmentManager.POP_BACK_STACK_INCLUSIVE);
    }
}
