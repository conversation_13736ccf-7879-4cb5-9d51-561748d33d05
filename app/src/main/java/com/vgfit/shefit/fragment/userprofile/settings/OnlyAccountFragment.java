package com.vgfit.shefit.fragment.userprofile.settings;

import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.app.Dialog;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.method.LinkMovementMethod;
import android.util.Log;
import android.view.Gravity;
import android.view.HapticFeedbackConstants;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.androidbolts.topsheet.TopSheetBehavior;
import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.R;
import com.vgfit.shefit.api.loginweb.LoginWebService;
import com.vgfit.shefit.api.loginweb.callback.DeleteUserResponse;
import com.vgfit.shefit.api.loginweb.callback.UserExistResponse;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.authorization.Users;
import com.vgfit.shefit.databinding.FragmentOnlyAccountBinding;
import com.vgfit.shefit.fragment.loginweb.LoginWebFragment;
import com.vgfit.shefit.fragment.loginweb.model.TimeDeleteElapsed;
import com.vgfit.shefit.fragment.personal_plan.structure.PersonalPlanPresenter;
import com.vgfit.shefit.notification.service.NotificationService;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.ElapsedTimerUtil;
import com.vgfit.shefit.util.TextUtil;
import com.vgfit.shefit.util.Translate;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

public class OnlyAccountFragment extends Fragment {
    private FragmentOnlyAccountBinding binding;
    private View view;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private ElapsedTimerUtil elapsedTimerUtil;
    private Users users;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        elapsedTimerUtil = new ElapsedTimerUtil();
        users = PersonalPlanPresenter.getUserProfile(prefsUtilsWtContext);
    }

    public static OnlyAccountFragment newInstance() {
        Bundle args = new Bundle();
        OnlyAccountFragment fragment = new OnlyAccountFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentOnlyAccountBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();
        setStatusBar(binding.top.topView);


        binding.top.backProfile.setOnClickListener(v -> {
            setVibrate(v);
            backToProfile();
        });
        binding.nestedScrollView.post(() -> {
            binding.nestedScrollView.fling(0);
            binding.nestedScrollView.fullScroll(View.FOCUS_UP);
            binding.nestedScrollView.scrollTo(0, 0);
        });
        binding.signIn.signOut.setOnClickListener(view14 -> {
            setVibrate(view14);
            openDialogSure("are_you_sure_you_want_to_sign_out", true);
        });
        binding.signIn.signIn.setOnClickListener(view12 -> {
            setVibrate(view12);
            LoginWebFragment fragmentC = LoginWebFragment.newInstance(false);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_up, R.anim.fade_out, R.anim.fade_in, R.anim.slide_down);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_login_web").commit();
        });
        binding.signIn.deleteAccountTxt.setOnClickListener(view15 -> {
            setVibrate(view15);
            openDialogSure("are_you_sure_you_want_to_delete_your_account", false);
        });
        translateWordsFragment();
        isLoginActive();
        setSaveStatus();
    }

    private void isLoginActive() {
        String email = prefsUtilsWtContext.getStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB);
        binding.signIn.containerLogOut.setVisibility(email != null && !email.isEmpty() ? View.VISIBLE : View.GONE);
        binding.signIn.containerLogIn.setVisibility(email == null || email.isEmpty() ? View.VISIBLE : View.GONE);
        if (email != null && !email.isEmpty()) binding.signIn.emailLogin.setText(email);
        binding.signIn.premiumActive.setVisibility(Constant.premium ? View.VISIBLE : View.GONE);
    }

    private void saveLogout() {
        prefsUtilsWtContext.setStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB, "");
        prefsUtilsWtContext.setStringPreferenceProfile(Constant.PASSWORD_LOGIN_WEB, "");
        prefsUtilsWtContext.setStringPreference(Constant.ACCESS_TOKEN, null);
        prefsUtilsWtContext.setStringPreference(Constant.REFRESH_TOKEN, null);
    }

    private void translateWordsFragment() {
        //Top
        binding.top.textView16.setText(Translate.getValue("account"));
        binding.top.saveProfile.setText(Translate.getValue("save"));
        //SignIn
        binding.signIn.logLogin.setText(Translate.getValue("already_have_an_account"));
        binding.signIn.signInTxt.setText(Translate.getValue("sign_in"));
        binding.signIn.signOutTxt.setText(Translate.getValue("sign_out"));
        binding.signIn.deleteAccountTxt.setText(Translate.getValue("delete_account"));

        String infoTxt = Translate.getValue("by_signing_in,_you_agree_to_our_terms_of_use_and_acknowledge_our_privacy_policy");
        binding.titleAlert.setText(Translate.getValue("warning"));
        binding.signIn.infoTerms.setText(TextUtil.getText(infoTxt, getContext()));
        binding.signIn.infoTerms.setMovementMethod(LinkMovementMethod.getInstance());

    }

    private void setVibrate(View view) {
        view.setHapticFeedbackEnabled(true);
        view.performHapticFeedback(HapticFeedbackConstants.KEYBOARD_TAP);
    }


    private void backToProfile() {
        FragmentManager fm = getParentFragmentManager();
        fm.popBackStack("account_only", FragmentManager.POP_BACK_STACK_INCLUSIVE);
    }


    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    public void openDialogSure(String keyTitle, boolean isSignOut) {
        String titleDialog;
        if (getContext() != null) {
            final Dialog d = new Dialog(getContext(), R.style.Theme_AppCompat_Light_Dialog_Alert);
            if (d.getWindow() != null) {
                d.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
                d.setContentView(R.layout.dialog_sure_web);
                d.setCanceledOnTouchOutside(true);
                d.setCancelable(true);
                TextView titleView = d.findViewById(R.id.footer_txt);
                titleDialog = Translate.getValue(keyTitle);
                titleView.setText(titleDialog);
                final TextView btnYes = d.findViewById(R.id.btnYes);
                btnYes.setText(Translate.getValue("yes"));
                btnYes.setOnClickListener(arg0 -> {
                    setVibrate(arg0);
                    sureSignYes(isSignOut, d);
                });
                final TextView btnNo = d.findViewById(R.id.btnNo);
                btnNo.setText(Translate.getValue("no"));
                btnNo.setOnClickListener(arg0 -> {
                    setVibrate(arg0);
                    d.dismiss();
                });
                if (!isSignOut) {
                    RelativeLayout containerYes = d.findViewById(R.id.containerYes);
                    containerYes.setBackgroundResource(R.drawable.delete_account_selector);
                }
                d.show();
            }
        }
    }

    private void logout() {
        saveLogout();
        isLoginActive();
    }

    public void verifyPremium() {
        if (getActivity() != null) {
            boolean tempIsPremium = Constant.premium;
            List<String> listPurchase = ((MainActivity) getActivity()).bp.listOwnedProducts();
            List<String> listSubscription = ((MainActivity) getActivity()).bp.listOwnedSubscriptions();
            boolean isPremium = !listPurchase.isEmpty() || !listSubscription.isEmpty();
            Constant.setPremium(isPremium);
            Constant.setAdsPremium(!isPremium);
            new NotificationService(getActivity()).logOutCheckNotification(tempIsPremium, isPremium);
        }
    }

    private void sureSignYes(boolean isSignOut, Dialog d) {
        if (isSignOut) {
            logout();
            verifyPremium();
            backToProfile();

        } else {
            LoginWebService.sendInstructionDelete(prefsUtilsWtContext, new DeleteUserResponse() {

                @Override
                public void deleteSendFailed() {
                    binding.signIn.progressServer.setVisibility(View.INVISIBLE);
                }

                @Override
                public void deleteSendSuccess() {
                    openDialogDelete();
                    if (getActivity() != null)
                        Constant.startCounterDelete();
                }

                @Override
                public void serverEndResponse(boolean isEnd) {
                    binding.signIn.progressServer.setVisibility(!isEnd ? View.VISIBLE : View.INVISIBLE);
                }

                @Override
                public void responseDeleteUserMessage(String message, boolean isWait) {
                    if (getContext() != null) {
                        topSheet(message);
                        if (isWait)
                            Constant.startCounterDelete();
                    }
                }
            });
        }
        d.dismiss();
    }

    public void openDialogDelete() {
        if (getContext() != null) {
            final Dialog d = new Dialog(getContext(), R.style.BottomDialog);
            if (d.getWindow() != null) {
                d.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
                d.setContentView(R.layout.dialog_forgot_password);
                d.setCanceledOnTouchOutside(true);
                d.setCancelable(true);

                Window window = d.getWindow();
                WindowManager.LayoutParams wlp = window.getAttributes();
                wlp.gravity = Gravity.BOTTOM;
                wlp.flags &= ~WindowManager.LayoutParams.FLAG_BLUR_BEHIND;
                window.setAttributes(wlp);
                TextView titleView = d.findViewById(R.id.footer_txt);
                titleView.setText(Translate.getValue("check_your_email_inbox"));
                TextView textForgot = d.findViewById(R.id.textForgot);
                String textMessage = Translate.getValue("delete_account_info");
                textForgot.setText(TextUtil.getText("<c>", textMessage, getContext()));
                textForgot.setMovementMethod(LinkMovementMethod.getInstance());
                ProgressBar progressBar = d.findViewById(R.id.progressServer);
                final TextView btnOk = d.findViewById(R.id.btnDone);
                btnOk.setText(Translate.getValue("verify"));
                btnOk.setOnClickListener(arg0 -> {
                    setVibrate(arg0);
                    prefsUtilsWtContext.getStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB);
                    String mail = prefsUtilsWtContext.getStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB);
                    if (mail == null || mail.isEmpty()) {
                        topSheet(Translate.getValue("invalid_email"));
                    } else {
                        verifyUserExist(progressBar);
                    }
                });

                d.show();
            }
        }
    }

    private void verifyUserExist(ProgressBar progressBar) {
        LoginWebService.isExistUserId(prefsUtilsWtContext, new UserExistResponse() {
            @Override
            public void userExist(boolean isExist) {
                if (isExist) {
                    topSheet(Translate.getValue("account_is_not_deleted_yet"));
                } else logout();
            }

            @Override
            public void serverEndResponse(boolean isEnd) {
                progressBar.setVisibility(!isEnd ? View.VISIBLE : View.INVISIBLE);
            }
        });
    }

    private void topSheet(String message) {
        binding.messageAlert.setText(message);
        binding.messageAlert.setSelected(true);
        final Handler handler = new Handler(Looper.getMainLooper());
        TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_EXPANDED);
        binding.topSheet.setOnClickListener(v -> TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_COLLAPSED));
        handler.postDelayed(() -> TopSheetBehavior.from(binding.topSheet).setState(TopSheetBehavior.STATE_COLLAPSED), 3000);
    }

    private void setSaveStatus() {
        boolean isModifiedUser = PersonalPlanPresenter.isModifiedUserProfile(users, prefsUtilsWtContext);
        binding.top.saveProfile.setVisibility(isModifiedUser ? View.VISIBLE : View.INVISIBLE);
        binding.top.backProfile.setVisibility(isModifiedUser ? View.INVISIBLE : View.VISIBLE);
    }

    @Override
    public void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }

    @Override
    public void onStop() {
        super.onStop();
        EventBus.getDefault().unregister(this);
    }

    @Override
    public void onResume() {
        super.onResume();
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
                PersonalPlanPresenter.resetUserProfile(prefsUtilsWtContext, users);
                backToProfile();
                return true; // pretend we've processed it
            } else
                return false; // pass on to be processed as normal
        });
        try {
            onMessageEvent(new TimeDeleteElapsed(Constant.timeElapsed));
        } catch (Exception ignored) {
            //ignored
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(TimeDeleteElapsed event) {
        if (event != null && elapsedTimerUtil != null) {
            long timeElapsed = event.getSecondElapse();
            binding.signIn.timeElapsedBigTxt.setText(elapsedTimerUtil.getSecondElapsed(timeElapsed));
            elapsedTimerUtil.isShowElapsed(binding.signIn.timeElapsedBigTxt, timeElapsed);
            elapsedTimerUtil.setValidResend(binding.signIn.deleteAccountTxt, timeElapsed);
            elapsedTimerUtil.setTimeElapsed(timeElapsed);
        }
    }
}
