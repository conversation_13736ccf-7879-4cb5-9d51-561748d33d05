package com.vgfit.shefit.fragment.more.adapter;

import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.vgfit.shefit.R;
import com.vgfit.shefit.fragment.more.MoreWebViewFr;
import com.vgfit.shefit.fragment.more.callbacks.OnItemClickMore;
import com.vgfit.shefit.fragment.more.model.ItemMore;
import com.vgfit.shefit.fragment.more.service.AlertDialogMore;
import com.vgfit.shefit.fragment.more.socialmedia.OpenAnotherApplication;

import java.util.ArrayList;


public class AdapterMore extends RecyclerView.Adapter {
    AlertDialogMore alertDialogMore;
    private final ArrayList<ItemMore> list;
    private final Context context;
    private final int TYPE_FIRST = 0;
    private final int TYPE_THE_REST = 1;
    private int mExpandedPosition = -1;
    private int previousExpandedPosition = -1;
    private final OnItemClickMore onItemClickMore;
    private OpenAnotherApplication openAnotherApplication;

    public AdapterMore(Context context, ArrayList<ItemMore> list, OnItemClickMore onItemClickMore) {
        this.context = context;
        this.list = list;
        this.onItemClickMore = onItemClickMore;
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) return TYPE_FIRST;
        else return TYPE_THE_REST;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        openAnotherApplication = new OpenAnotherApplication(context);
        alertDialogMore = new AlertDialogMore(context);
        if (viewType == 0)
            return new ViewHolderFirst(LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_more_first, viewGroup, false));
        else
            return new ViewHolderRest(LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_more, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {

        ItemMore item = list.get(position);
        int viewType = getItemViewType(position);
        switch (viewType) {
            case TYPE_FIRST:
                ViewHolderFirst holderFirst = ((ViewHolderFirst) viewHolder);
                holderFirst.tvMore.setText(item.getTitleMore());
                Glide.with(context)
                        .load(Uri.parse("file:///android_asset/images_more/" + list.get(position).getImageName()))
                        .into(((AdapterMore.ViewHolderFirst) viewHolder).ivMore);
                hideOrShowSocialMedia(position, holderFirst);
                goToAnotherSocialMedia(holderFirst);
                break;
            case TYPE_THE_REST:
                ViewHolderRest holderRest = ((ViewHolderRest) viewHolder);
                holderRest.tvMore.setText(item.getTitleMore());
                Glide.with(context)
                        .load(Uri.parse("file:///android_asset/images_more/" + list.get(position).getImageName()))
                        .into(((AdapterMore.ViewHolderRest) viewHolder).ivMore);
                if (position == list.size() - 1) holderRest.vLine.setVisibility(View.INVISIBLE);

                holderRest.itemView.setOnClickListener(v -> onItemClickMore.onItemClickMain(list.get(position), position));
                break;
        }
    }

    private void goToAnotherSocialMedia(ViewHolderFirst holderFirst) {
        holderFirst.rlFollowTwitter.setOnClickListener(v -> openAnotherApplication.openTwitterApp());
        holderFirst.rlFollowInstagram.setOnClickListener(v -> openAnotherApplication.openInstagramApp());
        holderFirst.rlFollowFacebook.setOnClickListener(v -> openAnotherApplication.openFacebookApp());
        holderFirst.rlFollowMail.setOnClickListener(v -> alertDialogMore.createDialog("Enter text message", "Send a message", "message"));
        holderFirst.rlFollowWeb.setOnClickListener(v -> changeFragment());
    }

    private void changeFragment() {
        FragmentTransaction fragmentTransaction = ((AppCompatActivity) context).getSupportFragmentManager().beginTransaction();
        fragmentTransaction.addToBackStack(null);
        fragmentTransaction.replace(R.id.root_fragment, new MoreWebViewFr());
        fragmentTransaction.commit();
    }

    private void hideOrShowSocialMedia(int position, ViewHolderFirst holderFirst) {
        final boolean isExpanded = position == mExpandedPosition;
        holderFirst.layout.setVisibility(isExpanded ? View.VISIBLE : View.GONE);
        holderFirst.llMore.setActivated(isExpanded);
        if (isExpanded) previousExpandedPosition = position;
        holderFirst.llMore.setOnClickListener(v -> {
            mExpandedPosition = isExpanded ? -1 : position;
            notifyItemChanged(previousExpandedPosition);
            notifyItemChanged(position);
        });
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    public static class ViewHolderFirst extends RecyclerView.ViewHolder {
        private final TextView tvMore;
        private final ImageView ivMore;
        private final View layout;
        private final LinearLayout llMore;
        private final RelativeLayout rlFollowTwitter;
        private final RelativeLayout rlFollowFacebook;
        private final RelativeLayout rlFollowInstagram;
        private final RelativeLayout rlFollowWeb;
        private final RelativeLayout rlFollowMail;

        public ViewHolderFirst(@NonNull View itemView) {
            super(itemView);

            tvMore = itemView.findViewById(R.id.tv_item_more);
            ivMore = itemView.findViewById(R.id.iv_item_image_more);
            layout = itemView.findViewById(R.id.layout_hidden);
            llMore = itemView.findViewById(R.id.ll_more);
            rlFollowTwitter = itemView.findViewById(R.id.follow_twitter);
            rlFollowFacebook = itemView.findViewById(R.id.follow_facebook);
            rlFollowInstagram = itemView.findViewById(R.id.follow_instagram);
            rlFollowWeb = itemView.findViewById(R.id.follow_web);
            rlFollowMail = itemView.findViewById(R.id.follow_message);
        }
    }


    public static class ViewHolderRest extends RecyclerView.ViewHolder {
        private final TextView tvMore;
        private final ImageView ivMore;
        private final View vLine;

        public ViewHolderRest(@NonNull View itemView) {
            super(itemView);

            tvMore = itemView.findViewById(R.id.tv_item_more);
            ivMore = itemView.findViewById(R.id.iv_item_image_more);
            vLine = itemView.findViewById(R.id.v_line);
        }
    }
}