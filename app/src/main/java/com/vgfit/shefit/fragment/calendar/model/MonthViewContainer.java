package com.vgfit.shefit.fragment.calendar.model;

import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.kizitonwose.calendar.view.ViewContainer;
import com.vgfit.shefit.R;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.Calendar;
import java.util.Locale;

public class MonthViewContainer extends ViewContainer {
    private final TextView[] dayTextViews = new TextView[7]; // Array for dynamic ordering
    private final LocalDate today;
    private final Locale currentLocale;

    public MonthViewContainer(@NonNull View view, LocalDate today, Locale currentLocale) {
        super(view);
        this.today = today;
        this.currentLocale = currentLocale;

        // Initialize TextViews in hardcoded XML order (Monday-Sunday)
        dayTextViews[0] = view.findViewById(R.id.mondayText);    // Monday
        dayTextViews[1] = view.findViewById(R.id.tuesdayText);   // Tuesday
        dayTextViews[2] = view.findViewById(R.id.wednesdayText); // Wednesday
        dayTextViews[3] = view.findViewById(R.id.thursdayText);  // Thursday
        dayTextViews[4] = view.findViewById(R.id.fridayText);    // Friday
        dayTextViews[5] = view.findViewById(R.id.saturdayText);  // Saturday
        dayTextViews[6] = view.findViewById(R.id.sundayText);    // Sunday

        setDayOfWeekTexts();
    }

    private void setDayOfWeekTexts() {
        DayOfWeek currentDayOfWeek = today.getDayOfWeek();

        // Get localized first day of week
        Calendar calendar = Calendar.getInstance(currentLocale);
        int firstDayOfWeek = calendar.getFirstDayOfWeek();

        // Create array of DayOfWeek in localized order
        DayOfWeek[] localizedDaysOrder = getLocalizedDaysOrder(firstDayOfWeek);

        // Set text and color for each TextView based on localized order
        for (int i = 0; i < 7; i++) {
            DayOfWeek dayOfWeek = localizedDaysOrder[i];
            TextView textView = dayTextViews[i];

            if (textView != null) {
                // Set day name text
                textView.setText(dayOfWeek.getDisplayName(TextStyle.SHORT, currentLocale));

                // Set color based on whether it's today
                if (textView.getContext() != null) {
                    int color = currentDayOfWeek == dayOfWeek ?
                        android.R.color.white : R.color.gray_text;
                    textView.setTextColor(ContextCompat.getColor(textView.getContext(), color));
                }
            }
        }
    }

    /**
     * Get array of DayOfWeek in localized order based on first day of week
     */
    private DayOfWeek[] getLocalizedDaysOrder(int firstDayOfWeek) {
        DayOfWeek[] days = new DayOfWeek[7];

        // Convert Calendar first day to DayOfWeek
        DayOfWeek startDay;
        switch (firstDayOfWeek) {
            case Calendar.SUNDAY:
                startDay = DayOfWeek.SUNDAY;
                break;
            case Calendar.MONDAY:
                startDay = DayOfWeek.MONDAY;
                break;
            default:
                startDay = DayOfWeek.MONDAY; // Default fallback
                break;
        }

        // Fill array starting from localized first day
        for (int i = 0; i < 7; i++) {
            days[i] = startDay.plus(i);
        }

        return days;
    }
}