package com.vgfit.shefit.fragment.nutrition;

import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bumptech.glide.Glide;
import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.MenuBottom;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.FragmentNutrition2Binding;
import com.vgfit.shefit.fragment.nutrition.adapter.AdapterNutritionOneDayR;
import com.vgfit.shefit.fragment.nutrition.callbacks.NutritionItemAccessed;
import com.vgfit.shefit.fragment.nutrition.model.ItemNutrition;
import com.vgfit.shefit.realm.Meal;
import com.vgfit.shefit.realm.MealByDay;
import com.vgfit.shefit.realm.NutritionDay;
import com.vgfit.shefit.realm.NutritionPlan;
import com.vgfit.shefit.util.AnimatorView;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.RCUtils;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.text.TextTwoRow;

import java.util.ArrayList;
import java.util.Collections;

import io.realm.Realm;
import io.realm.RealmResults;
import me.everything.android.ui.overscroll.OverScrollDecoratorHelper;

public class NutritionDayFr extends Fragment implements NutritionItemAccessed {
    private FragmentNutrition2Binding binding;
    //    @BindView(R.id.rv_nutrition_vertical)
//    RecyclerView rvNutritionVertical;
//    @BindView(R.id.backContainer)
//    RelativeLayout backContainer;
//    @BindView(R.id.tv_superset_name)
//    TextView tvSupersetName;
//    @BindView(R.id.containerNutrition)
//    RelativeLayout containerNutrition;
//    @BindView(R.id.imageNutrition)
//    ImageView imageNutrition;
//    @BindView(R.id.titleNutrition)
//    TextView titleNutrition;
//    @BindView(R.id.titleNutritionSecond)
//    TextView titleNutritionSecond;
//    @BindView(R.id.shortNutrition)
//    TextView shortNutrition;
//    @BindView(R.id.calInfo)
//    TextView calInfo;
//    @BindView(R.id.timeInfo)
//    TextView timeInfo;
//    @BindView(R.id.containerMenu)
//    RelativeLayout containerMenu;
    View view;
    ArrayList<ArrayList<ItemNutrition>> listNutrition;
    private ArrayList<MealByDay> listIdDay;
    private Context context;
    private String mealDayName = "";
    private AnimatorView animatorView;
    private Meal meal = null;
    private int positionSelected = 0;

    public static NutritionDayFr newInstance(int currentDay, String mealDay) {

        Bundle args = new Bundle();
        NutritionDayFr fragment = new NutritionDayFr();
        args.putInt("dayKeyMeal", currentDay);
        args.putString("nameKeyMeal", mealDay);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arg = getArguments();
        context = getContext();
        listNutrition = new ArrayList<>();
        ArrayList<NutritionDay> listNutrionDay = new ArrayList<>();
        listIdDay = new ArrayList<>();
        Realm realm = Realm.getDefaultInstance();
        RealmResults<NutritionPlan> listNutritionPlanRealm = realm.where(NutritionPlan.class).findAll();
        if (listNutritionPlanRealm.size() > 0 && listNutritionPlanRealm.get(0) != null) {
            listNutrionDay.addAll(listNutritionPlanRealm.get(0).getDays());
        }
        Collections.sort(listNutrionDay, (o1, o2) -> o1.getOrder() - o2.getOrder());

        if (arg != null) {
            int posDay = arg.getInt("dayKeyMeal");
            listIdDay.addAll(listNutrionDay.get(posDay).getMeals());
            mealDayName = arg.getString("nameKeyMeal");
            Collections.sort(listIdDay, (o1, o2) -> o1.getOrder() - o2.getOrder());
        }
        animatorView = new AnimatorView();
        animatorView.initScaleUpAlpha();
        animatorView.initScaleDownAlpha();
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup parent, Bundle savedInstanceState) {
        binding = FragmentNutrition2Binding.inflate(inflater, parent, false);
        view = binding.getRoot();
        setStatusBar(view);
        binding.backContainer.setOnClickListener(v ->
        {
            if (getActivity() != null) getActivity().onBackPressed();
        });

        AdapterNutritionOneDayR adapterNutritionOneDay = new AdapterNutritionOneDayR(listIdDay, this);
        binding.rvNutritionVertical.setAdapter(adapterNutritionOneDay);
        binding.rvNutritionVertical.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false));
        adapterNutritionOneDay.selectItemNutrition(positionSelected);
        if (getActivity() != null)
            ((MainActivity) getActivity()).goToPrevious();
        OverScrollDecoratorHelper.setUpOverScroll(binding.rvNutritionVertical, OverScrollDecoratorHelper.ORIENTATION_VERTICAL);
        binding.tvSupersetName.setText(mealDayName);

        binding.boxMeal.containerNutrition.setOnClickListener(v -> {
            if (meal != null)
                if (!Constant.premium)
                    openMainSubscribe();
                else openMeal(meal);
        });
        new MenuBottom((MainActivity) getActivity(), binding.containerMenu);
        return view;
    }

    private void setInfoCalories(Meal itemNutrition) {
        String kcal = itemNutrition.getCalories() + " kcal";
        String min = itemNutrition.getTimeToCook() + " min";
        binding.boxMeal.calInfo.setText(kcal);
        binding.boxMeal.timeInfo.setText(min);
    }

    private void openMainSubscribe() {
        RCUtils rcUtils = new RCUtils(getContext());
        rcUtils.getMainPaywallAndroidOffer(this);
    }

    private void openMeal(Meal itemNutrition) {
        Bundle arguments = new Bundle();
        Fragment fragment = new NutritionRecipeFr();
        arguments.putParcelable("itemObject", itemNutrition);
        FragmentTransaction fragmentTransaction = getParentFragmentManager().beginTransaction();
        fragmentTransaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
        fragmentTransaction.addToBackStack(null);
        fragmentTransaction.replace(R.id.root_fragment, fragment);
        fragment.setArguments(arguments);
        fragmentTransaction.commit();
        if (getActivity() != null)
            ((MainActivity) getActivity()).goToPrevious();
    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    public void onResume() {
        super.onResume();

    }

    @Override
    public void onItemClick(Meal itemNutrition, String nameMeal, int position) {
        animatorView.startScaleDownAlpha(binding.boxMeal.imageNutrition);
        Glide.with(context)
                .load(itemNutrition.getVerticalImage())
                .into(binding.boxMeal.imageNutrition);
//        ImageLoader.getInstance().displayImage(itemNutrition.getVerticalImage(), imageNutrition, ImageUtils.getDefaultDisplayImageOptions(), null);
//        titleNutrition.setText(nameMeal);
        setInfoCalories(itemNutrition);
        TextTwoRow.setText(binding.boxMeal.titleNutrition, binding.boxMeal.titleNutritionSecond, nameMeal);
        String shortDesc = Translate.getValue(itemNutrition.getName());
        binding.boxMeal.shortNutrition.setText(shortDesc);
        setMeal(itemNutrition);
        positionSelected = position;
    }

    private void setMeal(Meal itemNutrition) {
        this.meal = itemNutrition;
    }

}