package com.vgfit.shefit.fragment.workouts.adapter;


import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.vgfit.shefit.databinding.ItemExercisessWorkoutBinding;
import com.vgfit.shefit.fragment.exercises.callbacks.ExerciseClicked;
import com.vgfit.shefit.realm.WorkoutExercise;
import com.vgfit.shefit.util.Translate;

import java.util.List;

public class AdapterAllExercises extends RecyclerView.Adapter<AdapterAllExercises.ViewHolder> {
    private final List<WorkoutExercise> list;
    private final Context context;
    private RequestOptions requestOptions;
    private final ExerciseClicked exerciseClicked;

    public AdapterAllExercises(List<WorkoutExercise> list, Context context, ExerciseClicked exerciseClicked) {
        this.list = list;
        this.context = context;
        this.exerciseClicked = exerciseClicked;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        LayoutInflater inflater = LayoutInflater.from(viewGroup.getContext());
        ItemExercisessWorkoutBinding binding = ItemExercisessWorkoutBinding.inflate(inflater, viewGroup, false);
        ViewHolder holder = new ViewHolder(binding);
        requestOptions = new RequestOptions();
        return holder;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int position) {

        viewHolder.tvName.setText(Translate.getValue(list.get(position).getExercise().getName()));
        viewHolder.tvTime.setText(list.get(position).getDuration() + " sec");
        viewHolder.itemView.setOnClickListener(v -> {
            setVibrate(v);
            exerciseClicked.itemClicked(list.get(position).getExercise());
        });
        requestOptions = requestOptions.transforms(new CenterCrop(), new RoundedCorners(dpToPx(context, 15)));

        Glide.with(context)
                .load(Uri.parse(list.get(position).getExercise().getThumbnail()))
                .apply(requestOptions)
                .into(viewHolder.ivItemExercise);
    }

    @Override
    public int getItemCount() {
        if (list != null)
            return list.size();
        else return 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView ivItemExercise;
        TextView tvTime;
        TextView tvName;
        RelativeLayout rlItem;

        public ViewHolder(@NonNull ItemExercisessWorkoutBinding binding) {
            super(binding.getRoot());
            ivItemExercise = binding.ivItemExercise;
            tvTime = binding.tvItemExerciseTime;
            tvName = binding.tvItemExerciseName;
            rlItem = binding.rlExercises;
        }
    }
}