package com.vgfit.shefit.fragment.nutrition;

import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.R;
import com.vgfit.shefit.databinding.FragmentNutritionBinding;
import com.vgfit.shefit.fragment.nutrition.adapter.AdapterVertNutrition;
import com.vgfit.shefit.fragment.nutrition.callbacks.NutrItemClick;
import com.vgfit.shefit.fragment.nutrition.model.ItemNutrition;
import com.vgfit.shefit.realm.Meal;
import com.vgfit.shefit.realm.NutritionDay;
import com.vgfit.shefit.realm.NutritionPlan;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.RCUtils;
import com.vgfit.shefit.util.Translate;

import java.util.ArrayList;
import java.util.Collections;

import io.realm.Realm;
import io.realm.RealmResults;
import me.everything.android.ui.overscroll.OverScrollDecoratorHelper;

public class NutritionFr extends Fragment implements NutrItemClick {
    private FragmentNutritionBinding binding;
    View view;
    AdapterVertNutrition adapterVertNutrition;
    ArrayList<ArrayList<ItemNutrition>> listNutrition;
    private ArrayList<NutritionDay> listNutrionDay;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        listNutrition = new ArrayList<>();
        listNutrionDay = new ArrayList<>();
        Realm realm = Realm.getDefaultInstance();
        try {
            RealmResults<NutritionPlan> listNutritionPlanRealm = realm.where(NutritionPlan.class).findAll();
            if (listNutritionPlanRealm.size() > 0 && listNutritionPlanRealm.get(0) != null) {
                listNutrionDay.addAll(listNutritionPlanRealm.get(0).getDays());
            }
            Collections.sort(listNutrionDay, (o1, o2) -> o1.getOrder() - o2.getOrder());
        } finally {
            realm.close();
        }
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup parent, Bundle savedInstanceState) {
        binding = FragmentNutritionBinding.inflate(inflater, parent, false);
        view = binding.getRoot();
        setStatusBar(view);
        binding.top.menuButton.setOnClickListener(v ->
        {
            if ((MainActivity) getActivity() != null) ((MainActivity) getActivity()).openDrawer();
        });
        binding.top.nameFragment.setText(Translate.getValue("nutrition"));
        DisplayMetrics displayMetrics = requireContext().getResources().getDisplayMetrics();
        int layoutWidth = (int) (displayMetrics.widthPixels / 2.5);
        int layoutHeight = (int) (displayMetrics.heightPixels / 2.5);

        adapterVertNutrition = new AdapterVertNutrition(getContext(), listNutrionDay, this, layoutWidth, layoutHeight);
        binding.rvNutritionVertical.setAdapter(adapterVertNutrition);
        binding.rvNutritionVertical.setLayoutManager(new LinearLayoutManager(getContext()));
        OverScrollDecoratorHelper.setUpOverScroll(binding.rvNutritionVertical, OverScrollDecoratorHelper.ORIENTATION_VERTICAL);
        if (getActivity() != null)
            ((MainActivity) getActivity()).goToPrevious();

        return view;
    }

    private void openMainSubscribe() {
        RCUtils rcUtils = new RCUtils(getContext());
        rcUtils.getMainPaywallAndroidOffer(this);
    }

    @Override
    public void onItemClick(Meal itemNutrition) {
        if (!Constant.premium)
            openMainSubscribe();
        else openMeal(itemNutrition);
    }

    @Override
    public void onItemClick(Meal itemNutrition, ImageView imageView, TextView shortDesc, TextView calories, TextView time, LinearLayout containerInfoMeal, View view2) {

    }

    private void openMeal(Meal itemNutrition) {
        Bundle arguments = new Bundle();
        Fragment fragment = new NutritionRecipeFr();
        arguments.putParcelable("itemObject", itemNutrition);
        FragmentTransaction fragmentTransaction = getParentFragmentManager().beginTransaction();
        fragmentTransaction.addToBackStack(null);
        //fragmentTransaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
        fragmentTransaction.replace(R.id.root_fragment, fragment);
        fragment.setArguments(arguments);
        fragmentTransaction.commit();
        if (getActivity() != null)
            ((MainActivity) getActivity()).goToPrevious();
    }
}