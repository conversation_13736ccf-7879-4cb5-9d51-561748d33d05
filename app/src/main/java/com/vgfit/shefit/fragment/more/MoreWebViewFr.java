package com.vgfit.shefit.fragment.more;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;

import com.vgfit.shefit.R;
import com.vgfit.shefit.fragment.more.service.CustomWebViewClient;

public class MoreWebViewFr extends Fragment {
    View view;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.fragment_more_web_view, container, false);
        WebView browser = view.findViewById(R.id.webview);
        browser.getSettings().setLoadsImagesAutomatically(true);
        browser.setWebViewClient(new CustomWebViewClient());
        browser.setScrollBarStyle(View.SCROLLBARS_INSIDE_OVERLAY);
        browser.buildDrawingCache();
        browser.loadUrl("http://vgfit.com/home");
        return view;
    }
}