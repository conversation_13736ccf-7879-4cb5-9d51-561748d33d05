package com.vgfit.shefit.fragment.premium;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings;
import com.vgfit.shefit.api.loginweb.LoginWebService;
import com.vgfit.shefit.api.loginweb.callback.LoginStatusResponse;
import com.vgfit.shefit.api.loginweb.callback.UserStatusWebInited;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.billing.BillingProcessor;
import com.vgfit.shefit.billing.callback.IBillingHandler;
import com.vgfit.shefit.fragment.premium.callback.PremiumPurchase;
import com.vgfit.shefit.fragment.premium.callback.Response_RestoreP;
import com.vgfit.shefit.notification.service.NotificationService;
import com.vgfit.shefit.util.AmplitudeEvent;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.RCUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.Date;
import java.util.HashMap;


public class BillingAndRemoteConfig extends FragmentActivity implements IBillingHandler, Response_RestoreP {
    public static boolean readyToPurchase;
    public PremiumPurchase premiumPurchase;
    public BillingProcessor bp;
    public boolean isRunOffer;
    public boolean isNetworkProblem;
    String base64EncodedPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlezYvjKbN8L3CJD4u70Z+Iv3/Vq4a1R1hSwGy1mbZ2Mfxc86I7b2fRuhiZPIruXBOujE256yIgEnb24e6WIxg+i0pCAlb3CKh4UFgdCptbS2t9O3LsXwxrU+VEMKeNMGFSvYYUv9VOywii2umQ12vlU1n7NLdS9sL4aOMvyXcd3U1WbFdZcppKjVsDG/L/dnC1FLIx8HoQWrDY/jT4yDkLQ1idl7DAFZf9bi2MRfgLAUXPwRrEdEPvdouDASKUunZd2gJZPczLe2mrFRzxdm56/S1dhrSQMFMTX2+hNNU4SLPQUrjcFNFG8ufyVuoq5oskW2cHKxuQVKxyTUc2oGyQIDAQAB";
    private int onBoardingVariant = 0;
    private static final String KEY_RUN_FIRST_TIME = "KEY_RUN_FIRST_TIME";
    private static final String KEY_RUN_ONE_TIME = "KEY_RUN_ONE_TIME";
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private FirebaseRemoteConfig remoteConfig;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getApplicationContext());
        onBoardingVariant = prefsUtilsWtContext.getIntegerPreferenceProfile(Constant.onBoardingVariant, 0);
        bp = new BillingProcessor(this, base64EncodedPublicKey, this);
        try {
            bp.initBilling();
        } catch (Exception ignored) {
        }
        remoteConfig = FirebaseRemoteConfig.getInstance();


        getRemoteConfig();
    }

    private void getRemoteConfig() {
        FirebaseRemoteConfigSettings configSettings = new FirebaseRemoteConfigSettings.Builder()
                .setMinimumFetchIntervalInSeconds(0)
                .build();
        remoteConfig.setConfigSettingsAsync(configSettings);
        HashMap<String, Object> defaults = new HashMap<>();
        defaults.put("kMinimumFeaturesAndroid", 0);

        defaults.put("OnboardingVariantAndroid", 0);
        defaults.put("OnboardingPaywallAndroid", 0);
        defaults.put("MainPaywallAndroid", 0);
        defaults.put("AppStartPaywallAndroid", 0);
        defaults.put("ScrollingPaywallAndroid", 0);
        defaults.put("ShowCloseButtonAfterAndroid", 3);

        remoteConfig.setDefaultsAsync(defaults);
        remoteConfig.fetchAndActivate()
                .addOnCompleteListener(this, task -> {
                    if (task.isSuccessful()) {

                        onBoardingVariant = (int) remoteConfig.getLong("OnboardingVariantAndroid");
                        int onBoardingPaywallAndroid = (int) remoteConfig.getLong("OnboardingPaywallAndroid");
                        int mainPaywallAndroid = (int) remoteConfig.getLong("MainPaywallAndroid");
                        int appStartPaywallAndroid = (int) remoteConfig.getLong("AppStartPaywallAndroid");
                        int kMinimumFeaturesAndroid = (int) remoteConfig.getLong("kMinimumFeaturesAndroid");

                        int scrollingPaywall = (int) remoteConfig.getLong("ScrollingPaywallAndroid");
                        int showCloseButtonAfter = (int) remoteConfig.getLong("ShowCloseButtonAfterAndroid");

                        prefsUtilsWtContext.setIntegerPreferenceProfile(Constant.onBoardingVariant, onBoardingVariant);
                        RCUtils rcUtils = new RCUtils(this);
                        rcUtils.setOnBoardingVariantAndroid(onBoardingVariant);
                        rcUtils.setOnBoardingPaywallAndroid(onBoardingPaywallAndroid);
                        rcUtils.setMainPaywallAndroid(mainPaywallAndroid);
                        rcUtils.setAppStartPaywallAndroid(appStartPaywallAndroid);
                        rcUtils.setKMinimumFeaturesAndroid(kMinimumFeaturesAndroid);

                        rcUtils.setScrollingPaywall(scrollingPaywall);
                        rcUtils.setShowCloseButtonAfter(showCloseButtonAfter);
//                        onBoardingVariant = 0;
                    } else {
                        Log.e("RemoteConfig", "<=========Fetch failed======>");
                    }
                });
    }

    public void isRunFirstTime() {
        if (!Constant.premium) {
            isRunOffer = true;
            RCUtils rcUtils = new RCUtils(this);
            rcUtils.getOnBoardingPaywallAndroidOffer(BillingAndRemoteConfig.this);
            prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_RUN_FIRST_TIME, false);
        }
    }

    public void oneTime() {
        isRunOffer = true;
        prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_RUN_ONE_TIME, false);
        RCUtils rcUtils = new RCUtils(this);
        rcUtils.getOneTimePaywallAndroidOffer(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (bp != null)
            bp.release();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void processFinishRestore(Boolean output) {
        if (premiumPurchase != null) {
            premiumPurchase.isPurchased(true);
        }
    }


    @Override
    public void onProductPurchased(@NonNull String productId) {
        runOnUiThread(() -> {
            Log.e("IsPurchasedTest", "onProductPurchased Successful");
            AmplitudeEvent.sendAmplitude("[IAP] Purchase completed successfully");
            Constant.setPremium(true);
            if (premiumPurchase != null)
                premiumPurchase.isPurchased(true);

        });
    }

    @Override
    public void onPurchaseHistoryRestored() {

    }

    @Override
    public void onBillingError(int errorCode, Throwable error) {

    }

    @Override
    public void onBillingInitialized() {
        readyToPurchase = true;
        if (bp != null) {
            try {
                verifyPremium();
            } catch (Exception ignored) {
            }
        }
    }

    public void verifyPremium() {
        runOnUiThread(() -> {
            boolean haveActivePurchase = !bp.listOwnedSubscriptions().isEmpty() || !bp.listOwnedProducts().isEmpty();
            if (haveActivePurchase) {
                Constant.setPremium(true);
                Date subscriptionStartDate = bp.getSubscriptionStartDate();
                new NotificationService(BillingAndRemoteConfig.this).checkStatusUserNotification(true, subscriptionStartDate != null ? subscriptionStartDate : new Date());
                if (premiumPurchase != null)
                    premiumPurchase.isPurchased(true);
            }
            checkWebPremium();
        });
    }
    private void checkWebPremium() {
        if (prefsUtilsWtContext != null) {
            String email = prefsUtilsWtContext.getStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB);
            if (email != null && !email.isEmpty()) {
                Log.d("TestVerify", "Active");
                LoginWebService.getStatusUser(prefsUtilsWtContext, new LoginStatusResponse() {
                    @Override
                    public void responseStatusSuccess() {
                        EventBus.getDefault().post(new UserStatusWebInited());
                    }

                    @Override
                    public void statusPremium(Date date) {
                        new NotificationService(BillingAndRemoteConfig.this).checkStatusUserNotification(Constant.premium, date);
                    }

                    @Override
                    public void responseStatusFailed() {
                        //not implemented
                    }
                });
            }
        }
    }
}
