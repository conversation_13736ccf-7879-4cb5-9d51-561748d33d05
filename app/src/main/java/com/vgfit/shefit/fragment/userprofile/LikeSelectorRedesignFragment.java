package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;
import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.content.Context;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.nostra13.universalimageloader.core.ImageLoader;
import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentLikeRedesignBinding;
import com.vgfit.shefit.fragment.userprofile.model.LikeModelProfile;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.screen.ImageUtils;

import java.util.ArrayList;

public class LikeSelectorRedesignFragment extends Fragment {
    private FragmentLikeRedesignBinding binding;
    //    @BindView(R.id.rl_continue)
//    RelativeLayout rlContinue;
//    @BindView(R.id.textView16)
//    TextView textView16;
//    @BindView(R.id.linearProgress)
//    ProgressBar linearProgress;
//    @BindView(R.id.backLayout)
//    RelativeLayout backLayout;
//    @BindView(R.id.backTxt)
//    TextView backTxt;
//    @BindView(R.id.imageArea)
//    ImageView imageArea;
//    @BindView(R.id.dislikeExercise)
//    RelativeLayout dislikeExercise;
//    @BindView(R.id.likeExercise)
//    RelativeLayout likeExercise;
//    @BindView(R.id.nameExercise)
//    TextView nameExercise;
//    @BindView(R.id.btn_continue)
//    TextView btnContinue;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View mainView;
    private Context context;

    private ArrayList<LikeModelProfile> listImage = new ArrayList<>();

    private int currentPosition = 0;

    public static LikeSelectorRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        LikeSelectorRedesignFragment fragment = new LikeSelectorRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View]Exercises Like View appeared");
        context = getContext();
        listImage.add(new LikeModelProfile("Plank twist.webp", "Plank twist"));
        listImage.add(new LikeModelProfile("Donkey kicks.webp", "Donkey kicks"));
        listImage.add(new LikeModelProfile("Front raises.webp", "Front raises"));
        listImage.add(new LikeModelProfile("Jump rope.webp", "Jump rope"));
        listImage.add(new LikeModelProfile("Lunges.webp", "Lunges"));
        listImage.add(new LikeModelProfile("Shoulder press.webp", "Shoulder press"));
        listImage.add(new LikeModelProfile("Squats.webp", "Squats"));
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentLikeRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.mainView = view;
        this.mainView.setFocusableInTouchMode(true);
        this.mainView.requestFocus();
        binding.rlContinue.setOnClickListener(this::nextOnBoarding);
        binding.likeExercise.setOnClickListener(v -> {
            currentPosition++;
            if (currentPosition < listImage.size()) {
                setImageLike(listImage.get(currentPosition));
            } else nextOnBoarding(v);
            setVibrate(v);
        });
        binding.dislikeExercise.setOnClickListener(v -> {
            currentPosition++;
            if (currentPosition < listImage.size()) {
                setImageLike(listImage.get(currentPosition));
            } else nextOnBoarding(v);
            setVibrate(v);
        });
        binding.backLayout.setOnClickListener(view12 -> toBack());
        binding.backTxt.getPaint().setUnderlineText(true);
        if (currentPosition < listImage.size())
            setImageLike(listImage.get(currentPosition));
        setParamsFragment();
    }

    private void setImageLike(LikeModelProfile likeModelProfile) {
//        Glide.with(this)
//                .load(Uri.parse("file:///android_asset/like_image/" + likeModelProfile.getNameImage()))
//                .centerCrop()
//                .into(imageArea);
//        ImageLoader.getInstance().displayImage("assets://like_image/" + likeModelProfile.getNameImage(), imageArea, ImageUtils.getDefaultDisplayImageOptions(), null);
        ImageLoader.getInstance().displayImage("assets://like_image/" + likeModelProfile.getNameImage(), binding.imageArea, ImageUtils.getRoundDisplayImageOptions(dpToPx(getContext(), 30)), null);
        binding.nameExercise.setText(likeModelProfile.getNameExercise());
    }

    public void nextOnBoarding(View view) {
        isValidPush = false;
        if (listImage.size() > 0) {
            currentPosition = listImage.size() - 1;
        }
        CrushingGoalRedesignFragment fragmentC = CrushingGoalRedesignFragment.newInstance(lunchFirstTime);
        FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
        transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
        transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_").commitAllowingStateLoss();
        setVibrate(view);
    }


    public void setParamsFragment() {
        binding.textView16.setText(Translate.getValue("do_you_like_this_exercise"));
        binding.btnContinue.setText(Translate.getValue("skip"));
        binding.backTxt.setText(Translate.getValue("go_back"));
        binding.linearProgress.setProgress(84);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        mainView.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
//                if (!lunchFirstTime)
                toBack();
                return true; // pretend we've processed it
            } else return false; // pass on to be processed as normal
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void toBack() {
        isValidPush = false;
        if (getActivity() != null) getActivity().onBackPressed();
    }

}
