package com.vgfit.shefit.fragment.profile;

import static com.vgfit.shefit.util.screen.PresentBottomBar.isProfileFirstTime;

import android.os.Bundle;
import android.text.Html;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.vgfit.shefit.MainActivity;
import com.vgfit.shefit.databinding.FragmentSubscriptionBinding;
import com.vgfit.shefit.util.Translate;

import java.util.Objects;

public class ProfileSubscriptionFr extends Fragment {
    View view;
    private String infoSubs;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        infoSubs = Translate.getValue("subscription_automatically_renews").replace("https://vgfit.com/terms", "<a href=\"https://vgfit.com/terms\">Terms</a>").replace("https://vgfit.com/privacy", "<a href=\"https://vgfit.com/privacy\">Privacy policy</a>");
        infoSubs = infoSubs.replace("http://support.apple.com/kb/ht4098", "<a href=\"http://play.google.com/store/account/subscriptions\">Manage subscriptions.</a>");
    }

    private void setStatusBar(View view) {
        if (getContext() != null && view != null) isProfileFirstTime(getContext(), view, true);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        com.vgfit.shefit.databinding.FragmentSubscriptionBinding binding = FragmentSubscriptionBinding.inflate(inflater, container, false);
        view = binding.getRoot();
        setStatusBar(view);
        binding.backProfile.setOnClickListener(v ->
        {
            if (getActivity() != null)
                ((MainActivity) getActivity()).onBackPressed();
        });
        binding.textView16.setText(Translate.getValue("subscription_info"));
        binding.tvSubscription.setMovementMethod(LinkMovementMethod.getInstance());
        binding.tvSubscription.setText(Html.fromHtml(infoSubs.replace(Objects.requireNonNull(System.lineSeparator()), "<br />"), Html.FROM_HTML_MODE_LEGACY));
        return view;
    }
}
