package com.vgfit.shefit.fragment.personal_plan.adapter;

import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;

import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.vgfit.shefit.databinding.ItemDayWeekNewBinding;
import com.vgfit.shefit.fragment.personal_plan.callbacks.CalendarClicked;
import com.vgfit.shefit.fragment.personal_plan.model.DayOfWeek;

import java.util.ArrayList;


public class AdapterWeekDaysScroll extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private final ArrayList<DayOfWeek> listDaysCalendar;
    private final CalendarClicked calendarClicked;

    private int width = 0;

    public AdapterWeekDaysScroll(ArrayList<DayOfWeek> listWeekData, CalendarClicked calendarClicked, RecyclerView recyclerView) {
        this.listDaysCalendar = new ArrayList<>(listWeekData);
        this.calendarClicked = calendarClicked;
        getWidth(recyclerView);
    }

    private void getWidth(RecyclerView scroolView) {
        ViewTreeObserver vto = scroolView.getViewTreeObserver();
        vto.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                width = scroolView.getWidth();
                notifyDataSetChanged();
                scroolView.getViewTreeObserver().removeGlobalOnLayoutListener(this);
            }
        });
    }

    @Override
    public int getItemCount() {
        return listDaysCalendar.size();
    }


    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ItemDayWeekNewBinding binding = ItemDayWeekNewBinding.inflate(inflater, parent, false);
        return new DayViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        DayOfWeek dayOfWeek = listDaysCalendar.get(position);
        DayViewHolder dayViewHolder = (DayViewHolder) holder;

        dayViewHolder.nameDay.setText(dayOfWeek.getNameShort());
        dayViewHolder.itemView.setOnClickListener(v -> {
            calendarClicked.positionCalendar(position);
        });
        if (position == 0) {
            dayViewHolder.itemView.getLayoutParams().width = 0;
            dayViewHolder.itemView.requestLayout();
        } else {
            Log.d("TestWidth", "width==>" + width);
            dayViewHolder.itemView.getLayoutParams().width = width == 0 ? dpToPx(holder.itemView.getContext(), 60) : width / 3;
            dayViewHolder.itemView.requestLayout();
        }
        dayViewHolder.nameDay.setTextColor(position <= 6 ? Color.BLACK : Color.GRAY);
        dayViewHolder.dayDate.setTextColor(position <= 6 ? Color.BLACK : Color.GRAY);
        dayViewHolder.dayDate.setText(dayOfWeek.getDate());
    }

    public static class DayViewHolder extends RecyclerView.ViewHolder {
        TextView nameDay;
        TextView dayDate;

        public DayViewHolder(@NonNull ItemDayWeekNewBinding binding) {
            super(binding.getRoot());
            nameDay = binding.nameDay;
            dayDate = binding.dayDate;
        }
    }
}
