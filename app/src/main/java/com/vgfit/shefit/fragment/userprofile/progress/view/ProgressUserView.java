package com.vgfit.shefit.fragment.userprofile.progress.view;

import com.vgfit.shefit.realm.Achievement;
import com.vgfit.shefit.realm.WorkoutHistory;

import java.util.List;

public interface ProgressUserView {
    void displayWorkoutHistory(List<WorkoutHistory> workoutHistoryList);
    void displayStatistics(int completedWorkouts, int totalWorkouts, int totalDuration, int totalCalories);
    void displayDateRange(String Year,String dateRange, String completedPercent);
    void displayProgressChart(List<Integer> progressValues, List<String> labels, int periodType, int selectedIndex, List<Integer> missingList);
    void displayAchievements(List<Achievement> achievementList);
}