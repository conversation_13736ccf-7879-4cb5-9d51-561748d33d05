package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getCurrentMetricWeight;
import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getImperialWeight;
import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getMetricWeight;
import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentWeightRedesignBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;


public class RulerWeightRedesignFragment extends Fragment {
    private FragmentWeightRedesignBinding binding;
    private float tempValueMetric = 0.0f;
    private float tempValueImperial = 0.0f;
    private static final String KEY_FRAGMENT_CURRENT = "KEY_FRAGMENT_CURRENT";
    private static final String KEY_WEIGHT_VALUE_F = "KEY_WEIGHT_VALUE_F";
    private static final String KEY_IS_LBS = "KEY_IS_LBS";
    private boolean isLbs = false;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean isCurrentWeight = true;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;
    private float lastValue = 0f;

    public static RulerWeightRedesignFragment newInstance(boolean lunchFirstTime, boolean isCurrent) {

        Bundle args = new Bundle();
        args.putBoolean(KEY_FRAGMENT_CURRENT, isCurrent);
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        RulerWeightRedesignFragment fragment = new RulerWeightRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            isCurrentWeight = arg.getBoolean(KEY_FRAGMENT_CURRENT);
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        if (isCurrentWeight)
            sendAmplitude("[View] Current weight ruler View appeared");
        else
            sendAmplitude("[View] Goal weight ruler View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentWeightRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();
        isLbs = prefsUtilsWtContext.getBooleanPreferenceProfile(KEY_IS_LBS, false);
        binding.rulerView.setOnValueChangedListener(this::handlerValueChanged);
        binding.switchWeight.setOnCheckedChangeListener((compoundButton, isLbs) -> switchHandler(isLbs));
        binding.rlContinue.setOnClickListener(this::nextAction);
        binding.backLayout.setOnClickListener(view12 -> toBack());
        binding.backTxt.getPaint().setUnderlineText(true);
        if (getContext() != null) {
            binding.switchTextOn.setTextColor(isLbs ? ContextCompat.getColor(getContext(), R.color.black) : ContextCompat.getColor(getContext(), R.color.text_color_lime));
            binding.switchTextOff.setTextColor(isLbs ? ContextCompat.getColor(getContext(), R.color.text_color_lime) : ContextCompat.getColor(getContext(), R.color.black));
        }
        setParamsFragment();
    }

    private void handlerValueChanged(float value, boolean isProgrammaticScroll) {
        binding.weightVal.setText(String.format(Integer.toString((int) value)));
        if (lastValue != value) {
            setVibrate(getView());
            lastValue = value;
        }
        if (!isProgrammaticScroll) {
            tempValueMetric = 0.0f;
            tempValueImperial = 0.0f;
            if (isLbs) {
                tempValueImperial = value;
            } else
                tempValueMetric = value;

            if (tempValueMetric == 0.0f) {
                tempValueMetric = getMetricWeight(tempValueImperial);
            }
            if (tempValueImperial == 0.0f) {
                tempValueImperial = getImperialWeight(tempValueMetric);
            }
        }
    }
    private void switchHandler(boolean isLbs){
        this.isLbs = isLbs;
        if (getContext() != null) {
            binding.switchTextOn.setTextColor(isLbs ? ContextCompat.getColor(getContext(), R.color.black) : ContextCompat.getColor(getContext(), R.color.text_color_lime));
            binding.switchTextOff.setTextColor(isLbs ? ContextCompat.getColor(getContext(), R.color.text_color_lime) : ContextCompat.getColor(getContext(), R.color.black));
        }
        int maxValue;
        float valueWeight = !isLbs ? tempValueImperial : tempValueMetric;
        float currentValue = valueWeight != 0 ? valueWeight : binding.rulerView.getCurrentValue();
        if (isLbs) {
            currentValue = getImperialWeight(currentValue);
            maxValue = 441;
        } else {
            currentValue = getMetricWeight(currentValue);
            maxValue = 200;
        }
        binding.rulerView.setMaxValue(maxValue);
        binding.rulerView.setCurrentValue(Math.round(currentValue));
        binding.weightVal.setText(String.valueOf(Math.round(binding.rulerView.getCurrentValue())));
        prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_IS_LBS, isLbs);
        setVibrate(binding.switchWeight);
    }

    private void nextAction(View view1){
        isValidPush = false;
        float currentValue = getCurrentMetricWeight(binding.rulerView.getCurrentValue(), isLbs);
        prefsUtilsWtContext.setFloatPreference(KEY_WEIGHT_VALUE_F + (isCurrentWeight ? "1" : "2"), currentValue);
        if (isCurrentWeight) {
            RulerWeightRedesignFragment fragmentC = RulerWeightRedesignFragment.newInstance(lunchFirstTime, false);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_goal_weight").commitAllowingStateLoss();
        } else {
            RulerAgeRedesignFragment fragmentC = RulerAgeRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_age").commitAllowingStateLoss();
        }
        setVibrate(view1);
    }

    private void setParamsFragment() {
        binding.switchWeight.setChecked(isLbs);
        float valueWeight = prefsUtilsWtContext.getFloatPreference(KEY_WEIGHT_VALUE_F + (isCurrentWeight ? "1" : "2"), isCurrentWeight ? 60 : 50);
        binding.textView16.setText(isCurrentWeight ? Translate.getValue("your_current_weight") : Translate.getValue("your_goal_weight"));
        binding.switchTextOn.setText(Translate.getValue("kg"));
        binding.btnContinue.setText(Translate.getValue("next"));
        binding.backTxt.setText(Translate.getValue("go_back"));
        binding.linearProgress.setProgress(isCurrentWeight ? 38 : 46);
        int maxValue;
        float currentValue = valueWeight;
        if (isLbs) {
            currentValue = getImperialWeight(currentValue);
            maxValue = 441;
        } else {
            maxValue = 200;
        }
        binding.rulerView.setMaxValue(maxValue);
        binding.rulerView.setCurrentValue(Math.round(currentValue));
        binding.weightVal.setText(String.valueOf(Math.round(binding.rulerView.getCurrentValue())));
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
                toBack();
                return true; // pretend we've processed it
            } else
                return false; // pass on to be processed as normal
        });
    }

    private void toBack() {
        isValidPush = false;

        FragmentManager fm = getParentFragmentManager();
        fm.popBackStack(isCurrentWeight ? "frag_weight" : "frag_goal_weight", FragmentManager.POP_BACK_STACK_INCLUSIVE);
    }
}
