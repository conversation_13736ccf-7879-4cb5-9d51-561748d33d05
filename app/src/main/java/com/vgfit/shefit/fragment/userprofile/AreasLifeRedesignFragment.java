package com.vgfit.shefit.fragment.userprofile;

import static com.vgfit.shefit.util.AmplitudeEvent.sendAmplitude;
import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.databinding.FragmentLifeRedesignBinding;
import com.vgfit.shefit.util.PushNotificationService;
import com.vgfit.shefit.util.Translate;

public class AreasLifeRedesignFragment extends Fragment {
    private FragmentLifeRedesignBinding binding;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean lunchFirstTime = false;
    private boolean isValidPush = true;
    private PushNotificationService pushNotificationService;
    private View view;

    public static AreasLifeRedesignFragment newInstance(boolean lunchFirstTime) {

        Bundle args = new Bundle();
        args.putBoolean("lunchFirstTime", lunchFirstTime);
        AreasLifeRedesignFragment fragment = new AreasLifeRedesignFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getContext());
        Bundle arg = getArguments();
        if (arg != null) {
            lunchFirstTime = arg.getBoolean("lunchFirstTime");
        }
        pushNotificationService = new PushNotificationService(getContext());
        sendAmplitude("[View] Want to improve View appeared");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentLifeRedesignBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        this.view = view;
        this.view.setFocusableInTouchMode(true);
        this.view.requestFocus();
        binding.energyMotivation.setOnClickListener(v -> {
            saveStatusImprove("energyMotivation", binding.energyMotivation);
            getStatusImprove("energyMotivation", binding.energyMotivation, binding.energyMotivationTxt);
            setVibrate(v);
        });
        binding.enhanceSleep.setOnClickListener(v -> {
            saveStatusImprove("enhanceSleep", binding.enhanceSleep);
            getStatusImprove("enhanceSleep", binding.enhanceSleep, binding.enhanceSleepTxt);
            setVibrate(v);
        });
        binding.lookHot.setOnClickListener(v -> {
            saveStatusImprove("lookHot", binding.lookHot);
            getStatusImprove("lookHot", binding.lookHot, binding.lookHotTxt);
            setVibrate(v);
        });
        binding.physicalHealth.setOnClickListener(v -> {
            saveStatusImprove("physicalHealth", binding.physicalHealth);
            getStatusImprove("physicalHealth", binding.physicalHealth, binding.physicalHealthTxt);
            setVibrate(v);
        });
        binding.lookGorgeous.setOnClickListener(v -> {
            saveStatusImprove("lookGorgeous", binding.lookGorgeous);
            getStatusImprove("lookGorgeous", binding.lookGorgeous, binding.lookGorgeousTxt);
            setVibrate(v);
        });
        binding.selfConfidence.setOnClickListener(v -> {
            saveStatusImprove("selfConfidence", binding.selfConfidence);
            getStatusImprove("selfConfidence", binding.selfConfidence, binding.selfConfidenceTxt);
            setVibrate(v);
        });
        binding.buildStrength.setOnClickListener(v -> {
            saveStatusImprove("buildStrength", binding.buildStrength);
            getStatusImprove("buildStrength", binding.buildStrength, binding.buildStrengthTxt);
            setVibrate(v);
        });
        binding.coreImprove.setOnClickListener(v -> {
            saveStatusImprove("coreImprove", binding.coreImprove);
            getStatusImprove("coreImprove", binding.coreImprove, binding.coreImproveTxt);
            setVibrate(v);
        });
        getStatusImprove("energyMotivation", binding.energyMotivation, binding.energyMotivationTxt);
        getStatusImprove("enhanceSleep", binding.enhanceSleep, binding.enhanceSleepTxt);
        getStatusImprove("lookHot", binding.lookHot, binding.lookHotTxt);
        getStatusImprove("physicalHealth", binding.physicalHealth, binding.physicalHealthTxt);
        getStatusImprove("lookGorgeous", binding.lookGorgeous, binding.lookGorgeousTxt);
        getStatusImprove("selfConfidence", binding.selfConfidence, binding.selfConfidenceTxt);
        getStatusImprove("buildStrength", binding.buildStrength, binding.buildStrengthTxt);
        getStatusImprove("coreImprove", binding.coreImprove, binding.coreImproveTxt);

        binding.rlContinue.setOnClickListener(view1 -> {
            isValidPush = false;
            GreatRedesignFragment fragmentC = GreatRedesignFragment.newInstance(lunchFirstTime);
            FragmentTransaction transaction = getParentFragmentManager().beginTransaction();
            transaction.setCustomAnimations(R.anim.animation_fade_in, R.anim.animation_fade_out, R.anim.slide_in_left, R.anim.slide_out_right);
            transaction.replace(R.id.root_fragment, fragmentC).addToBackStack("frag_life").commitAllowingStateLoss();
            setVibrate(view1);
        });
        binding.backLayout.setOnClickListener(view12 -> toBack());
        binding.backTxt.getPaint().setUnderlineText(true);
        setParamsFragment();
    }


    private void saveStatusImprove(String keyImprove, RelativeLayout layout) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyImprove, layout == binding.enhanceSleep || layout == binding.lookGorgeous || layout == binding.coreImprove);
        prefsUtilsWtContext.setBooleanPreference(keyImprove, !isSelected);
    }

    private void getStatusImprove(String keyImprove, RelativeLayout layout, TextView textView) {
        boolean isSelected = prefsUtilsWtContext.getBooleanPreference(keyImprove, layout == binding.enhanceSleep || layout == binding.lookGorgeous || layout == binding.coreImprove);
        layout.setSelected(isSelected);
        textView.setSelected(isSelected);
    }

    public void setParamsFragment() {
        binding.textView16.setText(Translate.getValue("pick_the_areas_in_your_life_that_you_want_to_improve"));
        binding.energyMotivationTxt.setText(Translate.getValue("energy_and_motivation").toLowerCase());
        binding.enhanceSleepTxt.setText(Translate.getValue("enhance_sleep").toLowerCase());
        binding.lookHotTxt.setText(Translate.getValue("look_hot").toLowerCase());
        binding.physicalHealthTxt.setText(Translate.getValue("physical_health").toLowerCase());
        binding.lookGorgeousTxt.setText(Translate.getValue("look_gorgeous").toLowerCase());
        binding.selfConfidenceTxt.setText(Translate.getValue("self_confidence").toLowerCase());
        binding.buildStrengthTxt.setText(Translate.getValue("build_strength").toLowerCase());
        binding.coreImproveTxt.setText(Translate.getValue("core").toLowerCase());
        binding.btnContinue.setText(Translate.getValue("next"));
        binding.backTxt.setText(Translate.getValue("go_back"));
        binding.linearProgress.setProgress(24);
    }

    @Override
    public void onPause() {
        super.onPause();
        if (lunchFirstTime && isValidPush) {
            pushNotificationService.sendPushNotification(Translate.getValue("return_to_app_notifications_body"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        isValidPush = true;
        view.setOnKeyListener((v, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN)) {
                toBack();
                return true; // pretend we've processed it
            } else return false; // pass on to be processed as normal
        });
    }

    private void toBack() {
        isValidPush = false;
        if (getActivity() != null) getActivity().onBackPressed();
    }
}
