package com.vgfit.shefit.fragment.workouts.downloader;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.util.Log;
import android.widget.Toast;

import com.vgfit.shefit.fragment.workouts.streaming.Utils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import zlc.season.rxdownload3.RxDownload;
import zlc.season.rxdownload3.core.Downloading;
import zlc.season.rxdownload3.core.Failed;
import zlc.season.rxdownload3.core.Normal;
import zlc.season.rxdownload3.core.Status;
import zlc.season.rxdownload3.core.Succeed;
import zlc.season.rxdownload3.core.Suspend;
import zlc.season.rxdownload3.core.Waiting;

public class DownloaderManager {
    private Context context;
    private ArrayList<String> listFiles;
    private CheckInternetConnection ch;
    private Downloaded downloaded;
    private int progress = 0;
    private String URL_MAIN = "";
    private Disposable disposable;
    private int errors;
    private int events;
    private int position;
    private String URL;
    private ProgressDialog progressDialog;

    public DownloaderManager(Context context, Downloaded downloaded, ArrayList<String> listFiles) {
        this.context = context;
        this.listFiles = listFiles;
        this.downloaded = downloaded;
        ch = new CheckInternetConnection(context);

    }

    public void startDownload() {
        if (ch.isConnectingToInternet()) {
            initToDownload();
        } else {
            Toast.makeText(context, "Check Internet Connection !", Toast.LENGTH_LONG).show();
        }
    }


    private void initDialogNew() {
        progressDialog = new ProgressDialog(context);
        progressDialog.setMessage("Downloading video...");
        progressDialog.setIndeterminate(true);
        progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
        progressDialog.setCancelable(true);
        progressDialog.setIndeterminate(false);
        progressDialog.setMax(listFiles.size());
        progressDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "Cancel", (dialog, which) -> {
            stop();
            dialog.dismiss();
        });
        progressDialog.show();
    }

    private void initToDownload() {
        listFiles = new ArrayList<>(getListToDownload(videoExistInFolder(context), listFiles));
//        initDialogNew();
        if (listFiles.size() > 0) {
            create(listFiles.get(0));
        } else {
//            progressDialog.dismiss();
            downloaded.isSuccess(true);
        }
    }

    private void create(final String url) {
        URL = URL_MAIN + url;
        disposable = RxDownload.INSTANCE.create(URL_MAIN + url, true)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(status -> setActionText(status, url));
    }

    private void setActionText(Status status, String url) {
        String text = "";
        if (status instanceof Normal) {
            text = "Normal";
        } else if (status instanceof Suspend) {
            text = "Suspend";
        } else if (status instanceof Waiting) {
            text = "Waiting";
        } else if (status instanceof Downloading) {
            text = "Downloading";
        } else if (status instanceof Failed) {
            text = "Failed";

            Log.e("StartDownload", "FAILED URL " + URL_MAIN + url);
            position++;
            errors++;
            collectErrors(errors);
            create(listFiles.get(position));

        } else if (status instanceof Succeed) {
            text = "Success";
            Log.e("StartDownload", text + " ==>" + URL_MAIN + url);
            position++;
            progress++;
            onProgressUpdate(progress);
            create(listFiles.get(position));
        }
    }

    private void start() {
        RxDownload.INSTANCE.start(URL).subscribe();
    }

    private void stop() {
        RxDownload.INSTANCE.stop(URL).subscribe();
    }

    private void onProgressUpdate(int progress) {
        collectAllEvent();
        int currentProgress = progress;
//        progressDialog.setProgress(currentProgress);
        if (currentProgress == 100) {
            downloaded.isSuccess(true);
//            progressDialog.dismiss();
        }
    }

    private void collectErrors(int errors) {
        collectAllEvent();
        if (errors == listFiles.size()) {
            Toast.makeText(context, "Please check the Internet connection !", Toast.LENGTH_LONG).show();
            downloaded.isSuccess(false);
//            progressDialog.dismiss();
        }
    }

    private void collectAllEvent() {
        events++;
        if (events == listFiles.size() && errors > 0) {
            Toast.makeText(context, "There is a problem, please, try again!", Toast.LENGTH_LONG).show();
//            progressDialog.dismiss();
            downloaded.isSuccess(false);
        } else if (events == listFiles.size() && errors == 0) {
//            progressDialog.dismiss();
            downloaded.isSuccess(true);
        }
    }

    public HashMap<String, String> videoExistInFolder(Context context) {
        HashMap<String, String> listVideoExist = new HashMap<>();
        String fileName = "";
        File file = new File(String.valueOf(Utils.getVideoCacheDir(context)));
//        File file = new File(Environment.getExternalStorageDirectory().getAbsolutePath() + "/Android/data/" + context.getPackageName() + "/FemaleFitnessPro/");
        File[] list = file.listFiles();
        if (list != null)
            for (File f : list) {
                String name = f.getName();
                if (name.endsWith(".mp4")) {
                    fileName = name;
                    Log.e("StartDownload", "fileName ==>" + fileName);
                    listVideoExist.put(fileName, name);
                }
            }
        return listVideoExist;
    }

    public ArrayList<String> getListToDownload(HashMap<String, String> listVideoExist, ArrayList<String> listFiles) {
        ArrayList listToDownload = new ArrayList();
        String name = "";
        String url = "";
        int lastPosition = 0;
        for (int i = 0; i < listFiles.size(); i++) {
            name = listFiles.get(i);
            url = name;
            lastPosition = name.lastIndexOf("/");
            name = name.substring(lastPosition + 1, name.length());
            if (listVideoExist.containsKey(name)) {
                Log.e("StartDownload", "File Exist");
            } else listToDownload.add(url);
        }
        return listToDownload;
    }
}
