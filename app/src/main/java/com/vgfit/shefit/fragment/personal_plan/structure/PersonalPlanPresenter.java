package com.vgfit.shefit.fragment.personal_plan.structure;

import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getCurrentImperialHeight;
import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getCurrentImperialWeight;

import android.util.Log;

import com.vgfit.shefit.api.WeatherService;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.authorization.Users;
import com.vgfit.shefit.fragment.personal_plan.model.DayOfWeek;
import com.vgfit.shefit.fragment.personal_plan.model.OneDayData;
import com.vgfit.shefit.fragment.personal_plan.serviceWeather.ResultWeather;
import com.vgfit.shefit.fragment.userprofile.SaveProfileServer;
import com.vgfit.shefit.realm.CoverDayPlan;
import com.vgfit.shefit.realm.CoverVideoPlan;
import com.vgfit.shefit.realm.DayPlan;
import com.vgfit.shefit.realm.ExercisePlan;
import com.vgfit.shefit.realm.PhotoMeal;
import com.vgfit.shefit.realm.SupersetPlan;
import com.vgfit.shefit.realm.Workout;
import com.vgfit.shefit.realm.WorkoutExercise;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.Translate;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

import io.realm.Realm;
import io.realm.RealmList;
import io.realm.RealmResults;

public class PersonalPlanPresenter implements ResultWeather {
    private final ArrayList<DayPlan> listPlans;
    private final ArrayList<OneDayData> listWeekPlan;
    private final ArrayList<PhotoMeal> listPhotoMeal;
    private final ArrayList<CoverVideoPlan> listCoverVideoPlan;
    private final ArrayList<DayOfWeek> listNameOfWeek;
    private final ArrayList<CoverDayPlan> listCoverDay;
    private final PersonalPlanView personalPlanView;
    private final PrefsUtilsWtContext prefsUtilsWtContext;
    private String daySelected;
    private static final String KEY_IMAGE_WEATHER = "KEY_IMAGE_WEATHER";
    private static final String KEY_TEMP_WEATHER = "KEY_TEMP_WEATHER";

    public PersonalPlanPresenter(PersonalPlanView personalPlanView, PrefsUtilsWtContext prefsUtilsWtContext) {
        this.personalPlanView = personalPlanView;
        this.prefsUtilsWtContext = prefsUtilsWtContext;
        listPlans = new ArrayList<>();
        listWeekPlan = new ArrayList<>();
        listPhotoMeal = new ArrayList<>();
        listCoverVideoPlan = new ArrayList<>();
        listNameOfWeek = new ArrayList<>();
        listCoverDay = new ArrayList<>();
        daySelected = prefsUtilsWtContext.getStringPreferenceProfile("workout_days");
        checkIsProfileOnServer();
    }

    public void getCurrentDay() {
        int currentDayWeek;

        Calendar now = Calendar.getInstance();
        int calendarDayOfWeek = now.get(Calendar.DAY_OF_WEEK);
        int myDayOfWeek = calendarDayOfWeek - 2;
        if (myDayOfWeek < 0) {
            myDayOfWeek += 7;
        }
        currentDayWeek = myDayOfWeek;
        personalPlanView.currentDay(currentDayWeek);
    }

    public void getDailyWorkout() {

        int currentWeek;
        int currentDayWeek;

        Calendar now = Calendar.getInstance();
        currentWeek = now.get(Calendar.WEEK_OF_YEAR) % 4;
        currentWeek = currentWeek == 0 ? 4 : currentWeek;

        int calendarDayOfWeek = now.get(Calendar.DAY_OF_WEEK);
        int myDayOfWeek = calendarDayOfWeek - 2;
        if (myDayOfWeek < 0) {
            myDayOfWeek += 7;
        }
        currentDayWeek = myDayOfWeek;

        if (currentDayWeek == 6) {
            if (currentWeek - 1 == 0) currentWeek = 4;
            else currentWeek = currentWeek - 1;
        }
        int maxDay = currentWeek * 7;
        int minDay = maxDay - 6;

        getListPlans(minDay, maxDay, currentDayWeek);
    }


    public void getListPlans(int minDay, int maxDay, int currentDay) {
        Realm realm = Realm.getDefaultInstance();
        RealmResults<DayPlan> listPlansRealm = realm.where(DayPlan.class).findAll();
        RealmResults<PhotoMeal> listPhotoMealRealm = realm.where(PhotoMeal.class).findAll();
        RealmResults<CoverVideoPlan> listCoverVideoPlanRealm = realm.where(CoverVideoPlan.class).findAll();
        RealmResults<CoverDayPlan> listCoverDayRealm = realm.where(CoverDayPlan.class).findAll();
        ArrayList<CoverVideoPlan> listCoverInitial = new ArrayList<>(listCoverVideoPlanRealm);
        if (!listPlansRealm.isEmpty() && !listPhotoMealRealm.isEmpty() && !listCoverVideoPlanRealm.isEmpty()) {
            listPlans.clear();
            listWeekPlan.clear();
            listPhotoMeal.clear();
            listCoverVideoPlan.clear();
            listNameOfWeek.clear();
            listCoverDay.clear();

            listPlans.addAll(listPlansRealm);
            listPhotoMeal.addAll(listPhotoMealRealm);
            listCoverVideoPlan.addAll(getListCoverVideoPlan(listPlans.size(), listCoverInitial));
            ArrayList<DayOfWeek> listDayOfWeek = new ArrayList<>(getDaysOfWeek(currentDay));
            listNameOfWeek.addAll(listDayOfWeek);
            listCoverDay.addAll(getListCoverDay(listDayOfWeek, new ArrayList<>(listCoverDayRealm)));

            if (daySelected == null) daySelected = "1,3,5";
            OneDayData oneDayData;
            int countDay = 0;
            DayPlan dayPlanRecent = null;
            for (int i = 0; i < listPlans.size(); i++) {
                int order = listPlans.get(i).getOrder();
                if (order >= minDay && order <= maxDay) {
                    DayPlan dayPlan;


                    if (!daySelected.contains(String.valueOf(countDay))) {
                        dayPlan = listPlans.get(maxDay - 1);
                        dayPlanRecent = listPlans.get(i);
                    } else {
                        dayPlan = listPlans.get(i);
                    }
                    if (countDay == 6 && dayPlanRecent != null) {
                        dayPlan = dayPlanRecent;
                    }
                    int posPhotoMeal = order % listPhotoMeal.size();
                    oneDayData = new OneDayData();
                    oneDayData.setDayPlan(dayPlan);
                    oneDayData.setCoverVideoPlan(listCoverVideoPlan.get(i));
                    oneDayData.setPhotoMeal(listPhotoMeal.get(posPhotoMeal));
                    oneDayData.setDayOfWeek(listNameOfWeek.get(countDay));
                    oneDayData.setTimeLong(listNameOfWeek.get(countDay).getTimeLong());
                    oneDayData.setSelectedDay(currentDay == countDay);
                    oneDayData.setCoverDayPlan(listCoverDay.get(countDay));
                    oneDayData.setShowDay(daySelected.contains(String.valueOf(countDay)));
                    oneDayData.setIdWorkout(String.valueOf(listPlans.get(i).getId()));
                    listWeekPlan.add(oneDayData);
                    countDay++;
                }
            }
        }
        if (!listWeekPlan.isEmpty()) personalPlanView.planWeek(listWeekPlan, currentDay);
    }


    private ArrayList<CoverVideoPlan> getListCoverVideoPlan(int sizePlan, ArrayList<CoverVideoPlan> listCoverVideoPlan) {

        listCoverVideoPlan.sort(Comparator.comparingInt(CoverVideoPlan::getOrder));

        ArrayList<CoverVideoPlan> listFinalCover = new ArrayList<>();
        int posRelaxDay = 0;
        for (int i = 0; i < listCoverVideoPlan.size(); i++) {
            CoverVideoPlan cover = listCoverVideoPlan.get(i);
            if (cover.getOrder() == 5555555) posRelaxDay = i;
        }

        int count = 0;
        for (int i = 0; i < sizePlan; i++) {
            int step = i + 1;
            boolean isRelaxDay = step % 7 == 0;
            if (count == listCoverVideoPlan.size() - 1) count = 0;
            if (isRelaxDay) {
                listFinalCover.add(listCoverVideoPlan.get(posRelaxDay));
            } else {
                listFinalCover.add(listCoverVideoPlan.get(count));
                count++;
            }
            if (count == listCoverVideoPlan.size()) {
                count = 0;
            }
        }
        return listFinalCover;
    }

    private ArrayList<DayOfWeek> getDaysOfWeek(int currentDayWeek) {
        DayOfWeek dayOfWeek;
        ArrayList<DayOfWeek> listOfWeek = new ArrayList<>();
        // Get calendar set to current date and time
        Calendar c = Calendar.getInstance();

        // Set the calendar to Sunday of the current week
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);

        //Fix calendar OFFSET
        Date dates = new Date();
        c.setTime(dates);

        // 1 = Sunday, 2 = Monday, etc.
        int dayOfWeekPosition = c.get(Calendar.DAY_OF_WEEK);

        int mondayOffset;
        if (dayOfWeekPosition == 1) {
            mondayOffset = -6;
        } else mondayOffset = (2 - dayOfWeekPosition); // need to minus back

        c.add(Calendar.DAY_OF_YEAR, mondayOffset);
        //END CALENDAR OFFSET

        // Print dates of the current week starting on Sunday
        DateFormat dayNameLong = new SimpleDateFormat("EEEE", Locale.US);
        DateFormat date = new SimpleDateFormat("d", Locale.US);
        String todayDay = "";
        if (daySelected == null) daySelected = "1,3,5";
        for (int i = 0; i < 10; i++) {
            dayOfWeek = new DayOfWeek();
            String nameDayLong = dayNameLong.format(c.getTime());
            String nameDayShort = firstTwo(dayNameLong.format(c.getTime()));
            String dateDay = date.format(c.getTime());
            String nameMeal = "";
            if (currentDayWeek == i) {
                todayDay = nameDayLong;
            }
            if (currentDayWeek != i) {
                switch (nameDayLong) {
                    case "Monday":
                        nameDayLong = Translate.getValue("monday’s_workout");
                        nameMeal = Translate.getValueLine("monday’s_meal_plan", Collections.singletonList(1), false);
                        break;
                    case "Tuesday":
                        nameDayLong = Translate.getValue("tuesday’s_workout");
                        nameMeal = Translate.getValueLine("tuesday’s_meal_plan", Collections.singletonList(1), false);
                        break;
                    case "Wednesday":
                        nameDayLong = Translate.getValue("wednesday’s_workout");
                        nameMeal = Translate.getValueLine("wednesday’s_meal_plan", Collections.singletonList(1), false);
                        break;
                    case "Thursday":
                        nameDayLong = Translate.getValue("thursday’s_workout");
                        nameMeal = Translate.getValueLine("thursday’s_meal_plan", Collections.singletonList(1), false);
                        break;
                    case "Friday":
                        nameDayLong = Translate.getValue("friday’s_workout");
                        nameMeal = Translate.getValueLine("friday’s_meal_plan", Collections.singletonList(1), false);
                        break;
                    case "Saturday":
                        nameDayLong = Translate.getValue("saturday’s_workout");
                        nameMeal = Translate.getValueLine("saturday’s_meal_plan", Collections.singletonList(1), false);
                        break;
                    case "Sunday":
                        nameDayLong = Translate.getValue("sunday’s_workout");
                        nameMeal = Translate.getValueLine("sunday’s_meal_plan", Collections.singletonList(1), false);
                        break;
                    default:
                        break;
                }
                if (nameDayLong.isEmpty())
                    nameDayLong = nameDayLong + "'s " + Translate.getValue("workout");
                if (nameMeal.isEmpty())
                    nameMeal = dayNameLong.format(c.getTime()) + "'s " + Translate.getValue("meal_plan");

            } else {
                nameDayLong = Translate.getValue("todays_workout");
                nameMeal = Translate.getValueLine("todays_meal_plan", Collections.singletonList(1), false);
            }

            if (!daySelected.contains(String.valueOf(i))) {
                nameDayLong = Translate.getValue("rest_day");
            }

            dayOfWeek.setLongName(nameDayLong);
            dayOfWeek.setNameShort(Translate.getValue(nameDayShort));
            dayOfWeek.setDate(dateDay);
            dayOfWeek.setTimeLong(c.getTimeInMillis());
            dayOfWeek.setNameMeal(nameMeal);

            listOfWeek.add(dayOfWeek);
            c.add(Calendar.DAY_OF_MONTH, 1);
        }
        personalPlanView.daysCalendar(listOfWeek);
        personalPlanView.currentDayName(Translate.getValue(todayDay.toLowerCase()));
        return listOfWeek;
    }

    private ArrayList<CoverDayPlan> getListCoverDay(ArrayList<DayOfWeek> listCurrentDayOfWeek, ArrayList<CoverDayPlan> allCoverDay) {
        ArrayList<CoverDayPlan> listCurrentCoverOfWeek = new ArrayList<>();
        for (DayOfWeek dayOfWeek : listCurrentDayOfWeek) {
            int currentDate = Integer.parseInt(dayOfWeek.getDate());
            listCurrentCoverOfWeek.add(allCoverDay.get(currentDate - 1));
        }
        return listCurrentCoverOfWeek;
    }

    public void castToNormalWorkout(OneDayData oneDayData) {
        Workout workout = new Workout();
        WorkoutExercise workoutExercise;
        ArrayList<SupersetPlan> listSuperSet = new ArrayList<>(oneDayData.getDayPlan().getSupersetPlans());
        RealmList<WorkoutExercise> listCastExercise = new RealmList<>();
        for (int i = 0; i < listSuperSet.size(); i++) {
            SupersetPlan supersetPlan = listSuperSet.get(i);
            for (int j = 0; j < supersetPlan.getListExercisesPlan().size(); j++) {
                workoutExercise = new WorkoutExercise();
                ExercisePlan exercisePlan = supersetPlan.getListExercisesPlan().get(j);
                if (exercisePlan != null && exercisePlan.getExercise() != null) {
                    workoutExercise.setDuration(exercisePlan.getTime());
                    workoutExercise.setOrder(supersetPlan.getOrder());
                    workoutExercise.setId(String.valueOf(supersetPlan.getId()));
                    int restTime = j + 1 == supersetPlan.getListExercisesPlan().size() ? supersetPlan.getRestTime() : 0;
                    Log.d("TestRestTime", "restTime--->" + restTime);
                    workoutExercise.setRestTime(restTime);
                    workoutExercise.setExercise(exercisePlan.getExercise());
                }
                listCastExercise.add(workoutExercise);
            }
        }
        workout.setName("daily_plan");
        workout.setId(oneDayData.getIdWorkout());
        workout.setWorkoutExercises(listCastExercise);
        personalPlanView.openListExerciseWorkout(workout, oneDayData.getDayOfWeek().getLongName(), oneDayData.getDayOfWeek().getTimeLong());
    }


    public String firstTwo(String str) {
        return str.length() < 2 ? str.toLowerCase() : str.substring(0, 2).toLowerCase();
    }

    public void getWeather(double latitude, double longitude) {
        boolean isValidRequest = isValidRequestWeather();
        String imageWeather = prefsUtilsWtContext.getStringPreferenceProfile(KEY_IMAGE_WEATHER);
        String tempWeather = prefsUtilsWtContext.getStringPreferenceProfile(KEY_TEMP_WEATHER);
        if (imageWeather == null || tempWeather == null || isValidRequest) {
            personalPlanView.startAnimRequest();
            new WeatherService(this).getWeather(String.valueOf(latitude), String.valueOf(longitude));
        } else {
            personalPlanView.setImageWeather(imageWeather);
            personalPlanView.setTempWeather(tempWeather);
        }
    }

    private boolean isValidRequestWeather() {
        String keyWeatherTimeValid = "KEY_WEATHER_TIME_VALID";
        boolean isValid = false;
        long oneMinute = (1000 * 60);
        long oneHour = (oneMinute * 60);
        long currentTime = System.currentTimeMillis();
        long weatherTimeSaved = prefsUtilsWtContext.getLongPreferenceProfile(keyWeatherTimeValid, 0);
        if (weatherTimeSaved == 0) weatherTimeSaved = currentTime - oneHour;
        if (currentTime - weatherTimeSaved >= oneHour) {
            prefsUtilsWtContext.setLongPreferenceProfile(keyWeatherTimeValid, currentTime);
            return true;
        }

        return isValid;
    }

    @Override
    public void currentWeather(int code, double tempWeather) {
        if (code != -1000) {
            switch (code) {
                case 113:
                    prefsUtilsWtContext.setStringPreferenceProfile(KEY_IMAGE_WEATHER, "ic_weather_clear");
                    personalPlanView.setImageWeather("ic_weather_clear");
                    break;

                case 119:
                case 122:
                case 143:
                case 200:
                case 260:
                    prefsUtilsWtContext.setStringPreferenceProfile(KEY_IMAGE_WEATHER, "ic_weather_cloudy");
                    personalPlanView.setImageWeather("ic_weather_cloudy");
                    break;

                case 389:
                case 116:
                case 176:
                case 248:
                    prefsUtilsWtContext.setStringPreferenceProfile(KEY_IMAGE_WEATHER, "ic_weather_partialy_cloudy");
                    personalPlanView.setImageWeather("ic_weather_partialy_cloudy");
                    break;

                case 386:
                case 293:
                case 296:
                case 299:
                case 302:
                case 305:
                case 308:
                case 311:
                case 314:
                case 353:
                case 356:
                case 359:
                    prefsUtilsWtContext.setStringPreferenceProfile(KEY_IMAGE_WEATHER, "ic_weather_rain");
                    personalPlanView.setImageWeather("ic_weather_rain");
                    break;

                case 395:
                case 392:
                case 377:
                case 374:
                case 371:
                case 368:
                case 365:
                case 362:
                case 179:
                case 182:
                case 185:
                case 227:
                case 230:
                case 263:
                case 266:
                case 281:
                case 284:
                case 317:
                case 320:
                case 323:
                case 326:
                case 329:
                case 332:
                case 335:
                case 338:
                case 350:
                    prefsUtilsWtContext.setStringPreferenceProfile(KEY_IMAGE_WEATHER, "ic_weather_snow");
                    personalPlanView.setImageWeather("ic_weather_snow");
                    break;
                default:
                    break;
            }
            String temp = getTempMetric(tempWeather);
            prefsUtilsWtContext.setStringPreferenceProfile(KEY_TEMP_WEATHER, temp);
            personalPlanView.setTempWeather(temp);
        } else {
            personalPlanView.setHideWeather();
        }
    }

    private String getTempMetric(double tempWeather) {
        String celsius = "℃";
        String fahrenheit = "℉";
        double temperature;
        String finalTemperature;
        try {
            String unitHeight = prefsUtilsWtContext.getStringPreferenceProfile("unitHeight");
            if (unitHeight == null) unitHeight = "m";
            if (!unitHeight.equals("m")) {
                temperature = Math.round(tempWeather * 9 / 5 + 32);
                finalTemperature = temperature + fahrenheit;
            } else {
                finalTemperature = tempWeather + celsius;
            }
        } catch (Exception e) {
            finalTemperature = tempWeather + celsius;
        }

        return finalTemperature;
    }

    private void checkIsProfileOnServer() {
        if (prefsUtilsWtContext != null) {
            boolean isOnServer = prefsUtilsWtContext.getBooleanPreferenceProfile(Constant.KEY_PROFILE_UPDATED, false);
            if (!isOnServer) {
                Log.d("TestAccess", "Update success in this moment");
                SaveProfileServer saveProfileServer = new SaveProfileServer(prefsUtilsWtContext, null);
                saveProfileServer.saveFullProfileToServer(getUserProfile(prefsUtilsWtContext));
            }
        }
    }

    public static Users getUserProfile(PrefsUtilsWtContext prefsUtilsWtContext) {
        final String WEIGHT_VALUE_SAVE = "KEY_WEIGHT_VALUE_F";
        final String KEY_HEIGHT_VALUE = "KEY_HEIGHT_VALUE_F";
        final String KEY_IS_LBS = "KEY_IS_LBS";
        final String KEY_IS_IN = "KEY_IS_IN";
        final String KEY_AGE_VALUE = "KEY_AGE_VALUE_";
        final String KEY_WORKOUT_DAYS = "workout_days";
        Users users = new Users();
        int gender = 1;
        String unitHeight;
        String unitMass;
        int workLevel;
        String nameUser;
        float valueHeight = prefsUtilsWtContext.getFloatPreference(KEY_HEIGHT_VALUE, 150f);
        int age = prefsUtilsWtContext.getIntegerPreference(KEY_AGE_VALUE, 18);
        float weight = prefsUtilsWtContext.getFloatPreference(WEIGHT_VALUE_SAVE + 1, 50);
        float goalWeight = prefsUtilsWtContext.getFloatPreference(WEIGHT_VALUE_SAVE + 2, 45);
        boolean isLbs = prefsUtilsWtContext.getBooleanPreferenceProfile(KEY_IS_LBS, false);
        boolean isIn = prefsUtilsWtContext.getBooleanPreferenceProfile(KEY_IS_IN, false);
        String workoutDays = prefsUtilsWtContext.getStringPreferenceProfile(KEY_WORKOUT_DAYS, "1,3,5");
        unitMass = isLbs ? "lbs" : "kg";
        unitHeight = isIn ? "in" : "cm";
        String selected = prefsUtilsWtContext.getStringPreferenceProfile("active_daily_workout");
        valueHeight = getCurrentImperialHeight(valueHeight, isIn);
        weight = getCurrentImperialWeight(weight, isLbs);
        goalWeight = getCurrentImperialWeight(goalWeight, isLbs);
        workLevel = selected != null ? Integer.parseInt(selected) : 1;
        Log.d("TestUser", "workLevel--->" + workLevel);
        int fitnessGoal = workLevel == 1 ? 3 : workLevel - 2;
        Log.d("TestUser", "fitnessGoal--->" + fitnessGoal);
        workLevel = workLevel - 1;
        if (workLevel == 3) workLevel = 1;
        if (workLevel == 0) workLevel = 2;
        nameUser = "unknown";
        users.setGender(gender);
        users.setFirstName(nameUser);
        users.setAge(age);
        users.setWeight(weight);
        users.setWeightMeasureUnit(unitMass);
        users.setFitnessGoal(fitnessGoal);
        users.setHeight(valueHeight);
        users.setHeightMeasureUnit(unitHeight);
        users.setGoalWeight(goalWeight);
        users.setExperience(workLevel);
        users.setTimezone(TimeZone.getDefault().getID());
        users.setWorkoutDays(workoutDays);
        return users;
    }

    public static boolean isModifiedUserProfile(Users usersInitial, PrefsUtilsWtContext prefsUtilsWtContext) {
        Users usersSave = getUserProfile(prefsUtilsWtContext);
        if (usersInitial.getHeight() != usersSave.getHeight()) return true;
        if (usersInitial.getWeight() != usersSave.getWeight()) return true;
        if (usersInitial.getGoalWeight() != usersSave.getGoalWeight()) return true;
        if (usersInitial.getAge() != usersSave.getAge()) return true;
        Log.d("TestUser", "usersInitial.getFitnessGoal()--->" + usersInitial.getFitnessGoal());
        Log.d("TestUser", "usersSave.getFitnessGoal()------>" + usersSave.getFitnessGoal());
        Log.d("TestUser", "--------------------------------------------------------------");
        if (usersInitial.getFitnessGoal() != usersSave.getFitnessGoal()) return true;
//        if (usersInitial.getExperience() != usersSave.getExperience()) return true;
        if (!usersInitial.getHeightMeasureUnit().equals(usersSave.getHeightMeasureUnit()))
            return true;
        if (!usersInitial.getWeightMeasureUnit().equals(usersSave.getWeightMeasureUnit()))
            return true;
        int countDayWorkout = getCountDay(usersSave.getWorkoutDays());
        if (isModifyDayUser(usersInitial, usersSave) && countDayWorkout >= 2 && countDayWorkout < 7)
            return true;
        return false;
    }

    private static int getCountDay(String workoutDays) {
        if (workoutDays == null || workoutDays.isEmpty()) {
            return 0;
        }
        String[] digits = workoutDays.split(",");
        return digits.length;
    }

    private static boolean isModifyDayUser(Users usersInitial, Users usersSave) {
        boolean monday = usersInitial.getWorkoutDays().contains("0") == usersSave.getWorkoutDays().contains("0");
        boolean tuesday = usersInitial.getWorkoutDays().contains("1") == usersSave.getWorkoutDays().contains("1");
        boolean wednesday = usersInitial.getWorkoutDays().contains("2") == usersSave.getWorkoutDays().contains("2");
        boolean thursday = usersInitial.getWorkoutDays().contains("3") == usersSave.getWorkoutDays().contains("3");
        boolean friday = usersInitial.getWorkoutDays().contains("4") == usersSave.getWorkoutDays().contains("4");
        boolean saturday = usersInitial.getWorkoutDays().contains("5") == usersSave.getWorkoutDays().contains("5");
        boolean sunday = usersInitial.getWorkoutDays().contains("6") == usersSave.getWorkoutDays().contains("6");
        return !(monday && tuesday && wednesday && thursday && friday && saturday && sunday);
    }

    public static void resetUserProfile(PrefsUtilsWtContext pf, Users users) {
        final String MONDAY = "mondayDay";
        final String TUESDAY = "tuesdayDay";
        final String WEDNESDAY = "wednesdayDay";
        final String THURSDAY = "thursdayDay";
        final String FRIDAY = "fridayDay";
        final String SATURDAY = "satDay";
        final String SUNDAY = "sunDay";
        final String WEIGHT_VALUE_SAVE = "KEY_WEIGHT_VALUE_F";
        final String KEY_HEIGHT_VALUE = "KEY_HEIGHT_VALUE_F";
        final String KEY_IS_LBS = "KEY_IS_LBS";
        final String KEY_IS_IN = "KEY_IS_IN";
        final String KEY_AGE_VALUE = "KEY_AGE_VALUE_";
        final String KEY_WORKOUT_DAYS = "workout_days";
        pf.setFloatPreference(KEY_HEIGHT_VALUE, (float) users.getHeight());
        pf.setIntegerPreference(KEY_AGE_VALUE, users.getAge());
        pf.setFloatPreference(WEIGHT_VALUE_SAVE + 1, users.getWeight());
        pf.setFloatPreference(WEIGHT_VALUE_SAVE + 2, users.getGoalWeight());
        boolean unitMass = users.getWeightMeasureUnit().equals("lbs");
        boolean unitHeight = users.getHeightMeasureUnit().equals("in");
        pf.setBooleanPreferenceProfile(KEY_IS_LBS, unitMass);
        pf.setBooleanPreferenceProfile(KEY_IS_IN, unitHeight);
        String workoutDays = users.getWorkoutDays();
        pf.setStringPreferenceProfile(KEY_WORKOUT_DAYS, workoutDays);
        saveFitnessGoal(pf, users.getFitnessGoal());
        pf.setBooleanPreference(MONDAY, workoutDays.contains("0"));
        pf.setBooleanPreference(TUESDAY, workoutDays.contains("1"));
        pf.setBooleanPreference(WEDNESDAY, workoutDays.contains("2"));
        pf.setBooleanPreference(THURSDAY, workoutDays.contains("3"));
        pf.setBooleanPreference(FRIDAY, workoutDays.contains("4"));
        pf.setBooleanPreference(SATURDAY, workoutDays.contains("5"));
        pf.setBooleanPreference(SUNDAY, workoutDays.contains("6"));
    }

    private static void saveFitnessGoal(PrefsUtilsWtContext prefsUtilsWtContext, int fitnessGoal) {
        final String KEY_FITNESS_GOAL = "active_daily_workout";
        prefsUtilsWtContext.setStringPreferenceProfile(KEY_FITNESS_GOAL, String.valueOf(fitnessGoal == 3 ? 1 : fitnessGoal + 2));
    }
}
