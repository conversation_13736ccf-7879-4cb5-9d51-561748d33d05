package com.vgfit.shefit.util;

import android.app.Activity;
import android.content.Context;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentTransaction;

import com.vgfit.shefit.BaseApplication;
import com.vgfit.shefit.BuildConfig;
import com.vgfit.shefit.R;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.fragment.loginweb.LoginWebFragment;
import com.vgfit.shefit.fragment.premium.BillingAndRemoteConfig;
import com.vgfit.shefit.fragment.premium.redesign.MainSubscribeRedesign;
import com.vgfit.shefit.fragment.premium.web.OfferWebFragment;

import java.util.List;

public class RCUtils {
    private final PrefsUtilsWtContext pf;

    private static final String KEY_ON_BOARDING_VARIANT_ANDROID = "key_onBoardingVariantAndroid";
    private static final String KEY_K_MINIMUM_FEATURES_ANDROID = "key_kMinimumFeaturesAndroid";
    private static final String KEY_ON_BOARDING_PAYWALL_ANDROID = "key_OnBoardingPaywallAndroid";
    private static final String KEY_MAIN_PAYWALL_ANDROID = "key_MainPaywallAndroid";
    private static final String KEY_APP_START_PAYWALL_ANDROID = "key_AppStartPaywallAndroid";
    private static final String KEY_SCROLLING_PAYWALL_ANDROID = "key_ScrollingPaywallAndroid";
    private static final String KEY_SHOW_CLOSE_BUTTON_AFTER_ANDROID = "ShowCloseButtonAfterAndroid";


    public RCUtils(Context context) {
        pf = new PrefsUtilsWtContext(context);
    }

    public void setOnBoardingVariantAndroid(int value) {
        pf.setIntegerPreferenceProfile(KEY_ON_BOARDING_VARIANT_ANDROID, value);
        AmplitudeEvent.sendUserProperties("OnboardingVariantAndroid", value);
        if (BuildConfig.DEBUG) Log.d("RCUtils", "onBoardingVariantAndroid--->" + value);
    }

    private int getOnBoardingVariantAndroid() {
        return pf.getIntegerPreferenceProfile(KEY_ON_BOARDING_VARIANT_ANDROID, 1);
    }

    public void setScrollingPaywall(int value) {
        pf.setIntegerPreferenceProfile(KEY_SCROLLING_PAYWALL_ANDROID, value);
        AmplitudeEvent.sendUserProperties("ScrollingPaywallAndroid", value);
        if (BuildConfig.DEBUG) Log.d("RCUtils", "scrollingPaywall--->" + value);
    }

    public int getScrollingPaywall() {
        if (BuildConfig.DEBUG) return 0;
        else return pf.getIntegerPreferenceProfile(KEY_SCROLLING_PAYWALL_ANDROID, 0);
    }

    public void setShowCloseButtonAfter(int value) {
        pf.setIntegerPreferenceProfile(KEY_SHOW_CLOSE_BUTTON_AFTER_ANDROID, value);
        AmplitudeEvent.sendUserProperties("ShowCloseButtonAfterAndroid", value);
        if (BuildConfig.DEBUG) Log.d("RCUtils", "showCloseButtonAfter--->" + value);
    }

    public int getShowCloseButtonAfter() {
        return pf.getIntegerPreferenceProfile(KEY_SHOW_CLOSE_BUTTON_AFTER_ANDROID, 3);
    }

    public void setKMinimumFeaturesAndroid(int value) {
        pf.setIntegerPreferenceProfile(KEY_K_MINIMUM_FEATURES_ANDROID, value);
        AmplitudeEvent.sendUserProperties("kMinimumFeaturesAndroid", value);
        if (BuildConfig.DEBUG) Log.d("RCUtils", "kMinimumFeaturesAndroid--->" + value);
    }

    public int getKMinimumFeaturesAndroid() {
        return pf.getIntegerPreferenceProfile(KEY_K_MINIMUM_FEATURES_ANDROID, 1);
    }

    public void setOnBoardingPaywallAndroid(int value) {
        pf.setIntegerPreferenceProfile(KEY_ON_BOARDING_PAYWALL_ANDROID, value);
        AmplitudeEvent.sendUserProperties("OnboardingPaywallAndroid", getOnBoardingPaywallAndroid());
        if (BuildConfig.DEBUG) Log.d("RCUtils", "onBoardingPaywallAndroid--->" + value);
    }

    private int getOnBoardingPaywallAndroid() {
        return pf.getIntegerPreferenceProfile(KEY_ON_BOARDING_PAYWALL_ANDROID, 1);
    }

    public void setMainPaywallAndroid(int value) {
        pf.setIntegerPreferenceProfile(KEY_MAIN_PAYWALL_ANDROID, value);
        AmplitudeEvent.sendUserProperties("MainPaywallAndroid", getMainPaywallAndroid());
        if (BuildConfig.DEBUG) Log.d("RCUtils", "MainPaywallAndroid--->" + value);
    }

    private int getMainPaywallAndroid() {
        return pf.getIntegerPreferenceProfile(KEY_MAIN_PAYWALL_ANDROID, 1);
    }

    public void setAppStartPaywallAndroid(int value) {
        pf.setIntegerPreferenceProfile(KEY_APP_START_PAYWALL_ANDROID, value);
        AmplitudeEvent.sendUserProperties("AppStartPaywallAndroid", getAppStartPaywallAndroid());
        if (BuildConfig.DEBUG) Log.d("RCUtils", "AppStartPaywallAndroid--->" + value);
    }

    private int getAppStartPaywallAndroid() {
        return pf.getIntegerPreferenceProfile(KEY_APP_START_PAYWALL_ANDROID, 1);
    }

    public void getOnBoardingPaywallAndroidOffer(Activity activity) {
        setSubscribeTypeActivity(getOnBoardingPaywallAndroid(), activity, true);
    }

    public void getAppStartPaywallAndroidOffer(Activity activity) {
        setSubscribeTypeActivity(getAppStartPaywallAndroid(), activity, false);
    }

    public void getMainPaywallAndroidOffer(Fragment fragment) {
        setSubscribeTypeFragment(getMainPaywallAndroid(), fragment);
    }

    public void getOneTimePaywallAndroidOffer(Activity activity) {
        setSubscribeTypeActivity(3, activity, true);
    }

    private void setSubscribeTypeFragment(int type, Fragment fragment) {
        String email = pf.getStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB);
        FragmentTransaction transaction = fragment.getParentFragmentManager().beginTransaction();
        if (!existOfferActive() && !Constant.premium) {
            if (email != null && !email.isEmpty()) {
                OfferWebFragment fragmentAccessStory = OfferWebFragment.newInstance(false);
                transaction.setCustomAnimations(R.anim.slide_up, R.anim.fade_out, R.anim.fade_in, R.anim.slide_down);
                transaction.add(R.id.root_fragment, fragmentAccessStory).addToBackStack("web_offer").commitAllowingStateLoss();
            } else {
                MainSubscribeRedesign fragment2 = MainSubscribeRedesign.newInstance(false, true);
//                transaction.setCustomAnimations(R.anim.slide_up, R.anim.slide_down, 0, R.anim.slide_down);
                transaction.replace(R.id.root_fragment, fragment2).addToBackStack("frag_main_redesign").commitAllowingStateLoss();
            }
        }
    }

    public boolean existOfferActive() {
        List<Fragment> frags = BaseApplication.getOpenFragments();
        for (Fragment f : frags) {
            if (f != null) {
                if (f instanceof OfferWebFragment) return true;
                if (f instanceof MainSubscribeRedesign) return true;
                if (f instanceof LoginWebFragment) return true;
            }
        }
        return false;
    }

    private void setSubscribeTypeActivity(int type, Activity activity,
                                          boolean checkKMinimumFeature) {
        FragmentTransaction transaction;
        if (BuildConfig.DEBUG) Log.d("TestSubscribe", "Subscribe Activated ! ! !");
        if (activity instanceof BillingAndRemoteConfig) {
            transaction = ((FragmentActivity) activity).getSupportFragmentManager().beginTransaction();
        } else {
            transaction = ((AppCompatActivity) activity).getSupportFragmentManager().beginTransaction();
        }
        String email = pf.getStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB);
        if (!existOfferActive() && !Constant.premium) {
            if (email != null && !email.isEmpty()) {
                OfferWebFragment fragmentAccessStory = OfferWebFragment.newInstance(false);
                transaction.setCustomAnimations(R.anim.slide_up, R.anim.fade_out, R.anim.fade_in, R.anim.slide_down);
                transaction.add(R.id.root_fragment, fragmentAccessStory).addToBackStack("web_offer").commitAllowingStateLoss();
            } else {
                MainSubscribeRedesign fragment2 = MainSubscribeRedesign.newInstance(false, true);
//                transaction.setCustomAnimations(R.anim.slide_in_right_offer, R.anim.slide_out_left_offer, 0, 0);
                transaction.replace(R.id.root_fragment, fragment2).addToBackStack("frag_subscribe_first_time").commitAllowingStateLoss();
            }
        }
    }

    public void sendOfferEventAmplitude(boolean checkKMinimumFeature, boolean isMainPaywall) {
        if (isMainPaywall) {
            AmplitudeEvent.sendAmplitude("MainPaywallAndroid view appeared");
            if (BuildConfig.DEBUG) Log.d("TestEventOffer", "MainPaywallAndroid view appeared");
        } else if (checkKMinimumFeature) {
            AmplitudeEvent.sendAmplitude("OnBoardingPaywallAndroid view appeared");
            if (BuildConfig.DEBUG)
                Log.d("TestEventOffer", "OnBoardingPaywallAndroid view appeared");
        } else {
            AmplitudeEvent.sendAmplitude("AppStartPaywallAndroid view appeared");
            if (BuildConfig.DEBUG)
                Log.d("TestEventOffer", "AppStartPaywallAndroid view appeared");
        }
    }


}
