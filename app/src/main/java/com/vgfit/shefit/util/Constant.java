package com.vgfit.shefit.util;

import android.annotation.TargetApi;
import android.content.res.Resources;
import android.os.Build;
import android.speech.tts.TextToSpeech;
import android.util.Log;

import com.vgfit.shefit.fragment.loginweb.model.TimeDeleteElapsed;
import com.vgfit.shefit.fragment.loginweb.model.TimeResendElapsed;
import com.vgfit.shefit.fragment.workouts.Counter;

import org.greenrobot.eventbus.EventBus;

import java.util.HashMap;
import java.util.Locale;

public class Constant {
    public static String THEME_DARK = "THEME_DARK";
    public static String onBoardingVariant = "onBoardingVariant";
    public static final String LBS_TO_KG_COEFFICIENT = "0.45359237";
    public static HashMap<String, String> arrayLang;
    public static boolean premium;
    public static boolean ads_published = true;
    public static boolean isBadInternet = false;

    public static void setPremium(boolean isPremium) {
        premium = isPremium;
    }
    public static void setAdsPremium(boolean isPremium) {
        ads_published = isPremium;
    }

    public static void setIsBadInternet(boolean isBadInternet) {
        Constant.isBadInternet = isBadInternet;
    }

    public static boolean isRequestActiveExtInfo = false;
    public static boolean isRequestActiveAppInstall = false;
    public static boolean shouldShowNewDesign = true;
    public static String ACCESS_TOKEN = "ACCESS_TOKEN";
    public static String REFRESH_TOKEN = "REFRESH_TOKEN";
    public static String SUBSCRIBE_WEEKLY = "com.vgfit.shefit.weekly";
    public static String SUBSCRIBE_WEEKLY_TRIAL = "com.vgfit.shefit.weeklytrial";
    public static String SUBSCRIBE_MONTHLY = "com.vgfit.shefit.monthly";
    public static String SUBSCRIBE_YEARLY = "com.vgfit.shefit.yearly";
    public static String SUBSCRIBE_3_MONTH = "com.vgfit.shefit.3months";
    public static String SUBSCRIBE_3_Plus_3_MONTH = "com.vgfit.shefit.3plus3months";

    public static String SUBSCRIBE_ONE_TIME_WEEK = "com.vgfit.shefit.onetimeweekly";
    public static String KEY_COUNT_RUN = "keyCountRun";
    public static String KEY_FINISHED_WORKOUT = "keyFinishedWork";
    public static String UUId = "UUID";
    public static boolean isInitedBranch = false;

    public static String KEY_ONBOARDING_FINISHED = "KEY_ONBOARDING_FINISHED";
    public static String KEY_PROFILE_UPDATED = "profileUpdated";
    public static String EMAIL_LOGIN_WEB = "EMAIL_LOGIN_WEB";
    public static String PASSWORD_LOGIN_WEB = "PASSWORD_LOGIN_WEB";

    public static String getFirstItemPurchase(int scrollingPaywall) {
        return scrollingPaywall == 0 ? Constant.SUBSCRIBE_MONTHLY : Constant.SUBSCRIBE_3_MONTH;
    }

    public static String getSecondItemPurchase(int scrollingPaywall) {
        return scrollingPaywall == 0 ? Constant.SUBSCRIBE_YEARLY : Constant.SUBSCRIBE_WEEKLY;
    }

    public static String getFirstPeriodText(int scrollingPaywall) {
        return (scrollingPaywall == 0 ? Translate.getValue("month") : Translate.getValue("3_months")).toLowerCase();
    }

    public static String getSecondPeriodText(int scrollingPaywall) {
        return (scrollingPaywall == 0 ? Translate.getValue("year") : Translate.getValue("week2")).toLowerCase();
    }

    public static String getFirstPeriodTitleText(int scrollingPaywall) {
        return (scrollingPaywall == 0 ? Translate.getValue("monthly") : Translate.getValue("3_months")).toLowerCase();
    }

    public static String getSecondPeriodTitleText(int scrollingPaywall) {
        return (scrollingPaywall == 0 ? Translate.getValue("yearly") : Translate.getValue("weekly")).toLowerCase();
    }

    public static String getTextVariantStart(int scrollingPaywall) {
        return scrollingPaywall == 0 ? Translate.getValue("start") : Translate.getValue("continue");
    }

    public static boolean getLanguageTTSexist(TextToSpeech tts) {
        boolean isAviableTTs = false;
        int res = 0;
        Locale defaultLocale = Locale.getDefault();
        try {
            res = tts.isLanguageAvailable(defaultLocale);
        } catch (Exception ignored) {
        }
        if (res != 0)
            if (res == TextToSpeech.LANG_COUNTRY_AVAILABLE) {
                isAviableTTs = true;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    if (arrayLang.get(getCurrentLocale().getLanguage()) == null) {
                        isAviableTTs = false;
                    }
                } else {
                    if (arrayLang.get(getCurrentLocale2()) == null) {
                        isAviableTTs = false;
                    }

                }
                return isAviableTTs;
            }
        return false;
    }

    @TargetApi(Build.VERSION_CODES.N)
    public static Locale getCurrentLocale() {
        return Resources.getSystem().getConfiguration().getLocales().get(0);
    }

    public static String getCurrentLocale2() {
        return Resources.getSystem().getConfiguration().locale.getLanguage();
    }

    public static boolean aviableEnglishTTS(TextToSpeech tts) {
        boolean isAviableTTs = false;
        int res = 0;
        try {
            res = tts.isLanguageAvailable(Locale.US);
        } catch (Exception ignored) {
        }
        if (res != 0)
            if (res == TextToSpeech.LANG_COUNTRY_AVAILABLE) {
                isAviableTTs = true;
            }
        return isAviableTTs;
    }

    public static void initArrayLang() {
        arrayLang = new HashMap<>();
        arrayLang.put("en", "en");
    }

    public static Counter counterElapsed;
    public static Counter counterDelete;
    public static long timeElapsed = 0;
    public static long timeDelete = 0;

    public static void startCounterResend() {
        int tag = 1;
        long time = 60_000;
        if (counterElapsed != null) counterElapsed.stop();
        counterElapsed = new Counter();
        counterElapsed.setOnTickListener((second1, identy) -> {
            long current = second1 / 1000;
            if (current != Constant.timeElapsed) {
                Constant.timeElapsed = current;
                EventBus.getDefault().post(new TimeResendElapsed(current));
            }
        }, tag);
        counterElapsed.setOnFinishListener(indetity -> {
            Log.d("TimerTest", "onFinish-->" + indetity);
            counterElapsed = null;
        });
        counterElapsed.start(time);
    }

    public static void startCounterDelete() {
        int tag = 2;
        long time = 60_000;
        if (counterDelete != null) counterDelete.stop();
        counterDelete = new Counter();
        counterDelete.setOnTickListener((second1, identy) -> {
            long current = second1 / 1000;
            if (current != Constant.timeDelete) {
                Constant.timeDelete = current;
                EventBus.getDefault().post(new TimeDeleteElapsed(current));
            }
        }, tag);
        counterDelete.setOnFinishListener(indetity -> {
            counterDelete = null;
        });
        counterDelete.start(time);
    }

    public static void destroyAllTimers() {
        try {
            if (counterElapsed != null)
                counterElapsed.stop();
            if (counterDelete != null)
                counterDelete.stop();
        } catch (Exception ignored) {
        }

    }

    public static void setRequestActiveAppInstall(boolean requestActiveAppInstall) {
        isRequestActiveAppInstall = requestActiveAppInstall;
    }
}
