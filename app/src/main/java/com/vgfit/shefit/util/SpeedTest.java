package com.vgfit.shefit.util;

import com.vgfit.shefit.callbacks.TestSpeedIsCompleted;

import java.text.DecimalFormat;

import fr.bmartel.speedtest.SpeedTestReport;
import fr.bmartel.speedtest.SpeedTestSocket;
import fr.bmartel.speedtest.inter.ISpeedTestListener;
import fr.bmartel.speedtest.model.SpeedTestError;

public class SpeedTest {
    private static double networksSpeed = 0;
    private SpeedTestSocket speedTestSocket;
    private TestSpeedIsCompleted testSpeedIsCompleted;

    public SpeedTest(TestSpeedIsCompleted testSpeedCompleted) {
        this.testSpeedIsCompleted = testSpeedCompleted;
    }

    private static String conversionSize(long size) {
        if (size <= 0) return "0";
        networksSpeed = size / Math.pow(1024, 2);
//        networksSpeed = 0.5;
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        return new DecimalFormat("#,##0.#").format(size / Math.pow(1024, digitGroups)) + " " + units[digitGroups];
    }

    public void initSpeed() {
        speedTestSocket = new SpeedTestSocket();

        // add a listener to wait for speedtest completion and progress
        speedTestSocket.addSpeedTestListener(new ISpeedTestListener() {

            @Override
            public void onCompletion(SpeedTestReport report) {
                // called when download/upload is complete
                if (networksSpeed <= 1) testSpeedIsCompleted.testSpeedCompleted(true);
                else testSpeedIsCompleted.testSpeedCompleted(false);
            }

            @Override
            public void onError(SpeedTestError speedTestError, String errorMessage) {
                // called when a download/upload error occur
            }

            @Override
            public void onProgress(float percent, SpeedTestReport report) {
                // called to notify download/upload progress
            }
        });
        startTest();
    }

    private void startTest() {
        speedTestSocket.startDownload("ftp://speedtest.tele2.net/1MB.zip");
    }
}