package com.vgfit.shefit.util.offline_reader;

import android.content.Context;
import android.os.Environment;
import android.util.Log;
import android.widget.Toast;

import com.google.gson.Gson;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

public class WriteToJson {

    public static  <T> void saveResponse(Context context, T response, String nameFile) {
        Gson gson = new Gson();
        String json = gson.toJson(response);
        writeToFile(json, context, nameFile);
    }

    private static void writeToFile(String data, Context context, String nameFile) {
        File root = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
        if (!root.exists()) {
            root.mkdirs();
        }
        File gpxfile = new File(root, nameFile + ".json");

        try (FileWriter writer = new FileWriter(gpxfile)) {
            writer.append(data);
            writer.flush();
            Toast.makeText(context, "Saved", Toast.LENGTH_SHORT).show();
        } catch (IOException e) {
            Log.e("Exception", "File write failed: " + e.toString());
        }
    }
}
