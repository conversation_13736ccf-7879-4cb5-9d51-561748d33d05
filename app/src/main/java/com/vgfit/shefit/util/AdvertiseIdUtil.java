package com.vgfit.shefit.util;

import android.content.Context;
import android.util.Log;

import com.google.android.gms.ads.identifier.AdvertisingIdClient;
import com.google.android.gms.common.GooglePlayServicesNotAvailableException;
import com.google.android.gms.common.GooglePlayServicesRepairableException;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.util.async.AsyncTask;
import com.vgfit.shefit.util.meta_events.MetaEventsSender;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;


public class AdvertiseIdUtil {
    private static final String KEY_IS_LIMITED_AD_TRACKING_ENABLED = "KEY_IS_LIMITED_AD_TRACKING_ENABLED";
    private static final String KEY_ADVERTISER_ID = "KEY_ADVERTISER_ID";
    private PrefsUtilsWtContext prefsUtilsWtContext = null;

    public void setAdvertiseIdAmplitude(Context context) {
        prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        advertiseId(context);
    }

    private void advertiseId(Context context) {
        AsyncTask<Void, Void, String> task = new AsyncTask<Void, Void, String>() {
            private boolean isLimitAdTrackingEnabled = true;

            @Override
            protected String doInBackground(Void unused) {
                AdvertisingIdClient.Info idInfo = null;
                try {
                    idInfo = AdvertisingIdClient.getAdvertisingIdInfo(context);
                } catch (GooglePlayServicesNotAvailableException |
                         GooglePlayServicesRepairableException | IOException ignored) {
                }
                String advertId = "";
                try {
                    if (idInfo != null) {
                        isLimitAdTrackingEnabled = idInfo.isLimitAdTrackingEnabled();
                    }
                } catch (NullPointerException ignored) {
                }

                try {
                    if (idInfo != null) {
                        advertId = idInfo.getId();
                    }

                } catch (NullPointerException ignored) {
                }
                sendUserPropertyIsLimitAdTrackingEnabled(isLimitAdTrackingEnabled);
                return advertId;
            }

            @Override
            protected void onPostExecute(String advertId) {
                if (advertId.isEmpty())
                    advertId = "00000000-0000-0000-0000-000000000000";
                sendUserPropertyGAID(advertId);
                sendUserPropertyProvidedGAID(advertId);
                prefsUtilsWtContext.setBooleanPreference(KEY_IS_LIMITED_AD_TRACKING_ENABLED, isLimitAdTrackingEnabled);
                prefsUtilsWtContext.setStringPreference(KEY_ADVERTISER_ID, advertId);
                new MetaEventsSender(context).sendAppInstall();
            }

            @Override
            protected void onBackgroundError(Exception e) {
                new MetaEventsSender(context).sendAppInstall();
            }

        };
        task.execute();
    }


    private void sendUserPropertyGAID(String advertId) {
        // TODO: 13.03.2025 uncomment this line to send GAID to Amplitude
//        JSONObject jsonObject = new JSONObject();
//        try {
//            Log.d("IdAdvertise", "advertise_Id-->" + advertId);
//            jsonObject.put("GAID", advertId);
//            Amplitude.getInstance().setUserProperties(jsonObject);
//        } catch (JSONException ignored) {
//        }
    }

    private void sendUserPropertyIsLimitAdTrackingEnabled(boolean isLimitAdTrackingEnabled) {
        // TODO: 13.03.2025 uncomment this line to send GAID to Amplitude
//        Log.d("IdAdvertise", "isLimitAdTrackingEnabled-->" + isLimitAdTrackingEnabled);
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("AdvertiseIdUtil", isLimitAdTrackingEnabled);
//            Amplitude.getInstance().setUserProperties(jsonObject);
//        } catch (JSONException ignored) {
//        }
    }

    private void sendUserPropertyProvidedGAID(String advertId) {
        // TODO: 13.03.2025 uncomment this line to send GAID to Amplitude
//        boolean isValidId = !advertId.equals("00000000-0000-0000-0000-000000000000");
//        Log.d("IdAdvertise", "providedGAID-->" + isValidId);
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("providedGAID", isValidId);
//            Amplitude.getInstance().setUserProperties(jsonObject);
//        } catch (JSONException ignored) {
//        }
    }
}
