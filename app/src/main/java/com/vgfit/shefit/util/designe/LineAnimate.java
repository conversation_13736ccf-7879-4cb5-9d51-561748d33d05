package com.vgfit.shefit.util.designe;


import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathEffect;
import android.graphics.PathMeasure;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

public class LineAnimate extends View {
    Path path;
    Paint paint;
    Paint paintCircleSmall;
    Paint paintCircleBig;
    Paint paintCircleStroke;
    float length;
    int radiusCircle1;
    int radiusCircle2;
    private int xCircle2;
    private int yCircle2;

    public LineAnimate(Context context) {
        super(context);
    }

    public LineAnimate(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public LineAnimate(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void init(int x1, int y1, int x2, int y2, int curveRadius, int starColor, int endColor, int circleColor, int lineWidth) {
        this.xCircle2 = x2;
        this.yCircle2 = y2;
        this.radiusCircle1 = dpToPx(getContext(), 2);
        this.radiusCircle2 = dpToPx(getContext(), 8);
        Shader shader = new LinearGradient(x1, y1, x2, y2, starColor, endColor, Shader.TileMode.CLAMP);
        paint = new Paint();
        paint.setAntiAlias(true);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(lineWidth);
//        paint.setColor(color);
        paint.setShader(shader);

        path = new Path();
        int midX = x1 + ((x2 - x1) / 2);
        int midY = y1 + ((y2 - y1) / 2);
        float xDiff = (float) (midX - x1);
        float yDiff = (float) (midY - y1);
        double angle = (Math.atan2(yDiff, xDiff) * (180 / Math.PI)) + 90;

        double angleRadians = Math.toRadians(angle);
        float pointX = (float) (midX + curveRadius * Math.cos(angleRadians));
        float pointY = (float) (midY + curveRadius * Math.sin(angleRadians));

        xDiff = (float) (midX - x1);
        yDiff = (float) (midY - y1);
        angle = (Math.atan2(yDiff, xDiff) * (180 / Math.PI)) - 90;

        angleRadians = Math.toRadians(angle);
        float pointX2 = (float) (midX + curveRadius * Math.cos(angleRadians));
        float pointY2 = (float) (midY + curveRadius * Math.sin(angleRadians));

        path.moveTo(x1, y1);
        path.cubicTo(pointX, pointY, pointX2, pointY2, x2, y2);
//        canvas.drawPath(path, paint);

        // Measure the path

        PathMeasure measure = new PathMeasure(path, false);
        length = measure.getLength();

        paintCircleSmall = new Paint();
        paintCircleSmall.setColor(circleColor);
        paintCircleSmall.setStyle(Paint.Style.FILL);
        paintCircleStroke = new Paint(paint);

        paintCircleBig = new Paint();
        paintCircleBig.setColor(starColor);
        paintCircleSmall.setStyle(Paint.Style.FILL);

        ObjectAnimator animator = ObjectAnimator.ofFloat(LineAnimate.this, "phase", 1.0f, 0.0f);
        animator.setDuration(200);
        animator.start();
    }

    //is called by animtor object
    @Keep
    public void setPhase(float phase) {
        paint.setPathEffect(createPathEffect(length, phase));
        invalidate();//will calll onDraw
    }

    private static PathEffect createPathEffect(float pathLength, float phase) {
        return new DashPathEffect(new float[]{pathLength, pathLength},
                Math.max(phase * pathLength, (float) 0.0));
    }

    @Override
    public void onDraw(@NonNull Canvas c) {
        super.onDraw(c);
        try {
            c.drawPath(path, paint);
            c.drawCircle(xCircle2, yCircle2, radiusCircle2, paintCircleBig);
            c.drawCircle(xCircle2, yCircle2, radiusCircle1, paintCircleSmall);

        } catch (Exception ignored) {
        }


    }
}