package com.vgfit.shefit.util;

import com.vgfit.shefit.realm.Achievement;
import com.vgfit.shefit.repository.WorkoutHistoryRepository;

import java.util.ArrayList;
import java.util.List;

import io.realm.Realm;

public class AchievementHelper {
    private AchievementHelper() {
        // Private constructor to prevent instantiation
    }

    public static void initializeAchievements() {
        try (Realm realm = Realm.getDefaultInstance()) {
            // Check if Achievement table exists first
            try {
                realm.where(Achievement.class).count();
            } catch (Exception e) {
                return;
            }

            realm.executeTransaction(r -> {
                // Check if achievements are already initialized
                if (r.where(Achievement.class).count() > 0) {
                    return;
                }

                // Create all achievements based on the table
                List<Achievement> achievements = createAchievementsList();

                for (Achievement achievement : achievements) {
                    r.copyToRealm(achievement);
                }
            });
        } catch (Exception e) {
            // Silent catch to prevent crashes
        }
    }

    private static List<Achievement> createAchievementsList() {
        List<Achievement> achievements = new ArrayList<>();

        achievements.add(new Achievement(1, 1000, "first_step", "you_burned_the_calories"));
        achievements.add(new Achievement(2, 2500, "warming_up", "burned_off_a_pizza_and_dessert"));
        achievements.add(new Achievement(3, 5000, "in_the_zone", "that’s_a_full_day_of_fast_food"));
        achievements.add(new Achievement(4, 10000, "shape_in_progress", "you_melted"));
        achievements.add(new Achievement(5, 17500, "halfway_there", "that’s_the_calories_of_a_whole"));
        achievements.add(new Achievement(6, 25000, "stronger_than_you_think", "equal_to_100_chocolate_bars"));
        achievements.add(new Achievement(7, 35000, "fitness_queen", "burned_off_7_pizzas"));
        achievements.add(new Achievement(8, 50000, "you_did_it", "you_lost_6.5_kg_of_fat"));


        return achievements;
    }

    public static List<Achievement> getAllAchievements() {
        try (Realm realm = Realm.getDefaultInstance()) {
            return realm.copyFromRealm(realm.where(Achievement.class).findAll());
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public static Achievement getAchievementById(int id) {
        try (Realm realm = Realm.getDefaultInstance()) {
            Achievement achievement = realm.where(Achievement.class).equalTo("id", id).findFirst();
            return achievement != null ? realm.copyFromRealm(achievement) : null;
        } catch (Exception e) {
            return null;
        }
    }

    public static void unlockAchievement(int id) {
        try (Realm realm = Realm.getDefaultInstance()) {
            realm.executeTransaction(r -> {
                Achievement achievement = r.where(Achievement.class).equalTo("id", id).findFirst();
                if (achievement != null) {
                    achievement.setUnlocked(true);
                }
            });
        } catch (Exception e) {
            // Silent catch
        }
    }

    public static List<Achievement> getUnlockedAchievements() {
        try (Realm realm = Realm.getDefaultInstance()) {
            return realm.copyFromRealm(realm.where(Achievement.class).equalTo("isUnlocked", true).findAll());
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public static List<Achievement> getLockedAchievements() {
        try (Realm realm = Realm.getDefaultInstance()) {
            return realm.copyFromRealm(realm.where(Achievement.class).equalTo("isUnlocked", false).findAll());
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public static Achievement getNextAchievement(int currentCalories) {
        try (Realm realm = Realm.getDefaultInstance()) {
            Achievement nextAchievement = realm.where(Achievement.class)
                    .greaterThan("calories", currentCalories)
                    .findFirst();
            return nextAchievement != null ? realm.copyFromRealm(nextAchievement) : null;
        } catch (Exception e) {
            return null;
        }
    }

    public static List<Achievement> getAchievementsForCalories(int totalCalories) {
        List<Achievement> unlockedAchievements = new ArrayList<>();
        try (Realm realm = Realm.getDefaultInstance()) {
            List<Achievement> allAchievements = realm.copyFromRealm(
                    realm.where(Achievement.class)
                            .lessThanOrEqualTo("calories", totalCalories)
                            .findAll()
            );

            for (Achievement achievement : allAchievements) {
                if (!achievement.isUnlocked()) {
                    unlockAchievement(achievement.getId());
                    unlockedAchievements.add(achievement);
                }
            }
        } catch (Exception e) {
            // Silent catch
        }
        return unlockedAchievements;
    }

    /**
     * Checks and unlocks achievements based on total calories burned
     * This method should be called after completing a workout
     * @return list of newly unlocked achievements
     */
    public static List<Achievement> checkAndUnlockAchievements() {
        WorkoutHistoryRepository repository = new WorkoutHistoryRepository();
        int totalCalories = repository.getTotalCaloriesBurnedWithProgress();
        return getAchievementsForCalories(totalCalories);
    }

    /**
     * Checks and unlocks achievements based on total calories burned
     * with the option to specify the type of calorie calculation
     * @param useCompletedOnly if true, uses only 100% completed workouts
     * @return list of newly unlocked achievements
     */
    public static List<Achievement> checkAndUnlockAchievements(boolean useCompletedOnly) {
        WorkoutHistoryRepository repository = new WorkoutHistoryRepository();
        int totalCalories;

        if (useCompletedOnly) {
            totalCalories = repository.getTotalCaloriesBurnedCompleted();
        } else {
            totalCalories = repository.getTotalCaloriesBurnedWithProgress();
        }

        return getAchievementsForCalories(totalCalories);
    }

    /**
     * Gets progress towards the next achievement
     * @return an object with information about progress towards the next achievement
     */
    public static AchievementProgress getProgressToNextAchievement() {
        WorkoutHistoryRepository repository = new WorkoutHistoryRepository();
        int totalCalories = repository.getTotalCaloriesBurnedWithProgress();

        Achievement nextAchievement = getNextAchievement(totalCalories);

        if (nextAchievement == null) {
            // All achievements have been unlocked
            return new AchievementProgress(totalCalories, 0, 100, null);
        }

        int caloriesNeeded = nextAchievement.getCalories() - totalCalories;
        int progressPercentage = (int) ((double) totalCalories / nextAchievement.getCalories() * 100);

        return new AchievementProgress(totalCalories, caloriesNeeded, progressPercentage, nextAchievement);
    }

    /**
     * Helper class for information about progress towards the next achievement
     */
    public static class AchievementProgress {
        private final int currentCalories;
        private final int caloriesNeeded;
        private final int progressPercentage;
        private final Achievement nextAchievement;

        public AchievementProgress(int currentCalories, int caloriesNeeded, int progressPercentage, Achievement nextAchievement) {
            this.currentCalories = currentCalories;
            this.caloriesNeeded = caloriesNeeded;
            this.progressPercentage = progressPercentage;
            this.nextAchievement = nextAchievement;
        }

        public int getCurrentCalories() { return currentCalories; }
        public int getCaloriesNeeded() { return caloriesNeeded; }
        public int getProgressPercentage() { return progressPercentage; }
        public Achievement getNextAchievement() { return nextAchievement; }
        public boolean isAllAchievementsUnlocked() { return nextAchievement == null; }
    }
}
