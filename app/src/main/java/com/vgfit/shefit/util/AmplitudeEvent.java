package com.vgfit.shefit.util;

import android.os.Handler;


import org.json.JSONException;
import org.json.JSONObject;

public class AmplitudeEvent {
    private static long delayTime = 0L;
    private static final long refreshTime = 200L;

    public static void sendAmplitude(String eventType) {
//        Handler handler = new Handler();
//        handler.postDelayed(() -> {
//            try {
//                Amplitude.getInstance().logEvent(eventType);
//            } catch (Exception ignored) {
//            }
//        }, getDelayTime());
    }

    public static void sendAmplitude(String eventType, JSONObject jsonObject) {
//        Handler handler = new Handler();
//        handler.postDelayed(() -> {
//            try {
//                Amplitude.getInstance().logEvent(eventType, jsonObject);
//            } catch (Exception ignored) {
//            }
//        }, getDelayTime());
    }

    public static void sendUserProperties(String propName, int value) {
//        JSONObject jsonObject = new JSONObject();
//        Handler handler = new Handler();
//        handler.postDelayed(() -> {
//            try {
//                jsonObject.put(propName, value);
//                Amplitude.getInstance().setUserProperties(jsonObject);
//            } catch (JSONException ignored) {
//            }
//        }, getDelayTime());
    }

    private static long getDelayTime() {
        if (delayTime > refreshTime) delayTime = 0;
        delayTime = delayTime + 10;
        return delayTime;
    }
}
