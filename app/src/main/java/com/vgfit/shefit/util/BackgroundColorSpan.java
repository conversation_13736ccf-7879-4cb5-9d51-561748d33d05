package com.vgfit.shefit.util;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.LineBackgroundSpan;
import android.util.Log;
import android.widget.TextView;

import com.vgfit.shefit.R;

import static com.vgfit.shefit.apprate.SizeUtils.dpToPx;


public class BackgroundColorSpan implements LineBackgroundSpan {
    private float padding;
    private float radius;

    private RectF rect = new RectF();
    private Paint paint = new Paint();
    private Paint paintStroke = new Paint();
    private Path path = new Path();

    private float prevWidth = -1f;
    private float prevLeft = -1f;
    private float prevRight = -1f;
    private float prevBottom = -1f;
    private float prevTop = -1f;

    private int ALIGN_CENTER = 0;
    private int ALIGN_START = 1;
    private int ALIGN_END = 2;
    private int align = ALIGN_CENTER;

    public BackgroundColorSpan(int backgroundColor,
                               float padding,
                               float radius) {
        this.padding = padding;
        this.radius = radius;

        paint.setColor(backgroundColor);
        //paintStroke.setStyle(Paint.Style.STROKE);
        //paintStroke.setStrokeWidth(5f);
        paintStroke.setColor(backgroundColor);
    }

    public void setAlignment(int alignment) {
        align = alignment;
    }

    @Override
    public void drawBackground(
            final Canvas c,
            final Paint p,
            final int left,
            final int right,
            final int top,
            final int baseline,
            final int bottom,
            final CharSequence text,
            final int start,
            final int end,
            final int lnum) {

        float width = p.measureText(text, start, end) + 2f * padding;
//        float width = p.measureText(text, start, end) + 2f * padding;
        float shiftLeft;
        float shiftRight;

        if (align == ALIGN_START) {

            shiftLeft = 0f - padding;
            shiftRight = width + shiftLeft;

        } else if (align == ALIGN_END) {

            shiftLeft = right - width + padding;
            shiftRight = (right + padding);

        } else {

            shiftLeft = (right - width) / 2;
            shiftRight = right - shiftLeft;

        }

        rect.set(shiftLeft, top, shiftRight, bottom);

        if (lnum == 0) {
            c.drawRoundRect(rect, radius, radius, paint);
        } else {
            path.reset();
            float difference = width - prevWidth;
            float diff = -Math.signum(difference) * Math.min(2f * radius, Math.abs(difference / 2f)) / 2f;
            path.moveTo(
                    prevLeft, prevBottom - radius
            );

            if (align != ALIGN_START) {
                path.cubicTo(//1
                        prevLeft, prevBottom - radius,
                        prevLeft, rect.top,
                        prevLeft + 150, rect.top
                );
            } else {
                path.lineTo(prevLeft, prevBottom + radius);
            }
            path.lineTo(
                    rect.left - diff, rect.top
            );
            path.cubicTo(//2
                    rect.left - diff, rect.top,
                    rect.left, rect.top,
                    rect.left, rect.top + radius
            );
            path.lineTo(
                    rect.left, rect.bottom - radius
            );
            path.cubicTo(//3
                    rect.left, rect.bottom - radius,
                    rect.left, rect.bottom,
                    rect.left + radius, rect.bottom
            );
            path.lineTo(
                    rect.right - radius, rect.bottom
            );
            path.cubicTo(//4
                    rect.right - radius, rect.bottom,
                    rect.right, rect.bottom,
                    rect.right, rect.bottom - radius
            );
            path.lineTo(
                    rect.right, rect.top + radius
            );

            if (align != ALIGN_END) {
                path.cubicTo(//5
                        rect.right, rect.top + radius,
                        rect.right, rect.top,
                        rect.right + diff, rect.top
                );
                path.lineTo(
                        prevRight - diff, rect.top
                );
                path.cubicTo(//6
                        prevRight - diff, rect.top,
                        prevRight, rect.top,
                        prevRight, prevBottom - radius
                );

            } else {
                path.lineTo(prevRight, prevBottom - radius);
            }
            path.cubicTo(//7
                    prevRight, prevBottom - radius,
                    prevRight, prevBottom,
                    prevRight - radius, prevBottom
            );

            path.lineTo(
                    prevLeft + radius, prevBottom
            );

            path.cubicTo(//8
                    prevLeft + radius, prevBottom,
                    prevLeft, prevBottom,
                    prevLeft, rect.top - radius
            );
            c.drawPath(path, paintStroke);

        }
        prevWidth = width;
        prevLeft = rect.left;
        prevRight = rect.right;
        prevBottom = rect.bottom;
        prevTop = rect.top;
    }

    public static void setTextBeauty(Context context, TextView textView, String text, int colorSpannable, boolean isCapitalize) {
        try {
            int padding = dpToPx(context, 8);
            int radius = dpToPx(context, 5);
            String textSpanable = text;
            if (isCapitalize)
                textSpanable = CapitalizeFirstLetter.capitaliseName(text);
            Spannable spannable = new SpannableString(textSpanable);
            BackgroundColorSpan span = new BackgroundColorSpan(
                    colorSpannable,
                    padding,
                    radius
            );
            span.setAlignment(1);
            textView.setShadowLayer(padding, 0f, 0f, 0);
            textView.setPadding(padding, padding, padding, padding);
            spannable.setSpan(span, 0, textSpanable.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            textView.setText(spannable);
        } catch (Exception e) {
            textView.setText(text);
        }

    }

    public static void setTextBeautyOnlyWord(Context context, TextView textView, String text, String textSpan, int colorSpannable, boolean isCapitalize) {
        try {
            int padding = dpToPx(context, 8);
            int radius = dpToPx(context, 5);
            String textSpanable = text;

            int indexStartText = text.indexOf(textSpan);

            if (indexStartText != -1) {
                int indexEndText = indexStartText + textSpan.length() + 2;
                if (isCapitalize)
                    textSpanable = CapitalizeFirstLetter.capitaliseName(text);
                Spannable spannable = new SpannableString(textSpanable.replace(textSpan, " " + textSpan + " "));
                RoundedBackgroundSpan span = new RoundedBackgroundSpan(colorSpannable, context.getResources().getColor(R.color.white), radius);

                textView.setShadowLayer(padding, 0f, 0f, 0);
                textView.setPadding(padding, padding, padding, padding);
                spannable.setSpan(span, indexStartText, indexEndText, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                Log.e("TestSpanable", "spanable indexStartText-->" + indexStartText + " indexEndText--> " + indexEndText);
                textView.setText(spannable);
            } else textView.setText(text);

        } catch (Exception e) {
            Log.e("TestSpanable", "error-->" + e.getMessage());
            textView.setText(text);
        }

    }
    public static void setTextBeautyOnlyWordR(Context context, TextView textView, String text, String textSpan, int colorSpannable, boolean isCapitalize) {
        try {
            int radius = dpToPx(context, 5);
            String textSpanable = text + " ";

            int indexStartText = text.indexOf(textSpan);

            if (indexStartText != -1) {
                int indexEndText = indexStartText + textSpan.length() + 2;
                if (isCapitalize)
                    textSpanable = CapitalizeFirstLetter.capitaliseName(text);
                Spannable spannable = new SpannableString(textSpanable.replace(textSpan, " " + textSpan + " "));
                RoundedBackgroundSpan span = new RoundedBackgroundSpan(0, colorSpannable, radius);

                spannable.setSpan(span, indexStartText, indexEndText, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                Log.e("TestSpanable", "spanable indexStartText-->" + indexStartText + " indexEndText--> " + indexEndText);
                textView.setText(spannable);
//                ((Activity)context).runOnUiThread(() -> textView.setText(spannable));
            } else {
                textView.setText(text);
//                ((Activity)context).runOnUiThread(() -> textView.setText(text));
            }

        } catch (Exception e) {
            Log.e("TestSpanable", "error-->" + e.getMessage());
            try {
                textView.setText(text);
//                ((Activity)context).runOnUiThread(() -> textView.setText(text));
            }catch (Exception ignored){}

        }

    }

}