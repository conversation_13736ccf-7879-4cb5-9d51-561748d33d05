package com.vgfit.shefit.util;

import android.util.Log;

import com.vgfit.shefit.BuildConfig;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public class TimeServerUtil {
    private TimeServerUtil() {
        //empty constructor
    }

    private static final String LOG_TIME_STAMP = "LOG_TIME_STAMP";

    public static long getTimeLong(String timeStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS", Locale.US);
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date date = sdf.parse(timeStr);
            return (date != null) ? (date.getTime() / 1000) + 1 : 0;
        } catch (ParseException e) {
            printLogError("Invalid timestamp format");
            return 0;
        }
    }

    private static void printLogError(String message) {
        if (BuildConfig.DEBUG) {
            Log.e(LOG_TIME_STAMP, message);
        }
    }
}
