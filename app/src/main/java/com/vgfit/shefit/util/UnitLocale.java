package com.vgfit.shefit.util;

import java.util.Locale;

public class UnitLocale {

//    public static UnitLocale lbs = new UnitLocale();
//    public static UnitLocale kg = new UnitLocale();

    public static String getDefault() {
        return getFrom(Locale.getDefault());
    }
    public static String getFrom(Locale locale) {
        String countryCode = locale.getCountry();
        if ("US".equals(countryCode)) return "lb"; // USA
        if ("LR".equals(countryCode)) return "lb"; // liberia
        if ("MM".equals(countryCode)) return "lb"; // burma
        return "kg";
    }
}