package com.vgfit.shefit.util;

import android.content.Context;
import android.content.res.Resources;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.widget.EditText;

public class TextUtils {

	public static boolean isEmpty(EditText editText) {
		return editText.getText().toString().trim().length() == 0;
	}
	public static int convertDpToPx(Context context, int dp) {
		Resources resources = context.getResources();
		return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, resources.getDisplayMetrics());
	}
	public static String getDigitsFromString(String stringWithDigits) {
		return stringWithDigits.replaceAll("[^-?0-9]+", " ").replaceAll("\\s+", "");
	}
	public static int spToPx(float sp, Context context) {
		return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, sp, context.getResources().getDisplayMetrics());
	}
	public static int convertPxToDp(int px, Context context){
		return Math.round(px / ((float) context.getResources().getDisplayMetrics().densityDpi / DisplayMetrics.DENSITY_DEFAULT));
	}
}