package com.vgfit.shefit.util;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import androidx.activity.OnBackPressedCallback;
import androidx.fragment.app.Fragment;

import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.fragment.workouts.helpers.WorkoutProgressHelper;
import com.vgfit.shefit.fragment.workouts.model.ItemExercise;
import com.vgfit.shefit.realm.Achievement;
import com.vgfit.shefit.realm.WorkoutHistory;
import com.vgfit.shefit.util.eventCollect.CollectEvent;

import java.util.ArrayList;
import java.util.Set;

import io.realm.Realm;

public class WorkoutBackPressHelper {
    
    private static final String TAG = "WorkoutBackPress";
    
    public interface WorkoutBackPressListener {
        void onWorkoutPaused();
        void onFragmentShouldClose();
        Context getFragmentContext();
        void onShowAchievementCelebration(Achievement achievement);
    }
    
    private Fragment fragment;
    private WorkoutBackPressListener listener;
    private OnBackPressedCallback backPressedCallback;
    
    private boolean canCloseFragment = false;
    private boolean isShowingCustomLayout = false;
    
    // Workout data
    private String idWorkout;
    private String typeWorkout;
    private Set<Integer> completedExercises;
    private ArrayList<ItemExercise> exerciseList;
    private PrefsUtilsWtContext prefsUtilsWtContext;
    
    public WorkoutBackPressHelper(Fragment fragment, WorkoutBackPressListener listener) {
        this.fragment = fragment;
        this.listener = listener;
        setupBackPressCallback();
    }
    
    /**
     * Setup back press callback
     */
    private void setupBackPressCallback() {
        Log.d(TAG, "Setting up back press callback");
        backPressedCallback = new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                Log.d(TAG, "Back pressed detected via OnBackPressedCallback");
                handleBackPress();
            }
        };

        fragment.requireActivity().getOnBackPressedDispatcher().addCallback(fragment, backPressedCallback);
    }
    
    /**
     * Set workout data for progress saving
     */
    public void setWorkoutData(String idWorkout, String typeWorkout, Set<Integer> completedExercises,
                              ArrayList<ItemExercise> exerciseList, PrefsUtilsWtContext prefsUtilsWtContext) {
        this.idWorkout = idWorkout;
        this.typeWorkout = typeWorkout;
        this.completedExercises = completedExercises;
        this.exerciseList = exerciseList;
        this.prefsUtilsWtContext = prefsUtilsWtContext;
    }
    
    /**
     * Trigger back press manually (for buttons)
     */
    public void triggerBackPress() {
        handleBackPress();
    }

    /**
     * Handle back press logic
     */
    private void handleBackPress() {
        if (listener != null) {
            listener.onWorkoutPaused();
        }
        
        Log.d(TAG, "handleBackPress called");
        if (canCloseFragment && !isShowingCustomLayout) {
            Log.d(TAG, "Closing fragment directly");
            closeFragment();
        } else {
            Log.d(TAG, "Showing custom layout before closing");
            showCustomLayoutBeforeClosing();
        }
    }
    
    /**
     * Show custom layout before closing (save progress and check achievements)
     */
    private void showCustomLayoutBeforeClosing() {
        if (isShowingCustomLayout) return; // Prevent multiple calls

        isShowingCustomLayout = true;

        Log.d(TAG, "Show custom layout");
        try (Realm realm = Realm.getDefaultInstance()) {
            realm.executeTransactionAsync(bgRealm -> WorkoutProgressHelper.saveOrUpdateWorkoutHistory(
                    bgRealm, idWorkout, typeWorkout, completedExercises
            ), () -> {
                Log.d(TAG, "Workout progress saved successfully");
                updatePreferences();

                // Check for new achievements after saving workout progress
                Context context = listener != null ? listener.getFragmentContext() : null;
                Log.d(TAG, "Context for achievements: " + (context != null ? "available" : "null"));
                if (context != null) {
                    checkAndShowAchievementLayout(context);
                } else {
                    Log.e(TAG, "Cannot check achievements - context is null");
                    allowFragmentToClose();
                }

            }, error -> {
                Log.e(TAG, "Error saving workout progress: " + error.getMessage());
                allowFragmentToClose();
            });
        }
    }
    
    /**
     * Check and show achievement layout
     */
    private void checkAndShowAchievementLayout(Context context) {
        AchievementDialogHelper.checkAndShowAchievementDialog(context, fragment,
            new AchievementDialogHelper.OnAchievementDialogListener() {
                @Override
                public void onAchievementDialogClosed() {
                    allowFragmentToClose();
                }

                @Override
                public void onShowCelebrationLayout(Achievement achievement) {
                    // Notify listener to show celebration layout
                    if (listener != null) {
                        listener.onShowAchievementCelebration(achievement);
                    }
                }
            });
    }
    
    /**
     * Update preferences after workout completion
     */
    private void updatePreferences() {
        if (exerciseList == null || completedExercises == null || prefsUtilsWtContext == null) {
            Log.w(TAG, "Cannot update preferences - missing required data");
            return;
        }

        int listSize = exerciseList.size();
        int completedCount = completedExercises.size();
        float percentAmountForOne = (float) 100 / listSize;
        int realProgressPercent = (int) (percentAmountForOne * completedCount);

        int recentProgress = prefsUtilsWtContext.getIntegerPreferenceProfile(idWorkout + "_progress", 0);
        if (realProgressPercent > recentProgress) {
            prefsUtilsWtContext.setIntegerPreferenceProfile(idWorkout + "_progress", realProgressPercent);
        }
        prefsUtilsWtContext.setStringPreferenceProfile(idWorkout + "_completed_exercises",
                TextUtils.join(",", completedExercises));

        if (completedCount == listSize) {
            prefsUtilsWtContext.setBooleanPreferenceProfile(idWorkout + "_completed", true);
            CollectEvent.doneWorkout(prefsUtilsWtContext);
        }

        Log.d(TAG, "Preferences updated - Progress: " + realProgressPercent + "%, Completed: " + completedCount + "/" + listSize);
    }
    
    /**
     * Allow fragment to close
     */
    public void allowFragmentToClose() {
        Log.d(TAG, "Allowing fragment to close");
        isShowingCustomLayout = false;
        canCloseFragment = true;
        closeFragment();
    }
    
    /**
     * Prevent fragment from closing
     */
    public void preventFragmentClosing() {
        canCloseFragment = false;
    }
    
    /**
     * Close the fragment
     */
    private void closeFragment() {
        if (backPressedCallback != null) {
            backPressedCallback.setEnabled(false);
        }
        
        if (listener != null) {
            listener.onFragmentShouldClose();
        } else if (fragment.getActivity() != null) {
            fragment.getActivity().onBackPressed();
        }
    }
    
    /**
     * Clean up resources
     */
    public void cleanup() {
        if (backPressedCallback != null) {
            backPressedCallback.setEnabled(false);
            backPressedCallback = null;
        }
        fragment = null;
        listener = null;
    }
}
