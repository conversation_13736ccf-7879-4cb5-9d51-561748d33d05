package com.vgfit.shefit.util.eventCollect;

import android.util.Log;

import com.vgfit.shefit.BaseApplication;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.util.eventCollect.model.GoalPurchaseUser;

import org.json.JSONException;
import org.json.JSONObject;


public class CollectEvent {
    private static final String KEY_COUNT_START_WORKOUT = "KEY_COUNT_START_WORKOUT";
    private static final String KEY_COUNT_END_WORKOUT = "KEY_COUNT_END_WORKOUT";
    private static final String KEY_COUNT_SESSION_APP = "KEY_COUNT_SESSION_APP";
    private static final String KEY_WORKOUT_LEVEL = "workLevel";
    private static final String KEY_RUN_FIRST_TIME = "KEY_RUN_FIRST_TIME";

    public static void startWorkout(PrefsUtilsWtContext prefsUtilsWtContext) {
        int currentCount = howManyWorkoutStart(prefsUtilsWtContext);
        currentCount++;
        prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_COUNT_START_WORKOUT, currentCount);
    }

    private static int howManyWorkoutStart(PrefsUtilsWtContext prefsUtilsWtContext) {
        return prefsUtilsWtContext.getIntegerPreferenceProfile(KEY_COUNT_START_WORKOUT, 0);
    }

    public static void doneWorkout(PrefsUtilsWtContext prefsUtilsWtContext) {
        int currentCount = howManyWorkoutDone(prefsUtilsWtContext);
        currentCount++;
        prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_COUNT_END_WORKOUT, currentCount);
    }

    private static int howManyWorkoutDone(PrefsUtilsWtContext prefsUtilsWtContext) {
        return prefsUtilsWtContext.getIntegerPreferenceProfile(KEY_COUNT_END_WORKOUT, 0);
    }

    public static void startAPP(PrefsUtilsWtContext prefsUtilsWtContext) {
        int currentCount = howManyStartApp(prefsUtilsWtContext);
        currentCount++;
        prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_COUNT_SESSION_APP, currentCount);
    }

    private static int howManyStartApp(PrefsUtilsWtContext prefsUtilsWtContext) {
        return prefsUtilsWtContext.getIntegerPreferenceProfile(KEY_COUNT_SESSION_APP, 0);
    }

    private static int getWorkoutLevel(PrefsUtilsWtContext prefsUtilsWtContext) {
        return prefsUtilsWtContext.getIntegerPreferenceProfile(KEY_WORKOUT_LEVEL, 0);
    }

    private static boolean isFirstTime(PrefsUtilsWtContext prefsUtilsWtContext) {
        return prefsUtilsWtContext.getBooleanPreferenceProfile(KEY_RUN_FIRST_TIME, true);
    }

    private static GoalPurchaseUser getPurchaseUser(PrefsUtilsWtContext prefsUtilsWtContext, String idPurchase) {
        GoalPurchaseUser goalPurchaseUser = new GoalPurchaseUser();
        goalPurchaseUser.setCountEndWorkout(howManyWorkoutDone(prefsUtilsWtContext));
        goalPurchaseUser.setCountStartWorkout(howManyWorkoutStart(prefsUtilsWtContext));
        goalPurchaseUser.setCountSessionApp(howManyStartApp(prefsUtilsWtContext));
        goalPurchaseUser.setIdPurchase(idPurchase);
        goalPurchaseUser.setFirstTime(isFirstTime(prefsUtilsWtContext));
        goalPurchaseUser.setWorkoutLevel(getWorkoutLevel(prefsUtilsWtContext));
        return goalPurchaseUser;
    }


}
