package com.vgfit.shefit.util.dialog;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.app.Dialog;
import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.vgfit.shefit.R;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.dialog.callback.DialogHelperCallback;

public class DialogHelper {
    private final Context context;
    private String title;
    private String message;
    private DialogHelperCallback callback;
    private boolean isOnlyOneButton;

    public DialogHelper(Context context) {
        this.context = context;
    }

    public void showDialogUniversal(String title, String message, DialogHelperCallback callback, boolean isOnlyOneButton) {
        this.title = title;
        this.message = message;
        this.callback = callback;
        this.isOnlyOneButton = isOnlyOneButton;
        showDialog();
    }

    public void showDialogExit(DialogHelperCallback callback) {
        this.title = "";
        this.message = Translate.getValue("are_you_sure_you_want_to_exit");
        this.callback = callback;
        this.isOnlyOneButton = false;
        showDialog();
    }

    public void showRestorePurchaseFailed(DialogHelperCallback callback) {
        this.title = "";
        this.message = Translate.getValue("there_is_nothing_to_be_restored");
        this.callback = callback;
        this.isOnlyOneButton = true;
        showDialog();
    }
    public void showRestorePurchaseSuccess(DialogHelperCallback callback) {
        this.title = "";
        this.message = Translate.getValue("purchases_successfully_restored");
        this.callback = callback;
        this.isOnlyOneButton = true;
        showDialog();
    }

    public void showDialog() {
        if (context == null) {
            return;
        }
        final Dialog dialog = new Dialog(context, R.style.Theme_AppCompat_Light_Dialog_Alert);
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
            dialog.setContentView(R.layout.dialog_helper);
            dialog.setCanceledOnTouchOutside(true);
            dialog.setCancelable(true);

            TextView dialogTitle = dialog.findViewById(R.id.dialog_title);
            TextView dialogMessage = dialog.findViewById(R.id.dialog_message);
            TextView btnYes = dialog.findViewById(R.id.btnYes);
            TextView btnNo = dialog.findViewById(R.id.btnNo);

            // Set translated texts
            dialogTitle.setText(title);
            dialogMessage.setText(message);
            btnYes.setText(Translate.getValue("yes"));
            btnNo.setText(Translate.getValue("no"));

            btnYes.setOnClickListener(v -> {
                setVibrate(v);
                // Reset the workout progress
                setConfirmed(true);
                dialog.dismiss();
            });

            btnNo.setOnClickListener(v -> {
                setVibrate(v);
                setConfirmed(false);
                dialog.dismiss();
            });
            if (isOnlyOneButton) {
                btnNo.setVisibility(View.GONE);
                btnYes.setText(Translate.getValue("ok"));
            }
            if(title.isEmpty()){
                dialogTitle.setVisibility(View.GONE);
            }
            dialog.show();
        }
    }

    private void setConfirmed(boolean isConfirmed) {
        if (callback != null) {
            callback.isConfirmed(isConfirmed);
        }
    }
}
