package com.vgfit.shefit.util.exo_utility;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.os.Build;
import android.service.notification.StatusBarNotification;
import android.util.Log;

import androidx.core.app.NotificationCompat;
import androidx.work.Data;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkInfo;
import androidx.work.WorkManager;

import com.google.android.exoplayer2.offline.Download;
import com.google.android.exoplayer2.offline.DownloadIndex;
import com.google.android.exoplayer2.offline.DownloadManager;
import com.vgfit.shefit.BaseApplication;
import com.vgfit.shefit.R;

import java.io.IOException;

public class DownloadHLS {
    private static final int DOWNLOAD_NOTIFICATION_ID = 1;
    private static final String CHANNEL_ID = "download_channel";
    private final Context context;
    private final NotificationManager notificationManager;
    private final DownloadManager downloadManager;

    public DownloadHLS(Context context) {
        this.context = context;
        createNotificationChannel();
        notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        downloadManager = ((BaseApplication) context.getApplicationContext()).getDownloadManager();
    }

    public void downloadManager(String uri) {

        Data inputData = new Data.Builder()
                .putString("video_url", uri)
                .build();

        OneTimeWorkRequest downloadWorkRequest = new OneTimeWorkRequest.Builder(DownloadWorker.class)
                .setInputData(inputData)
                .addTag("downloadTag")
                .build();

        WorkManager.getInstance(context).enqueue(downloadWorkRequest);

        WorkManager.getInstance(context)
                .getWorkInfosByTagLiveData("downloadTag")
                .observeForever(workInfos -> {
                    if (workInfos == null || workInfos.isEmpty()) {
                        cancelDownloadNotification();
                        return;
                    }

                    boolean allFinished = true;
                    boolean hasRunning = false;

                    for (WorkInfo workInfo : workInfos) {
                        if (workInfo.getState() == WorkInfo.State.RUNNING) {
                            hasRunning = true;
                            allFinished = false;
                            break;
                        } else if (!workInfo.getState().isFinished()) {
                            allFinished = false;
                        }
                    }

                    if (hasRunning) {
                        showDownloadNotification();
                    } else if (allFinished) {
                        cancelDownloadNotification();
                    } else {
                        cancelDownloadNotification();
                    }
                });
    }

    private void showDownloadNotification() {
        if (isNotificationVisible()) {
            return;
        }
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.mipmap.ic_launcher_nv)
                .setContentTitle("Download in progress...")
                .setContentText("Downloading video files in progress...")
                .setPriority(NotificationCompat.PRIORITY_LOW);
        notificationManager.notify(DOWNLOAD_NOTIFICATION_ID, builder.build());
    }

    public void cancelDownloadNotification() {
        notificationManager.cancel(DOWNLOAD_NOTIFICATION_ID);
    }

    private boolean isNotificationVisible() {
        StatusBarNotification[] notifications = notificationManager.getActiveNotifications();
        for (StatusBarNotification notification : notifications) {
            if (notification.getId() == DownloadHLS.DOWNLOAD_NOTIFICATION_ID) {
                return true;
            }
        }
        return false;
    }

    public boolean isVideoDownloaded(String uri) {
        DownloadIndex downloadIndex = downloadManager.getDownloadIndex();
        try {
            Download download = downloadIndex.getDownload(uri);
            return download != null && download.state == Download.STATE_COMPLETED;
        } catch (IOException e) {
            Log.e("DownloadCheck", "Error checking download status", e);
            return false;
        }
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            String name = "Download Notifications";
            String description = "Notifications for download progress";
            int importance = NotificationManager.IMPORTANCE_LOW;
            NotificationChannel channel = new NotificationChannel(CHANNEL_ID, name, importance);
            channel.setDescription(description);
            NotificationManager notificationManager = context.getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }
}
