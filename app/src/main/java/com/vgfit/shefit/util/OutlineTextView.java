package com.vgfit.shefit.util;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.text.TextPaint;
import android.util.AttributeSet;

public class OutlineTextView extends androidx.appcompat.widget.AppCompatTextView {

    private boolean drawOutline = true;

    public OutlineTextView(Context context) {
        super(context);
        init();
    }

    public OutlineTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public OutlineTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setLayerType(LAYER_TYPE_SOFTWARE, null);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (drawOutline) {
            TextPaint paint = getPaint();

            int originalColor = getCurrentTextColor();

            paint.setStyle(Paint.Style.STROKE);
            paint.setStrokeWidth(8);
            setTextColor(Color.BLACK);

            super.onDraw(canvas);

            paint.setStyle(Paint.Style.FILL);
            setTextColor(originalColor);

            super.onDraw(canvas);
        } else {
            super.onDraw(canvas);
        }
    }
}