package com.vgfit.shefit.util.animation;

import static com.vgfit.shefit.util.TextUtils.convertDpToPx;

import android.content.Context;
import android.view.View;
import android.view.animation.Animation;
import android.widget.TextView;

import com.vgfit.shefit.util.animation.callback.FinishedAnimation;

public class DynamicIsland {
    private final Context context;
    private final View mainCContainer;
    private final TextView dayCurrentInfo;

    private final int durationAnim = 350;
    private final int minimSize = 67;
    private final int maxSize = 186;


    public DynamicIsland(Context context, View mainCContainer, TextView dayCurrentInfo) {
        this.context = context;
        this.mainCContainer = mainCContainer;
        this.dayCurrentInfo = dayCurrentInfo;
    }

    public void startAnim(FinishedAnimation finishedAnimation) {

        ResizeWidthAnimation animMinimize = new ResizeWidthAnimation(mainCContainer, convertDpToPx(context, minimSize));
        animMinimize.setDuration(durationAnim);
        animMinimize.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                dayCurrentInfo.setVisibility(View.INVISIBLE);
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                ResizeWidthAnimation animMaximize = new ResizeWidthAnimation(mainCContainer, convertDpToPx(context, maxSize));
                animMaximize.setDuration(durationAnim);
                animMaximize.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {

                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        dayCurrentInfo.setVisibility(View.VISIBLE);
                        finishedAnimation.finished();
                        // TODO: 25.05.2023 set callback
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                });
                mainCContainer.startAnimation(animMaximize);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

        mainCContainer.startAnimation(animMinimize);
    }
}
