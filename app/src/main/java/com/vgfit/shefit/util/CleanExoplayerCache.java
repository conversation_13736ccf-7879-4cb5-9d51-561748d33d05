package com.vgfit.shefit.util;

import android.content.Context;
import android.util.Log;

import com.vgfit.shefit.BuildConfig;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.util.async.AsyncTask;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

public class CleanExoplayerCache {
    private final Context context;

    public CleanExoplayerCache(Context context) {
        this.context = context;
    }

    public void start() {
        PrefsUtilsWtContext prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        //Setup version clean cache
        String KEY_VERSION_CODE_APP = "KEY_VERSION_CODE_APP";
        int currentVersionCode = BuildConfig.VERSION_CODE;
        int savedVersionCodeApp = prefsUtilsWtContext.getIntegerPreferenceProfile(KEY_VERSION_CODE_APP, 150);
        if (currentVersionCode >= savedVersionCodeApp) {
            new DeleteDirectory().execute();
//            File downloadsDir = new File(context.getFilesDir(), "downloads");
////            deleteDirectoryAnother(downloadsDir);
//            try {
//                deleteDirectoryAnotherf(downloadsDir);
////                Log.d("CleanCashTest", "Folder deleted->" + deleteDirectoryV(downloadsDir));
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
        }
        prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_VERSION_CODE_APP, currentVersionCode);
    }

    private class DeleteDirectory extends AsyncTask<Void, Void, Void> {
        @Override
        protected Void doInBackground(Void unused) {
            Log.d("CleanCashTest", "Clean cache init");
            File cacheDir = new File(context.getFilesDir(), "downloads");
            try {
                deleteDirectory(cacheDir);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            return null;
        }

        @Override
        protected void onBackgroundError(Exception e) {

        }

        @Override
        protected void onPostExecute(Void unused) {
            super.onPostExecute(unused);
            Log.d("CleanCashTest", "Clean cache executed");
        }
    }

    public static void deleteDirectory(File dir) {
        Log.d("CleanCashTest", "directory to delete-->" + dir.getPath());
        if (dir.exists() && dir.isDirectory()) {
//            Log.d("CleanCashTest", "isDeleted-->" + dir.delete());
//            Log.d("CleanCashTest", "directory exist");
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        Log.d("CleanCashTest", "delete file-->" + file.getPath());
                        deleteDirectory(file);
                    } else {
                        boolean deleted = file.delete();
                        if (!deleted) {
                            Log.e("CleanCashTest", "Failed to delete file: " + file.getPath());
                        }
                    }
                }
            }

        }
    }

    public static void deleteDirectoryAnother(File directoryToBeDeleted) {
        ExecutorService executorService = Executors.newFixedThreadPool(4); // Ajustează numărul de thread-uri după nevoie

        if (directoryToBeDeleted.isDirectory()) {
            File[] files = directoryToBeDeleted.listFiles();
            if (files != null) {
                for (File file : files) {
                    executorService.execute(() -> {
                        Log.d("CleanCashTest", "Thread delete file-->" + file.getPath());
                        deleteDirectoryAnother(file);
                    }); // Apel recursiv pentru subdirectoare
                }
            }
        }

        boolean deleted = directoryToBeDeleted.delete(); // Șterge directorul sau fișierul curent
        if (!deleted) {
            Log.e("CleanCashTest", "Failed to delete file: " + directoryToBeDeleted.getPath());
        }

        executorService.shutdown(); // Începe procesul de închidere a executorului
        try {
            if (!executorService.awaitTermination(1, TimeUnit.SECONDS)) { // Așteaptă ca toate task-urile să se termine
                executorService.shutdownNow(); // Forțează închiderea dacă task-urile nu s-au terminat
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt(); // Setează statusul de interrupt a thread-ului curent
        }
    }
    public  void deleteDirectoryAnotherf(File directoryToBeDeleted) {
        ExecutorService executorService = Executors.newFixedThreadPool(4); // Ajustează numărul de thread-uri după nevoie
        executorService.execute(() -> {
            Log.d("CleanCashTest", "Thread delete file-->" + directoryToBeDeleted.getPath());
            try {
                deleteDirectoryV(directoryToBeDeleted);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }); // A

        executorService.shutdown(); // Începe procesul de închidere a executorului
        try {
            if (!executorService.awaitTermination(1, TimeUnit.SECONDS)) { // Așteaptă ca toate task-urile să se termine
                executorService.shutdownNow(); // Forțează închiderea dacă task-urile nu s-au terminat
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt(); // Setează statusul de interrupt a thread-ului curent
        }
    }

    static public boolean deleteDirectoryV(File file) throws Exception {
        Log.d("CleanCashTest", "directory to delete-->" + file.getPath());
        if (file.exists()) {

            String deleteCommand = "rm -rf " + file.getAbsolutePath();
            Runtime runtime = Runtime.getRuntime();

            Process process = runtime.exec(deleteCommand);
            process.waitFor();

            return true;
        }

        return false;

    }
}
