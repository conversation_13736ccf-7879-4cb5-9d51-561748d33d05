package com.vgfit.shefit.util;


import static com.facebook.FacebookSdk.setAdvertiserIDCollectionEnabled;
import static com.facebook.FacebookSdk.setAutoLogAppEventsEnabled;
import static com.vgfit.shefit.authorization.SecDevice.getUUID;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;

import com.facebook.FacebookSdk;
import com.facebook.LoggingBehavior;
import com.facebook.appevents.AppEventsConstants;
import com.facebook.appevents.AppEventsLogger;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.billing.BillingProcessor;


public class FacebookEvent {
    private static String KEY_APP_INSTALL = "KEY_APP_INSTALL_FB";
    private static String COUNT_VALID_SESSION = "COUNT_VALID_SESSION";

    public static void initFBSettings(Context context) {
        String appUserId = getUUID(new PrefsUtilsWtContext(context));
        setAdvertiserIDCollectionEnabled(true);
        setAutoLogAppEventsEnabled(true);
        AppEventsLogger.setUserID(appUserId);
//        FacebookSdk.setIsDebugEnabled(true);
//        FacebookSdk.addLoggingBehavior(LoggingBehavior.APP_EVENTS);
    }

    public static void sendActivateApp(Activity activity) {
        try {
            PrefsUtilsWtContext prefsUtilsWtContext = new PrefsUtilsWtContext(activity);
//            boolean appInstall = prefsUtilsWtContext.getBooleanPreferenceProfile(KEY_APP_INSTALL, true);
            int count = prefsUtilsWtContext.getIntegerPreferenceProfile(COUNT_VALID_SESSION, 0);
            boolean appInstall = count == 0;

            FacebookSdk.setIsDebugEnabled(true);
            FacebookSdk.addLoggingBehavior(LoggingBehavior.APP_EVENTS);
            setAdvertiserIDCollectionEnabled(true);
            AppEventsLogger logger = AppEventsLogger.newLogger(activity);
            if (FacebookSdk.isInitialized()) {

                if (appInstall) {
                    Bundle params = new Bundle();
                    String isEnabledTracking = !FacebookSdk.getLimitEventAndDataUsage(activity) ? AppEventsConstants.EVENT_PARAM_VALUE_YES : AppEventsConstants.EVENT_PARAM_VALUE_NO;
                    String isEnabledAdvertisingId = FacebookSdk.getAdvertiserIDCollectionEnabled() ? AppEventsConstants.EVENT_PARAM_VALUE_YES : AppEventsConstants.EVENT_PARAM_VALUE_NO;
                    params.putString("advertiser_id_collection_enabled", isEnabledAdvertisingId);
                    params.putString("application_tracking_enabled", isEnabledTracking);
                    params.putString("advertiser_tracking_enabled", AppEventsConstants.EVENT_PARAM_VALUE_YES);
                    logger.logEvent("mobile_app_install", params);
//                    prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_APP_INSTALL, false);
                    Log.d("TestFbEvent", "Event KEY_APP_INSTALL Send  isEnabledTracking-->" + isEnabledTracking);
                }
                Log.d("TestFbEvent", "Event EVENT_NAME_ACTIVATED_APP Send-->");
                logger.logEvent(AppEventsConstants.EVENT_NAME_ACTIVATED_APP);
            }
        } catch (Exception e) {
            Log.e("TestFbEvent", "Error Activated-->" + e.getMessage());
        }

    }

    public static void sendStartTrialApp(Activity activity, String productId, BillingProcessor bp) {
        if (bp != null) {
            try {
                bp.getSubscriptionListingDetails(productId, skuDetail -> {
                    try {
                        AppEventsLogger logger = AppEventsLogger.newLogger(activity);
                        Bundle params = new Bundle();
                        params.putString(AppEventsConstants.EVENT_PARAM_CURRENCY, skuDetail.currency);
                        params.putString(AppEventsConstants.EVENT_PARAM_CONTENT_TYPE, "subscribe");
                        params.putString(AppEventsConstants.EVENT_PARAM_CONTENT_ID, productId);
                        logger.logEvent(AppEventsConstants.EVENT_NAME_START_TRIAL, skuDetail.priceValue, params);
                    } catch (Exception ignored) {
                    }
                });
            } catch (Exception ignored) {
            }
        }
    }
}
