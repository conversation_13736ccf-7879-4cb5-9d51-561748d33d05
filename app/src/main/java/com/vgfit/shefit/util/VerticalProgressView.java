package com.vgfit.shefit.util;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;

import com.vgfit.shefit.R;

public class VerticalProgressView extends View {

    private Paint trackPaint;
    private Paint progressPaint;
    private RectF rectF = new RectF();
    private Path path = new Path();

    private int progress = 0; // între 0 și 100
    private int max = 100;
    private int minProgressHeightPx;

    // Material Design colors and dimensions
    private int trackColor = Color.parseColor("#F1F0EC");
    private int indicatorColor = Color.parseColor("#F09740");
    private float trackThickness = 14f;
    private float cornerRadius = 7f;

    public VerticalProgressView(Context context) {
        super(context);
        init(context, null);
    }

    public VerticalProgressView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public VerticalProgressView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        // Parse custom attributes if provided
        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.VerticalProgressView);
            trackColor = a.getColor(R.styleable.VerticalProgressView_trackColor, trackColor);
            indicatorColor = a.getColor(R.styleable.VerticalProgressView_indicatorColor, indicatorColor);
            trackThickness = a.getDimension(R.styleable.VerticalProgressView_trackThickness,
                    TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 14, getResources().getDisplayMetrics()));
            cornerRadius = a.getDimension(R.styleable.VerticalProgressView_progressCornerRadius,
                    TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 7, getResources().getDisplayMetrics()));
            a.recycle();
        }

        trackPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        trackPaint.setColor(trackColor);

        progressPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        progressPaint.setColor(indicatorColor);

        minProgressHeightPx = (int) TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP, 8, getResources().getDisplayMetrics()
        );

        // Set default progress for testing
        setProgress(50);
    }

    public void setProgress(int progress) {
        this.progress = Math.max(0, Math.min(progress, max));
        invalidate();
    }

    public int getProgress() {
        return progress;
    }

    public void setMax(int max) {
        this.max = Math.max(1, max);
        invalidate();
    }

    public int getMax() {
        return max;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        float width = getWidth();
        float height = getHeight();

        if (width <= 0 || height <= 0) return;

        // Calculate track dimensions (centered, like LinearProgressIndicator)
        float actualTrackThickness = Math.min(trackThickness, width);
        float trackLeft = (width - actualTrackThickness) / 2f;
        float trackRight = trackLeft + actualTrackThickness;

        // Draw background track (centered)
        rectF.set(trackLeft, 0, trackRight, height);
        canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, trackPaint);

        // Draw progress if any
        if (progress > 0 && max > 0) {
            // Calculate progress height
            float progressHeight = height * progress / (float) max;

            // Ensure minimum visible height for small progress values (like LinearProgressIndicator)
            if (progress <= 8) {
                float progressPercent = progress / 100f;
                float enhancedMinHeight = minProgressHeightPx * (1.2f + (progressPercent * 0.8f));
                if (progressHeight < enhancedMinHeight) {
                    progressHeight = enhancedMinHeight;
                }
            } else if (progressHeight < minProgressHeightPx) {
                progressHeight = minProgressHeightPx;
            }

            progressHeight = Math.min(progressHeight, height);

            // Draw progress from bottom (same width as track)
            rectF.set(trackLeft, height - progressHeight, trackRight, height);

            // Draw progress from bottom with rounded corners
            rectF.set(trackLeft, height - progressHeight, trackRight, height);

            if (progressHeight < height) {
                // For partial progress, use rounded corners
                canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, progressPaint);
            } else {
                // Full progress - use same corners as background
                canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, progressPaint);
            }
        }
    }
}