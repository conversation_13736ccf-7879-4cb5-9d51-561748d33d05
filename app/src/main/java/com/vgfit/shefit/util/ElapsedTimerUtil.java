package com.vgfit.shefit.util;

import android.app.Dialog;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.TextView;


public class ElapsedTimerUtil {
    private final String oneSecond ;
    private final String twoToFourSecond ;
    private final String fiveOrMoreSecond ;
    private long timeElapsed;

    public void setTimeElapsed(long timeElapsed) {
        this.timeElapsed = timeElapsed;
    }

    public ElapsedTimerUtil() {
        oneSecond = Translate.getValue("please_wait_one_second");
        twoToFourSecond = Translate.getValue("please_wait_two_to_four_seconds");
        fiveOrMoreSecond = Translate.getValue("please_wait_five_or_more_seconds");
    }

    public void displayElapseHandler(Dialog d, TextView timeElapsedTxt, TextView resend) {
        Handler handler = new Handler(Looper.getMainLooper());
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                Log.d("TestHandler", "handler active-->" + timeElapsed);
                if (d.isShowing() && timeElapsed > 0) {
                    timeElapsedTxt.setText(getSecondElapsed(timeElapsed));
                    handler.postDelayed(this, 250); // re-planifică runnable-ul la fiecare secundă
                }
                if (timeElapsedTxt != null) isShowElapsed(timeElapsedTxt, timeElapsed);
                if (resend != null) setValidResend(resend, timeElapsed);
            }
        };
        handler.post(runnable);
    }

    public String getSecondElapsed(long time) {
        String timeString = String.valueOf(time);
        String tag = "%@";
        switch ((int) time) {
            case 1:
                return oneSecond;
            case 2:
            case 3:
            case 4:
                return twoToFourSecond.replace(tag, timeString);
            default:
                return fiveOrMoreSecond.replace(tag, timeString);
        }
    }

    public void isShowElapsed(TextView timeElapsedTxt, long timeElapsed) {
        timeElapsedTxt.setVisibility(timeElapsed > 0 ? View.VISIBLE : View.GONE);
    }

    public void setValidResend(TextView resend, long timeElapsed) {
        resend.setAlpha(timeElapsed > 0 ? 0.4f : 1F);
        resend.setEnabled(timeElapsed == 0);
    }
}
