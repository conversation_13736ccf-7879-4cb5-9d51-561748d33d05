package com.vgfit.shefit.util;

import android.content.Context;
import android.util.AttributeSet;

import androidx.appcompat.widget.SwitchCompat;
import androidx.dynamicanimation.animation.DynamicAnimation;
import androidx.dynamicanimation.animation.SpringAnimation;
import androidx.dynamicanimation.animation.SpringForce;

public class CustomSwitchCompat extends SwitchCompat {

    public CustomSwitchCompat(Context context) {
        super(context);
    }

    public CustomSwitchCompat(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomSwitchCompat(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void setChecked(boolean checked) {
        if (isChecked() != checked) {
            super.setChecked(checked);
            animateThumb(checked);
        } else {
            super.setChecked(checked);
        }
    }

    private void animateThumb(boolean toCheckedState) {
        final SpringAnimation springAnim = new SpringAnimation(this, DynamicAnimation.TRANSLATION_X);

        // Set the spring's properties using SpringForce
        SpringForce springForce = new SpringForce();
        springForce.setStiffness(SpringForce.STIFFNESS_LOW);
        springForce.setDampingRatio(0.35f);
        springAnim.setSpring(springForce);
        // The following is just an example of starting the animation
        if (toCheckedState) {
            springAnim.setStartValue(-50);  // start from -50 pixels
            springAnim.animateToFinalPosition(0); // and animate back to 0
        } else {
            springAnim.setStartValue(50);   // start from 50 pixels
            springAnim.animateToFinalPosition(0); // and animate back to 0
        }
        springAnim.start();
    }
}