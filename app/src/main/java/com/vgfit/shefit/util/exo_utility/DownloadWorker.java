package com.vgfit.shefit.util.exo_utility;

import android.content.Context;
import android.net.Uri;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import com.google.android.exoplayer2.C;
import com.google.android.exoplayer2.offline.Download;
import com.google.android.exoplayer2.offline.DownloadIndex;
import com.google.android.exoplayer2.offline.DownloadManager;
import com.google.android.exoplayer2.offline.DownloadRequest;
import com.google.android.exoplayer2.util.MimeTypes;
import com.vgfit.shefit.BaseApplication;
import com.vgfit.shefit.BuildConfig;

import java.io.IOException;

public class DownloadWorker extends Worker {

    private static final String TAG = "DownloadWorker";
    private DownloadManager downloadManager;
    private int lastLoggedPercentage = -1;

    public DownloadWorker(@NonNull Context context, @NonNull WorkerParameters params) {
        super(context, params);
        downloadManager = ((BaseApplication) context.getApplicationContext()).getDownloadManager();
    }

    @NonNull
    @Override
    public Result doWork() {
        String videoUrl = getInputData().getString("video_url");
        if (videoUrl == null || videoUrl.isEmpty()) {
            Log.e(TAG, "Invalid video URL");
            return Result.failure();
        }

        try {
            String downloadId = videoUrl;
            DownloadRequest downloadRequest = new DownloadRequest.Builder(downloadId, Uri.parse(videoUrl))
                    .setMimeType(MimeTypes.APPLICATION_M3U8)
                    .build();

            DownloadIndex downloadIndex = downloadManager.getDownloadIndex();
            Download existingDownload = downloadIndex.getDownload(downloadId);
            if (existingDownload != null && existingDownload.state == Download.STATE_COMPLETED) {
                Log.d(TAG, "Video already downloaded");
                return Result.success();
            }

            downloadManager.addDownload(downloadRequest);
            if (BuildConfig.DEBUG)
                waitForDownloadCompletion(downloadId);

            return Result.success();
        } catch (Exception e) {
            Log.e(TAG, "Error downloading HLS stream", e);
            return Result.failure();
        }
    }

    private void waitForDownloadCompletion(String downloadId) {
        boolean isCompleted = false;
        while (!isCompleted) {
            try {
                DownloadIndex downloadIndex = downloadManager.getDownloadIndex();
                Download download = downloadIndex.getDownload(downloadId);
                if (download != null) {
                    switch (download.state) {
                        case Download.STATE_COMPLETED:
                            isCompleted = true;
                            Log.d(TAG, "Download completed");
                            break;
                        case Download.STATE_FAILED:
                            isCompleted = true;
                            Log.e(TAG, "Download failed");
                            break;
                        case Download.STATE_REMOVING:
                            isCompleted = true;
                            Log.d(TAG, "Download removed");
                            break;
                        default:
                            float percentDownloaded = download.getPercentDownloaded();
                            if (percentDownloaded != C.PERCENTAGE_UNSET) {
                                int percentInt = (int) percentDownloaded;
                                if (percentInt != lastLoggedPercentage) {
                                    lastLoggedPercentage = percentInt;
                                    Log.d(TAG, "Download url->" + downloadId + " percent->" + percentInt + "%");
                                }
                            } else {
                                Log.d(TAG, "Progresul descărcării este necunoscut");
                            }
                            break;
                    }
                } else {
                    Log.d(TAG, "Download not found in index for downloadId: " + downloadId);
                }
            } catch (IOException e) {
                Log.e(TAG, "Error accessing download index", e);
            }
        }
    }
}
