package com.vgfit.shefit.util.screen;

import android.content.Context;
import android.content.res.Resources;
import android.util.Log;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;

public class PresentBottomBar {

    public static boolean isPresentBottomBar(Context context, View view) {
        boolean hasBackKey = KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_BACK);
        boolean hasHomeKey = KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_HOME);
        if (hasBackKey && hasHomeKey) {
            // no navigation bar, unless it is enabled in the settings
            return false;
        } else {
            // 99% sure there's a navigation bar
            setMarginBottomView(context, view);
            return true;
        }
    }

    private static void setMarginBottomView(Context context, View view) {
        int heightBottomBar = getHeightBottomBar(context);
        if (view.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
            params.setMargins(0, 0, 0, heightBottomBar);
            view.setLayoutParams(params);
        }
    }

    public static int getHeightBottomBar(Context context) {
        Resources resources = context.getResources();
        int resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android");
        if (resourceId > 0) {
            return resources.getDimensionPixelSize(resourceId);
        }
        return 0;
    }

    public static void isProfileFirstTime(Context context, View view, boolean isFirstime) {
//        if (isFirstime) {
            setMarginHeadView(context, view);
//        }
    }

    private static void setMarginHeadView(Context context, View view) {
        int heightStatusBar = UtilsStatusBar.getStatusBarHeight(context);
        Log.e("statusTest", "Status bar height==>" + heightStatusBar);
        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
        params.setMargins(0, heightStatusBar, 0, 0);
        view.setLayoutParams(params);
    }
}
