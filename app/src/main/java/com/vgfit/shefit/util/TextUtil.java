package com.vgfit.shefit.util;


import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.View;

import java.util.HashMap;
import java.util.Locale;


public class TextUtil {
    private static final HashMap<String, String> customLanguageMap = new HashMap<>();

    static {
        customLanguageMap.put("zh-<PERSON>", "简体中文"); // Simplified Chinese
        customLanguageMap.put("zh-Hant", "繁體中文"); // Traditional Chinese
        customLanguageMap.put("ms", "Bahasa Malaysia"); // Malay
        customLanguageMap.put("fil", "Filipino"); // Filipino
        // Add more custom ISO codes if needed
    }
    public TextUtil() {
    }

    public static CharSequence colorizeSubString(String input, int color) {
        SpannableStringBuilder builder = new SpannableStringBuilder();

        // Start and end indices of the current substring
        int startIndex = 0;
        int endIndex;

        while ((endIndex = input.indexOf('<', startIndex)) != -1) {
            // Append the normal part of the string
            builder.append(input, startIndex, endIndex);

            // Update the start index to the beginning of the colored substring
            startIndex = endIndex + 1;

            // Find the end of the colored substring
            endIndex = input.indexOf('>', startIndex);

            // Make sure we found a valid closing '>'
            if (endIndex == -1) {
                throw new IllegalArgumentException("Missing closing '>' in input string: " + input);
            }

            // Extract the colored substring
            String coloredSubString = input.substring(startIndex, endIndex);

            // Colorize the substring and add it to the builder
            Spannable spannable = new SpannableStringBuilder(coloredSubString);
            spannable.setSpan(new ForegroundColorSpan(color), 0, coloredSubString.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            builder.append(spannable);

            // Update the start index to after the closing '>'
            startIndex = endIndex + 1;
        }

        // Append the rest of the input string
        builder.append(input.substring(startIndex));

        return builder;
    }

    public static Spannable getText(String fullText, Context context) {
        SpannableString spannable = new SpannableString("");
        try {
            int startTerms = fullText.indexOf("<t>") + 3;
            int endTerms = fullText.indexOf("<t>", startTerms);

            int startPrivacy = fullText.indexOf("<p>") + 3;
            int endPrivacy = fullText.indexOf("<p>", startPrivacy);

            String termsText = fullText.substring(startTerms, endTerms);
            String privacyText = fullText.substring(startPrivacy, endPrivacy);

            // Create new string without the tags
            String cleanedText = fullText.replaceAll("<t>", "").replaceAll("<p>", "");

            spannable = new SpannableString(cleanedText);

            ClickableSpan termsOfUseClickableSpan = new ClickableSpan() {
                @Override
                public void onClick(View widget) {
                    Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://vgfit.com/terms"));
                    context.startActivity(browserIntent);
                    Log.d("TestClickable", "TERMS ACCESSED");
                    setVibrate(widget);
                }
            };

            ClickableSpan privacyPolicyClickableSpan = new ClickableSpan() {
                @Override
                public void onClick(View widget) {
                    Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://vgfit.com/privacy"));
                    context.startActivity(browserIntent);
                    Log.d("TestClickable", "POLICY ACCESSED");
                    setVibrate(widget);
                }
            };

            // Apply the clickable spans to the new SpannableString based on our texts
            int newStartTerms = cleanedText.indexOf(termsText);
            int newEndTerms = newStartTerms + termsText.length();

            int newStartPrivacy = cleanedText.indexOf(privacyText);
            int newEndPrivacy = newStartPrivacy + privacyText.length();

            spannable.setSpan(termsOfUseClickableSpan, newStartTerms, newEndTerms, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannable.setSpan(privacyPolicyClickableSpan, newStartPrivacy, newEndPrivacy, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        } catch (Exception ignored) {
        }
        return spannable;
    }

    public static Spannable getText(String tag, String fullText, Context context) {
        SpannableString spannable = new SpannableString("");
        try {
            Log.d("TestSpanable", "text initial-->" + fullText);
            int startTerms = fullText.indexOf(tag) + 3;
            int endTerms = fullText.indexOf(tag, startTerms);

            String termsText = fullText.substring(startTerms, endTerms);

            // Create new string without the tags
            String cleanedText = fullText.replaceAll(tag, "");

            spannable = new SpannableString(cleanedText);

            ClickableSpan termsOfUseClickableSpan = new ClickableSpan() {
                @Override
                public void onClick(View widget) {
                    sendMessage(context, "", "");
                    setVibrate(widget);
                }
            };

            // Apply the clickable spans to the new SpannableString based on our texts
            int newStartTerms = cleanedText.indexOf(termsText);
            int newEndTerms = newStartTerms + termsText.length();


            spannable.setSpan(termsOfUseClickableSpan, newStartTerms, newEndTerms, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            Log.d("TestSpanable", "text-->" + spannable);
        } catch (Exception e) {
            Log.e("TestSpanable", "spannable error-->" + e.getMessage());
        }
        return spannable;
    }

    private static void sendMessage(Context context, String nameAction, String text) {

        Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
        emailIntent.setData(Uri.parse("mailto: <EMAIL>"));
        emailIntent.putExtra(Intent.EXTRA_SUBJECT, "Support");
        emailIntent.putExtra(Intent.EXTRA_TEXT, "Dear ...," + text);
        context.startActivity(Intent.createChooser(emailIntent, nameAction));
    }

    public static String capitalizeEachWord(String phrase) {
        if (phrase == null || phrase.isEmpty()) {
            return "";
        }

        String[] words = phrase.split("\\s+");
        StringBuilder capitalizedPhrase = new StringBuilder();

        for (String word : words) {
            if (word.length() == 0) continue; // Handle multiple spaces
            capitalizedPhrase.append(Character.toUpperCase(word.charAt(0)));
            capitalizedPhrase.append(word.substring(1).toLowerCase());
            capitalizedPhrase.append(" ");
        }

        return capitalizedPhrase.toString().trim();
    }

    public static String capitalizeFirstWord(String input) {
        if (input == null || input.length() == 0) {
            return "";
        }

        char firstLetter = Character.toUpperCase(input.charAt(0));
        String remainder = input.substring(1);

        return firstLetter + remainder;
    }

    public static String getLanguageName(String isoCode) {
        if (isoCode == null || isoCode.isEmpty()) {
            return "N/A";
        }

        // Check if there's a custom translation for this ISO code
        if (customLanguageMap.containsKey(isoCode)) {
            return capitalizeFirstLetter(customLanguageMap.get(isoCode));
        }

        // Get the language name using Locale for standard ISO codes
        Locale locale = new Locale(isoCode);
        String languageName = locale.getDisplayLanguage(locale);

        // Return the ISO code itself if the language name is empty
        return languageName.isEmpty() ? isoCode : capitalizeFirstLetter(languageName);
    }

    /**
     * Capitalizes the first letter of a text.
     *
     * @param text The text to format.
     * @return The text with the first letter capitalized, or the original text if null or empty.
     */
    private static String capitalizeFirstLetter(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        return text.substring(0, 1).toUpperCase() + text.substring(1);
    }
    public static String toSnakeCaseTranslate(String input) {
        String lower = input.toLowerCase();
        String text = Translate.getValue(lower.replace("&", "and")
                .replace(".", "")
                .replace("/", "_")
                .replace(" ", "_"));
        if (text.isEmpty())
            text = input;
        return text;
    }
}
