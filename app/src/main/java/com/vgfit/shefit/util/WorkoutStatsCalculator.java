package com.vgfit.shefit.util;

import com.vgfit.shefit.realm.WorkoutHistory;

import java.util.Calendar;
import java.util.Date;

import io.realm.Realm;
import io.realm.RealmResults;

public class WorkoutStatsCalculator {
    private static final String COMPLETED_DATE = "completedDate";

    public static WorkoutStats getDailyStats(Date date) {
        try (Realm realm = Realm.getDefaultInstance()) {
            Date startOfDay = getStartOfDay(date);
            Date endOfDay = getEndOfDay(date);

            RealmResults<WorkoutHistory> workouts = realm.where(WorkoutHistory.class)
                    .greaterThanOrEqualTo(COMPLETED_DATE, startOfDay)
                    .lessThan(COMPLETED_DATE, endOfDay)
                    .findAll();

            return calculateStats(workouts);
        }
    }

    public static WorkoutStats getWeeklyStats(Date date) {
        try (Realm realm = Realm.getDefaultInstance()) {
            Date startOfWeek = getStartOfWeek(date);
            Date endOfWeek = getEndOfWeek(date);

            RealmResults<WorkoutHistory> workouts = realm.where(WorkoutHistory.class)
                    .greaterThanOrEqualTo(COMPLETED_DATE, startOfWeek)
                    .lessThan(COMPLETED_DATE, endOfWeek)
                    .findAll();

            return calculateStats(workouts);
        }
    }

    public static WorkoutStats getMonthlyStats(Date date) {
        try (Realm realm = Realm.getDefaultInstance()) {
            Date startOfMonth = getStartOfMonth(date);
            Date endOfMonth = getEndOfMonth(date);

            RealmResults<WorkoutHistory> workouts = realm.where(WorkoutHistory.class)
                    .greaterThanOrEqualTo(COMPLETED_DATE, startOfMonth)
                    .lessThan(COMPLETED_DATE, endOfMonth)
                    .findAll();

            return calculateStats(workouts);
        }
    }

    private static WorkoutStats calculateStats(RealmResults<WorkoutHistory> workouts) {
        int totalWorkouts = workouts.size();
        int totalDuration = 0;
        int totalKcalBurned = 0;
        int totalProgress = 0;

        for (WorkoutHistory workout : workouts) {
            totalDuration += workout.getDuration();
            totalKcalBurned += workout.getKcalBurned();
            totalProgress += workout.getProgressPercentage();
        }

        int averageProgress = totalWorkouts > 0 ? totalProgress / totalWorkouts : 0;

        return new WorkoutStats(totalWorkouts, totalDuration, totalKcalBurned, averageProgress);
    }

    public static Date getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }


    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }


    public static Date getStartOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date getEndOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek() + 6);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    public static Date getStartOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date getEndOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    public static class WorkoutStats {
        private final int totalWorkouts;
        private final int totalDuration;
        private final int totalKcalBurned;
        private final int averageProgress;

        public WorkoutStats(int totalWorkouts, int totalDuration, int totalKcalBurned, int averageProgress) {
            this.totalWorkouts = totalWorkouts;
            this.totalDuration = totalDuration;
            this.totalKcalBurned = totalKcalBurned;
            this.averageProgress = averageProgress;
        }

        public int getTotalWorkouts() {
            return totalWorkouts;
        }

        public int getTotalDuration() {
            return totalDuration;
        }

        public int getTotalKcalBurned() {
            return totalKcalBurned;
        }

        public int getAverageProgress() {
            return averageProgress;
        }

        /**
         * Returnează durata formatată ca string (ex: "1h 30m" sau "45m")
         */
        public String getFormattedDuration() {
            int hours = totalDuration / 3600;
            int minutes = (totalDuration % 3600) / 60;

            if (hours > 0) {
                return hours + "h " + minutes + "m";
            } else {
                return minutes + "m";
            }
        }
    }
}