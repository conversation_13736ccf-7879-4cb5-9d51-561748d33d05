package com.vgfit.shefit.util;

public class CapitalizeFirstLetter {
    public static String capitaliseName(String name) {
        if (name != null && name.length() > 0) {
            String[] collect = name.split(" ");
            StringBuilder returnName = new StringBuilder();
            for (int i = 0; i < collect.length; i++) {
                collect[i] = collect[i].trim().toLowerCase();
                if (!collect[i].isEmpty()) {
                    returnName.append(collect[i].substring(0, 1).toUpperCase()).append(collect[i].substring(1)).append(" ");
                }
            }
            return returnName.toString().trim();
        } else return "";
    }

    public static String capitaliseOnlyFirstLetter(String data) {
        if (data != null && data.length() > 0)
            return data.substring(0, 1).toUpperCase() + data.substring(1).toLowerCase();
        else return "";
    }
}