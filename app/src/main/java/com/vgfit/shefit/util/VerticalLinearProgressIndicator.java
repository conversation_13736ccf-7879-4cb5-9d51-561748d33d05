package com.vgfit.shefit.util;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;

import com.vgfit.shefit.R;

/**
 * Vertical version of LinearProgressIndicator that mimics Material Design exactly
 */
public class VerticalLinearProgressIndicator extends View {

    private Paint trackPaint;
    private Paint indicatorPaint;
    private RectF rectF;

    // Default values matching LinearProgressIndicator
    private int trackColor = 0xFFE0E0E0;  // Light gray
    private int indicatorColor = 0xFF6200EE;  // Material purple
    private float trackThickness;
    private float trackCornerRadius;

    private int progress = 0;
    private int max = 100;

    public VerticalLinearProgressIndicator(Context context) {
        this(context, null);
    }

    public VerticalLinearProgressIndicator(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public VerticalLinearProgressIndicator(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        // Default values matching LinearProgressIndicator (4dp thickness)
        trackThickness = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 14,
                getResources().getDisplayMetrics());
        trackCornerRadius = trackThickness / 6f;  // Fully rounded like LinearProgressIndicator

        // Parse custom attributes if provided
        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.VerticalProgressView);
            trackColor = a.getColor(R.styleable.VerticalProgressView_trackColor, trackColor);
            indicatorColor = a.getColor(R.styleable.VerticalProgressView_indicatorColor, indicatorColor);
            trackThickness = a.getDimension(R.styleable.VerticalProgressView_trackThickness, trackThickness);
            trackCornerRadius = a.getDimension(R.styleable.VerticalProgressView_progressCornerRadius, trackCornerRadius);
            a.recycle();
        }

        // Initialize paints
        trackPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        trackPaint.setColor(trackColor);
        trackPaint.setStyle(Paint.Style.FILL);

        indicatorPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        indicatorPaint.setColor(indicatorColor);
        indicatorPaint.setStyle(Paint.Style.FILL);

        rectF = new RectF();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        int width = getWidth();
        int height = getHeight();

        if (width <= 0 || height <= 0) return;

        // Use the exact track thickness specified, don't limit it
        // This allows for proper thin progress bars
        float actualTrackThickness = trackThickness;
        float actualCornerRadius = trackCornerRadius;

        // Calculate track bounds (centered horizontally)
        float trackLeft = (width - actualTrackThickness) / 2f;
        float trackRight = trackLeft + actualTrackThickness;

        // Ensure we don't draw outside bounds
        if (trackLeft < 0) {
            trackLeft = 0;
            trackRight = width;
            actualTrackThickness = width;
            actualCornerRadius = Math.min(trackCornerRadius, actualTrackThickness / 2f);
        }

        // Draw track background (full height)
        rectF.set(trackLeft, 0, trackRight, height);
        canvas.drawRoundRect(rectF, actualCornerRadius, actualCornerRadius, trackPaint);

        // Draw progress indicator (from bottom up)
        if (progress > 0 && max > 0) {
            float progressRatio = (float) progress / max;
            float progressHeight = height * progressRatio;

            if (progressHeight > 0) {
                float centerX = (trackLeft + trackRight) / 2f;
                float radius = (trackRight - trackLeft) / 2f;

                float progressTop = height - progressHeight;
                float progressBottom = height;

                if (progressHeight <= radius) {
                    // Draw partial bottom arc only
                    float sweepAngle = (progressHeight / radius) * 180f;
                    Path partialArc = new Path();
                    partialArc.moveTo(trackLeft, progressBottom);
                    partialArc.arcTo(new RectF(trackLeft, progressBottom - 2 * radius, trackRight, progressBottom), 180, -sweepAngle);
                    partialArc.lineTo(centerX, progressBottom);
                    partialArc.close();
                    canvas.drawPath(partialArc, indicatorPaint);
                } else {
                    // Draw the middle rectangle
//                    rectF.set(trackLeft, progressTop + radius, trackRight, progressBottom - radius);
//                    canvas.drawRect(rectF, indicatorPaint);

                    // Draw bottom semicircle
                    Path bottomCap = new Path();
                    bottomCap.moveTo(trackLeft, progressBottom - radius);
                    bottomCap.arcTo(new RectF(trackLeft, progressBottom - 2 * radius, trackRight, progressBottom), 180, -180);
                    bottomCap.close();
                    canvas.drawPath(bottomCap, indicatorPaint);

                    // Draw top semicircle
                    Path topCap = new Path();
                    topCap.moveTo(trackLeft, progressTop + radius);
                    topCap.arcTo(new RectF(trackLeft, progressTop, trackRight, progressTop + 2 * radius), 180, 180);
                    topCap.close();
                    canvas.drawPath(topCap, indicatorPaint);
                }
            }
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        // Default size like LinearProgressIndicator
        int defaultWidth = (int) (trackThickness + getPaddingLeft() + getPaddingRight());
        int defaultHeight = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 48,
                getResources().getDisplayMetrics());

        int width = resolveSize(defaultWidth, widthMeasureSpec);
        int height = resolveSize(defaultHeight, heightMeasureSpec);

        // Ensure we have enough width for the track thickness
        // If the specified width is smaller than track thickness, adjust
        int minWidth = (int) trackThickness + getPaddingLeft() + getPaddingRight();
        if (width < minWidth) {
            width = minWidth;
        }

        setMeasuredDimension(width, height);
    }

    // Public API matching LinearProgressIndicator
    public void setProgress(int progress) {
        this.progress = Math.max(0, Math.min(progress, max));
        invalidate();
    }

    public int getProgress() {
        return progress;
    }

    public void setMax(int max) {
        this.max = Math.max(1, max);
        invalidate();
    }

    public int getMax() {
        return max;
    }

    public void setTrackColor(int color) {
        this.trackColor = color;
        trackPaint.setColor(color);
        invalidate();
    }

    public void setIndicatorColor(int color) {
        this.indicatorColor = color;
        indicatorPaint.setColor(color);
        invalidate();
    }

    public void setTrackThickness(float thickness) {
        this.trackThickness = thickness;
        this.trackCornerRadius = thickness / 2f;  // Keep fully rounded
        requestLayout();
    }
}
