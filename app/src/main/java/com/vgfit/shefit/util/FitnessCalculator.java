package com.vgfit.shefit.util;

import android.content.Context;
import android.util.Log;

import com.google.android.exoplayer2.C;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;

import java.text.DecimalFormat;

public class FitnessCalculator {
    private static final String KEY_AGE_VALUE = "KEY_AGE_VALUE_";
    private static final String KEY_WEIGHT_VALUE = "KEY_WEIGHT_VALUE_F";
    private static final String KEY_HEIGHT_VALUE = "KEY_HEIGHT_VALUE_F";

    private FitnessCalculator() {
        //empty constructor
    }

    public static int getDailySteps(Context context) {
        PrefsUtilsWtContext prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        String goalString = prefsUtilsWtContext.getStringPreferenceProfile("active_daily_workout", "1");
        float currentWeight = prefsUtilsWtContext.getFloatPreference(KEY_WEIGHT_VALUE + "1", 60);
        float targetWeight = prefsUtilsWtContext.getFloatPreference(KEY_WEIGHT_VALUE + "2", 50);

        int goal = Integer.parseInt(goalString);
        int steps = 10_000;
        int age = prefsUtilsWtContext.getIntegerPreference(KEY_AGE_VALUE, 20);
        boolean isTeenager = age >= 14 && age <= 19;
        int teenagersCoeff = isTeenager ? 2_000 : 0;
        steps += teenagersCoeff;
        boolean isLosingWeight = goal == 1 || currentWeight - targetWeight >= 5;
        int goalCoeff = isLosingWeight ? 2_500 : 0;
        steps += goalCoeff;
        return steps;
    }

    public static int getDailyCalories(Context context) {
        PrefsUtilsWtContext prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        String goalString = prefsUtilsWtContext.getStringPreferenceProfile("active_daily_workout", "1");
        float currentWeight = prefsUtilsWtContext.getFloatPreference(KEY_WEIGHT_VALUE + "1", 60);
        float targetWeight = prefsUtilsWtContext.getFloatPreference(KEY_WEIGHT_VALUE + "2", 50);
        float height = prefsUtilsWtContext.getFloatPreference(KEY_HEIGHT_VALUE, 165);
        int age = prefsUtilsWtContext.getIntegerPreference(KEY_AGE_VALUE, 20);
        int goal = Integer.parseInt(goalString);
        Log.d("TestDailyWater", " sportActivity-->" + getSportActivity(context));
        double weightCoeff = currentWeight * 10;
        double heightCoeff = (double) (height * 6.25);
        double ageCoeff = (double) (age * 5);
        int genderCoeff = -161;
        double calories = weightCoeff + heightCoeff - ageCoeff + genderCoeff;
        double activityCoefficient;
        switch (getSportActivity(context)) {
            case 1:
                activityCoefficient = 1.7;
                break;
            case 4:
                activityCoefficient = 1.5;
                break;
            default:
                activityCoefficient = 1.3;

        }
        calories = (double) Math.round(calories * activityCoefficient);
        double goalCoeff = 1;
        if (goal == 1) goalCoeff = 0.9;
        if (goal == 2) goalCoeff = 1.1;
        if (goal == 3) goalCoeff = 1.3;
        if (goal == 4) goalCoeff = 1.1;
        calories = (double) Math.round(calories * goalCoeff);
        if (currentWeight > 0) {
            calories = (int) Math.round(calories * ((double) targetWeight / currentWeight));
        }
        return (int) calories;
    }

    private static int getSportActivity(Context context) {
        final String KEY_SPORT_LATELY = "chooseSport";
        PrefsUtilsWtContext pf = new PrefsUtilsWtContext(context);
        for (int i = 1; i < 5; i++) {
            if (pf.getBooleanPreference(KEY_SPORT_LATELY + i, false)) return i;
        }
        return 2;
    }

    public static double getDailyWater(Context context) {
        double finalValue = 1.11;
        try {
            PrefsUtilsWtContext prefsUtilsWtContext = new PrefsUtilsWtContext(context);
            float weight = prefsUtilsWtContext.getFloatPreference(KEY_WEIGHT_VALUE + "1", 60);
            Log.d("TestDailyWater", " weight-->" + weight);
            double activityCoefficient;
            switch (getSportActivity(context)) {
                case 1:
                    activityCoefficient = 6.96;
                    break;
                case 4:
                    activityCoefficient = 4.96;
                    break;
                default:
                    activityCoefficient = 2;

            }
            double trainingAddition = weight * activityCoefficient;
            double value = weight * 33 + trainingAddition - 400;
            double volume = (value / 1000);
            DecimalFormat df = new DecimalFormat("#.##");
            String formattedValue = df.format(volume);// formattedValue va fi "10.12"
            formattedValue = formattedValue.replace(',', '.');
            finalValue = Double.parseDouble(formattedValue);
        } catch (Exception e) {
            Log.e("TestDailyWater", " water error->" + e.getMessage());
        }

        return finalValue;
    }
}
