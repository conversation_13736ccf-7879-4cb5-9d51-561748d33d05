package com.vgfit.shefit.util;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.widget.TextView;

import java.lang.reflect.Field;


public class TextMarqueView extends TextView {

    private static final float NEW_GAP = 0F;

    Object marqueeObject;
    Field mStatusField;
    Field mGhostStartField;
    Field mMaxScrollField;
    Field mGhostOffsetField;
    Field mFadeStopField;
    Field mMaxFadeScrollField;

    public TextMarqueView(Context context) {
        super(context);
        setSelected(true);
    }

    public TextMarqueView(Context context, AttributeSet attrs) {
        super(context, attrs);
        setSelected(true);
    }

    public TextMarqueView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setSelected(true);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        try {
            initMarqueeObject();

            if (didMarqueeRestart()) {
                // We need to update the values each time it restarts
                updateMarqueeFieldValues();
            }
        } catch (Exception exception) {
        }

        super.onDraw(canvas);
    }

    private void initMarqueeObject() throws Exception {
        if (marqueeObject == null) {
            Field marqueeField = getClass().getSuperclass().getSuperclass().getDeclaredField("mMarquee"); // use if extending from AppCompatTextView
            // Field marqueeField = getClass().getSuperclass().getDeclaredField("mMarquee"); // use if extending from TextView
            marqueeField.setAccessible(true);
            marqueeObject = marqueeField.get(this);
            initMarqueeFields();
        }
    }

    private void initMarqueeFields() throws Exception {
        mStatusField = marqueeObject.getClass().getDeclaredField("mStatus");
        mStatusField.setAccessible(true);
        mGhostStartField = marqueeObject.getClass().getDeclaredField("mGhostStart");
        mGhostStartField.setAccessible(true);
        mMaxScrollField = marqueeObject.getClass().getDeclaredField("mMaxScroll");
        mMaxScrollField.setAccessible(true);
        mGhostOffsetField = marqueeObject.getClass().getDeclaredField("mGhostOffset");
        mGhostOffsetField.setAccessible(true);
        mFadeStopField = marqueeObject.getClass().getDeclaredField("mFadeStop");
        mFadeStopField.setAccessible(true);
        mMaxFadeScrollField = marqueeObject.getClass().getDeclaredField("mMaxFadeScroll");
        mMaxFadeScrollField.setAccessible(true);
    }

    private boolean didMarqueeRestart() throws Exception {
        byte currentState = mStatusField.getByte(marqueeObject);
        return currentState == 0x1; // 0x1 is the byte object for the MARQUEE_STARTING state
    }

    private void updateMarqueeFieldValues() throws Exception {
        float textWidth = (float) (getWidth() - getCompoundPaddingLeft() - getCompoundPaddingRight());
        float originalGap = textWidth / 3.0F;
        // We have to calculate the lineWidth based on the original value
        float originalValueStartValue = mGhostStartField.getFloat(marqueeObject);
        float lineWidth = originalValueStartValue + textWidth - originalGap;
        float mGhostStart = lineWidth - textWidth + NEW_GAP;
        mGhostStartField.setFloat(marqueeObject, mGhostStart);
        mMaxScrollField.setFloat(marqueeObject, mGhostStart + textWidth);
        mGhostOffsetField.setFloat(marqueeObject, lineWidth + NEW_GAP);
        mFadeStopField.setFloat(marqueeObject, lineWidth);
        mMaxFadeScrollField.setFloat(marqueeObject, mGhostStart + lineWidth + lineWidth);
    }
}