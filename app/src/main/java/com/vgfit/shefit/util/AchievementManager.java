package com.vgfit.shefit.util;

import android.content.Context;
import android.util.Log;

import com.vgfit.shefit.realm.Achievement;

import java.util.List;

/**
 * Manager for handling achievements after completing workouts
 */
public class AchievementManager {
    private AchievementManager() {
        // Private constructor to prevent instantiation
    }

    private static final String TAG = "AchievementManager";

    /**
     * Checks and displays achievements after completing a workout
     * This method should be called after a workout has been saved to WorkoutHistory
     *
     * @param context Context for displaying dialogs or notifications
     */
    public static void checkAchievementsAfterWorkout(Context context) {
        try {
            // Check for newly unlocked achievements
            List<Achievement> newAchievements = AchievementHelper.checkAndUnlockAchievements();

            if (!newAchievements.isEmpty()) {
                Log.d(TAG, "New achievements unlocked: " + newAchievements.size());

                // Display newly unlocked achievements
                for (Achievement achievement : newAchievements) {
                    showAchievementUnlocked(context, achievement);
                    Log.d(TAG, "Achievement unlocked: " + achievement.getBadgeNameKey() +
                          " (Required: " + achievement.getCalories() + " calories)");
                }
            } else {
                Log.d(TAG, "No new achievements unlocked after workout");
            }

            // Display progress towards next achievement
            AchievementHelper.AchievementProgress progress = AchievementHelper.getProgressToNextAchievement();
            if (!progress.isAllAchievementsUnlocked()) {
                Log.d(TAG, "Progress to next achievement: " + progress.getProgressPercentage() +
                      "% (" + progress.getCaloriesNeeded() + " calories needed)");
            } else {
                Log.d(TAG, "All achievements unlocked! Total calories: " + progress.getCurrentCalories());
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error checking achievements: " + e.getMessage());
        }
    }

    /**
     * Checks achievements using only 100% completed workouts
     */
    public static void checkAchievementsAfterWorkoutCompleted(Context context) {
        try {
            List<Achievement> newAchievements = AchievementHelper.checkAndUnlockAchievements(true);
            
            if (!newAchievements.isEmpty()) {
                for (Achievement achievement : newAchievements) {
                    showAchievementUnlocked(context, achievement);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking completed achievements: " + e.getMessage());
        }
    }

    /**
     * Displays an unlocked achievement (can be implemented with dialog, toast, or notification)
     */
    private static void showAchievementUnlocked(Context context, Achievement achievement) {
        // TODO: Implement achievement display
        // You can use:
        // - Custom dialog with animation
        // - Toast message
        // - Notification
        // - Fragment overlay

        String badgeName = Translate.getValue(achievement.getBadgeNameKey());
        String description = Translate.getValue(achievement.getDescriptionKey());

        Log.i(TAG, "🏆 ACHIEVEMENT UNLOCKED: " + badgeName);
        Log.i(TAG, "📝 " + description);

        // Simple example with Toast (you can replace with custom dialog)
        // Toast.makeText(context, "🏆 Achievement Unlocked: " + badgeName, Toast.LENGTH_LONG).show();
    }

    /**
     * Gets information about current progress
     */
    public static String getProgressInfo() {
        AchievementHelper.AchievementProgress progress = AchievementHelper.getProgressToNextAchievement();
        
        if (progress.isAllAchievementsUnlocked()) {
            return "🎉 All achievements unlocked! Total: " + progress.getCurrentCalories() + " calories burned!";
        } else {
            Achievement next = progress.getNextAchievement();
            String nextBadgeName = Translate.getValue(next.getBadgeNameKey());
            return "🔥 " + progress.getCurrentCalories() + " calories burned | " +
                   "Next: " + nextBadgeName + " (" + progress.getCaloriesNeeded() + " calories needed)";
        }
    }

    /**
     * Gets the total number of unlocked achievements
     */
    public static int getUnlockedAchievementsCount() {
        return AchievementHelper.getUnlockedAchievements().size();
    }

    /**
     * Gets the total number of achievements
     */
    public static int getTotalAchievementsCount() {
        return AchievementHelper.getAllAchievements().size();
    }
}
