package com.vgfit.shefit.util.offline_reader;

import android.content.Context;
import android.util.Log;

import com.google.gson.reflect.TypeToken;
import com.google.gson.Gson;
import com.vgfit.shefit.json.model.TranslatorData;
import com.vgfit.shefit.realm.Category;
import com.vgfit.shefit.realm.CoverDayPlan;
import com.vgfit.shefit.realm.CoverVideoPlan;
import com.vgfit.shefit.realm.DaysWorkoutsPlan;
import com.vgfit.shefit.realm.Languages;
import com.vgfit.shefit.realm.NutritionPlan;
import com.vgfit.shefit.realm.PhotoMeal;
import com.vgfit.shefit.realm.Superset;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.util.ArrayList;

public class ReaderOffline {

    public static <T> T loadResponse(Context context, ModelType modelType) {
        Gson gson = new Gson();
        Type type;
        String fileName;
        switch (modelType) {

            case TRANSLATOR_DATA:
                type = new TypeToken<ArrayList<TranslatorData>>() {
                }.getType();
                fileName = getPathFile("lang_data_en");
                break;
            case LANGUAGE_DATA:
                type = new TypeToken<ArrayList<Languages>>() {
                }.getType();
                fileName = getPathFile("language_data");
                break;
            case SUPERSETS_DATA:
                type = new TypeToken<ArrayList<Superset>>() {
                }.getType();
                fileName = getPathFile("supersets_data");
                break;
            case CATEGORY_DATA:
                type = new TypeToken<ArrayList<Category>>() {
                }.getType();
                fileName = getPathFile("category_data");
                break;
            case NUTRITION_DATA:
                type = new TypeToken<ArrayList<NutritionPlan>>() {
                }.getType();
                fileName = getPathFile("nutrition_data");
                break;
            case PHOTO_MEAL_DATA:
                type = new TypeToken<ArrayList<PhotoMeal>>() {
                }.getType();
                fileName = getPathFile("photo_meal_data");
                break;
            case COVER_VIDEO_PLAN_DATA:
                type = new TypeToken<ArrayList<CoverVideoPlan>>() {
                }.getType();
                fileName = getPathFile("cover_video_plan");
                break;
            case COVER_DAY_PLAN_DATA:
                type = new TypeToken<ArrayList<CoverDayPlan>>() {
                }.getType();
                fileName = getPathFile("cover_day_plan");
                break;

            default:
                throw new IllegalArgumentException("Invalid model type");
        }

        try (InputStream stream = context.getAssets().open(fileName);
             InputStreamReader reader = new InputStreamReader(stream)) {
            return gson.fromJson(reader, type);
        } catch (IOException e) {
            Log.e("TestOffline", "offline error->" + e.getMessage());
        }

        return null;
    }

    public static <T> T loadResponse(Context context, int experience) {
        Gson gson = new Gson();
        Type type;
        String fileName;
        type = new TypeToken<DaysWorkoutsPlan>() {
        }.getType();
        fileName = getPathFile("workout_plan_days_" + experience);
        try (InputStream stream = context.getAssets().open(fileName);
             InputStreamReader reader = new InputStreamReader(stream)) {
            return gson.fromJson(reader, type);
        } catch (IOException e) {
            Log.e("TestOffline", "offline error->" + e.getMessage());
        }
        return null;
    }

    private static String getPathFile(String nameFile) {
        final String extension = ".json";
        final String path = "offlineJson/";
        return path + nameFile + extension;
    }
}
