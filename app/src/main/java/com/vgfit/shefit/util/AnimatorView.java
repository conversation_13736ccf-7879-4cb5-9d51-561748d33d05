package com.vgfit.shefit.util;

import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.view.animation.ScaleAnimation;

public class AnimatorView {
    private ScaleAnimation scalerUpAlpha;
    private ScaleAnimation scalerDownAlpha;
    private AnimationSet animationUpAlpha;
    private AnimationSet animationDownAlpha;
    private final long durationAlpha = 500;
    private final long durationFade = 250;
    private Animation fadeIn;

    public void initScaleUpAlpha() {
        fadeIn = new AlphaAnimation(0.5f, 1);
        fadeIn.setDuration(durationFade);

        scalerUpAlpha = new ScaleAnimation(1.0f, 1.1f, 1.0f, 1.1f, Animation.RELATIVE_TO_SELF, 0.5f, // Pivot point of X scaling
                Animation.RELATIVE_TO_SELF, 0.5f);
        scalerUpAlpha.setDuration(durationAlpha);

        animationUpAlpha = new AnimationSet(true);
        animationUpAlpha.addAnimation(scalerUpAlpha);
        animationUpAlpha.addAnimation(fadeIn);
        animationUpAlpha.setFillAfter(true);
    }

    public void initScaleDownAlpha() {
        fadeIn = new AlphaAnimation(0.5f, 1);
        fadeIn.setDuration(durationFade);

        scalerDownAlpha = new ScaleAnimation(1.1f, 1.0f, 1.1f, 1.0f, Animation.RELATIVE_TO_SELF, 0.5f, // Pivot point of X scaling
                Animation.RELATIVE_TO_SELF, 0.5f);
        scalerDownAlpha.setDuration(durationAlpha);

        animationDownAlpha = new AnimationSet(true);
        animationDownAlpha.addAnimation(scalerDownAlpha);
        animationDownAlpha.addAnimation(fadeIn);
        animationDownAlpha.setFillAfter(true);
    }


    public void startScaleUpAlpha(View view) {
        if (animationUpAlpha != null)
            view.startAnimation(animationUpAlpha);
    }

    public void startScaleDownAlpha(View view) {
        if (animationDownAlpha != null)
            view.startAnimation(animationDownAlpha);
    }

}
