package com.vgfit.shefit.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class TimestampConverter {
    public long transform(String createdAt) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
        Date testDate = null;
        try {
            testDate = sdf.parse(createdAt);
        } catch (ParseException ignored) {
        }
        if (testDate != null) {
            return testDate.getTime();
        } else return -1;
    }
}
