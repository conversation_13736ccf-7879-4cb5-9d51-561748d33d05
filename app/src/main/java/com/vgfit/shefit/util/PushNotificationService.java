package com.vgfit.shefit.util;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.media.AudioAttributes;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.vgfit.shefit.R;
import com.vgfit.shefit.StartScreen;

public class PushNotificationService {
    private final Context context;

    public PushNotificationService(Context context) {
        this.context = context;
    }

    private final String CHANNEL_ID = "SHEFIT_REMINDER_CHANNEL_ID";
//    int random = ThreadLocalRandom.current().nextInt(1, 2023);
    private int REQUEST_CODE = 123;

    public void sendPushNotification(String message) {
        Log.e("TestEvent","Send Push Notification");
        Uri sound = Settings.System.DEFAULT_NOTIFICATION_URI;
        Intent intent = new Intent(context, StartScreen.class);
        PendingIntent contentIntent;
        contentIntent = PendingIntent.getActivity(context, (int)System.currentTimeMillis(), intent, PendingIntent.FLAG_IMMUTABLE);
        NotificationManager mNotificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        Notification builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSound(sound)
                .setSmallIcon(R.mipmap.ic_launcher_nv)
                .setContentTitle(context.getString(R.string.app_name))
                .setContentText(message)
                .setContentIntent(contentIntent)
                .setAutoCancel(true)
                .setChannelId(CHANNEL_ID).build();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            String name = "FEMALE_VGFIT";
            int importance = NotificationManager.IMPORTANCE_HIGH;
            NotificationChannel mChannel = new NotificationChannel(CHANNEL_ID, name, importance);
            AudioAttributes attributes = new AudioAttributes.Builder()
                    .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                    .setUsage(AudioAttributes.USAGE_ALARM)
                    .build();
            mChannel.setSound(sound, attributes);
            mChannel.enableVibration(true);
            mNotificationManager.createNotificationChannel(mChannel);

        }
        mNotificationManager.notify(REQUEST_CODE, builder);
    }
}
