package com.vgfit.shefit.util;

import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.util.LruCache;
import android.widget.TextView;

import com.vgfit.shefit.realm.Languages;
import com.vgfit.shefit.realm.Localizations;
import com.vgfit.shefit.realm.LocalizationsFreeRealm;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.realm.Realm;
import io.realm.RealmResults;

public class Translate {
    private Realm realm;
    public static HashMap<String, LocalizationsFreeRealm> listLocalization;
    private static final LruCache<String, String> cache = new LruCache<>(4000);
    private static String language = "";

    public static String getLanguage() {
        return language;
    }

    public static void setLanguage(String language) {
        Translate.language = language;
    }

    public Translate() {
        listLocalization = new HashMap<>();
        realm = Realm.getDefaultInstance();
    }

    public static void setCache(Map<String, String> cacheTranslate, String language) {
        for (Map.Entry<String, String> entry : cacheTranslate.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (key != null && value != null) {
                cache.put(key, value);
            }
        }
        setLanguage(language);
    }

    public void initTranslate(String languageName) {
        ArrayList<Languages> listLanguages = new ArrayList<>(realm.where(Languages.class).findAll());
        ArrayList<String> listLanguageString = new ArrayList<>();

        for (int i = 0; i < listLanguages.size(); i++) {
            listLanguageString.add(listLanguages.get(i).getIsoOneCode());
        }
        if (!listLanguageString.contains(languageName)) languageName = "en";
        language = languageName;
        RealmResults<Localizations> loc = realm.where(Localizations.class).equalTo("languageName", language).findAll();
        ArrayList<Localizations> listLocal = new ArrayList<>(loc);
        if (listLocalization.isEmpty()) {
            loc = realm.where(Localizations.class).equalTo("languageName", languageName).findAll();
            listLocal.addAll(loc);
        }
        for (Localizations localizations : listLocal) {

            LocalizationsFreeRealm localizationsFreeRealm = new LocalizationsFreeRealm();
            localizationsFreeRealm.setKey(localizations.getKey());
            localizationsFreeRealm.setLanguageName(localizations.getLanguageName());
            localizationsFreeRealm.setValue(localizations.getValue());

            listLocalization.put(localizations.getKey(), localizationsFreeRealm);
        }
        listLanguages.clear();
        listLanguageString.clear();
        realm.close();
    }

    public static String getValue(String key) {
        if (key != null && cache.get(key) != null) {
            return cache.get(key);
        }
        if (key == null || key.isEmpty()) {
            return "";
        }
        String languageCode = language.equals("en") ? "" : "_" + language;
        if (listLocalization != null) {
            LocalizationsFreeRealm result = listLocalization.get(key + languageCode);
            if (result == null) {
                result = listLocalization.get(key);
            }
            if (result != null && result.getValue() != null && !result.getValue().isEmpty()) {
                return result.getValue();
            }
            return getDefaultTranslate(key);
        }

        return "";
    }

    private static String getDefaultTranslate(String key) {
        Realm realm = null;
        try {
            realm = Realm.getDefaultInstance();
            Localizations loc = realm.where(Localizations.class).equalTo("key", key).findFirst();
            if (loc != null && loc.getValue() != null && !loc.getValue().isEmpty()) {
                return loc.getValue();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (realm != null && !realm.isClosed()) {
                realm.close();
            }
        }
        return "";
    }

    public static String getValueLine(String key, List<Integer> positionLine, boolean isForce) {
        String languageCode;
        LocalizationsFreeRealm result;
        String lang = getLanguage();
        if (language.equals("en")) languageCode = "";
        else languageCode = "_" + language;
        if (listLocalization != null) {
            result = listLocalization.get(key + languageCode);
            if (result == null) result = listLocalization.get(key);
            if (result != null)
                if (lang.contains("en") || isForce)
                    return getSplitWord(result.getValue(), positionLine);
                else
                    return result.getValue();
            else return "";
        } else {
            return "";
        }
    }

    private static String getSplitWord(String value, List<Integer> posList) {
        String[] cuvinte = value.split("\\s+");
        StringBuilder finalSequence = new StringBuilder();

        for (int i = 0; i < cuvinte.length; i++) {
            finalSequence.append(cuvinte[i]);
            if (posList.contains(i + 1) && !(finalSequence.charAt(finalSequence.length() - 1) == '\n')) {
                finalSequence.append("\n");
            } else if (i < cuvinte.length - 1) {
                finalSequence.append(" ");
            }
        }

        return finalSequence.toString();
    }

    private static String getSplitWord(String value) {
        String[] cuvinte = value.split("\\s+");
        StringBuilder finalSequence = new StringBuilder();

        for (int i = 0; i < cuvinte.length; i++) {
            finalSequence.append(cuvinte[i]);
            finalSequence.append("\n");
        }

        return finalSequence.toString();
    }

    private static String extractSelect(String propozitie) {
        Pattern pattern = Pattern.compile("<(.*?)>");
        Matcher matcher = pattern.matcher(propozitie);

        if (matcher.find()) {
            return matcher.group(1);
        }

        return "";
    }

    public static CharSequence colorizeSubString(String input, int color) {
        SpannableStringBuilder builder = new SpannableStringBuilder();

        // Start and end indices of the current substring
        int startIndex = 0;
        int endIndex;

        while ((endIndex = input.indexOf('<', startIndex)) != -1) {
            // Append the normal part of the string
            builder.append(input, startIndex, endIndex);

            // Update the start index to the beginning of the colored substring
            startIndex = endIndex + 1;

            // Find the end of the colored substring
            endIndex = input.indexOf('>', startIndex);

            // Make sure we found a valid closing '>'
            if (endIndex == -1) {
                throw new IllegalArgumentException("Missing closing '>' in input string: " + input);
            }

            // Extract the colored substring
            String coloredSubString = input.substring(startIndex, endIndex);

            // Colorize the substring and add it to the builder
            Spannable spannable = new SpannableStringBuilder(coloredSubString);
            spannable.setSpan(new ForegroundColorSpan(color), 0, coloredSubString.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            builder.append(spannable);

            // Update the start index to after the closing '>'
            startIndex = endIndex + 1;
        }

        // Append the rest of the input string
        builder.append(input.substring(startIndex));

        return builder;
    }

    public static void set(TextView textView, String key) {
        String languageCode;
        LocalizationsFreeRealm result;
        String value = "";
        if (language.contains("en")) languageCode = "";
        else languageCode = "_" + language;
        if (listLocalization != null) {
            result = listLocalization.get(key + languageCode);
            if (result == null) result = listLocalization.get(key);
            if (result != null) value = result.getValue();
            if (!value.isEmpty())
                textView.setText(value);
        }
    }
}