package com.vgfit.shefit.util.designe;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.vgfit.shefit.util.designe.callback.AnimationListener;

public class LineAnimateStroke extends View {
    Paint fgPaintSel;
    int radiusCircle1;
    int radiusCircle2;

    private int xCircle1;
    private int yCircle1;
    private Path linePath; // Adauga asta

    private int finalX = 0;
    private int finalY = 0;
    private int duration = 1000;
    private AnimationListener animationListener;

    public LineAnimateStroke(Context context) {
        super(context);
    }

    public LineAnimateStroke(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public LineAnimateStroke(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public void setFinishedListener(AnimationListener animationListener) {
        this.animationListener = animationListener;
    }

    public void init(int x1, int y1, int x2, int y2, int starColor, int lineWidth) {

        this.xCircle1 = x1;
        this.yCircle1 = y1;
        this.radiusCircle1 = 0;
        this.radiusCircle2 = 0;
        float[] dashes = {20, 20};

        fgPaintSel = new Paint();
        fgPaintSel.setARGB(255, 0, 0, 0);
        fgPaintSel.setStyle(Paint.Style.STROKE);
        fgPaintSel.setStrokeWidth(lineWidth);
        fgPaintSel.setPathEffect(new DashPathEffect(dashes, 0));
        fgPaintSel.setColor(starColor);

        linePath = new Path(); // Initializeaza Path aici

        ObjectAnimator animator = ObjectAnimator.ofFloat(LineAnimateStroke.this, "phase", xCircle1, x2);
        animator.setDuration(duration);
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationEnd(@NonNull Animator animation) {
                if (animationListener != null) animationListener.onFinishedAnim();
            }

            @Override
            public void onAnimationCancel(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationRepeat(@NonNull Animator animation) {

            }
        });
        animator.start();

        ObjectAnimator animator2 = ObjectAnimator.ofFloat(LineAnimateStroke.this, "phase2", yCircle1, y2);
        animator2.setDuration(duration);
        animator2.start();
    }

    //is called by animtor object
    @Keep
    private void setPhase(float phase) {
        finalX = (int) phase;
        invalidate();
    }

    @Keep
    private void setPhase2(float phase) {
        finalY = (int) phase;
        invalidate();
    }

    @Override
    public void onDraw(@NonNull Canvas c) {
        super.onDraw(c);
        try {
        linePath.reset(); // Reseteaza Path la fiecare desenare
        linePath.moveTo(xCircle1, yCircle1); // Seteaza punctul de inceput
        linePath.lineTo(finalX, finalY); // Seteaza punctul final


            c.drawPath(linePath, fgPaintSel); // Deseneaza Path cu Paint definit anterior
        } catch (Exception ignored) {
        }
    }
}
