package com.vgfit.shefit.util;

import android.content.Context;
import android.util.Log;

import com.vgfit.shefit.BuildConfig;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;

import java.util.Calendar;

public class LockTimeApp {
    private static final String TEST_LOGIC = "TestLogic";
    private static final String KEY_FIRST_LUNCH = "firstLunchApp";
    private static final String KEY_COUNT_SESSION = "countSessionApp";
    private final Context context;
    private final PrefsUtilsWtContext prefsUtilsWtContext;
    private static final int MILLISECOND = 1000;
    private static final int MINUTES = 60 * MILLISECOND;
    private static final int ONE_HOUR = 60 * MINUTES;

    public LockTimeApp(Context context) {
        this.context = context;
        this.prefsUtilsWtContext = new PrefsUtilsWtContext(context);
    }

    public boolean isValidRunApp() {
        int kMinimumFeaturesAndroid = new RCUtils(context).getKMinimumFeaturesAndroid();

        if (kMinimumFeaturesAndroid == 1)
            return false;

        long currentTime = Calendar.getInstance().getTimeInMillis();
        long timeFirstLunch = prefsUtilsWtContext.getLongPreferenceProfile(KEY_FIRST_LUNCH, 0);
        int countSession = prefsUtilsWtContext.getIntegerPreferenceProfile(KEY_COUNT_SESSION, 0);

        if (timeFirstLunch == 0) {
            prefsUtilsWtContext.setLongPreferenceProfile(KEY_FIRST_LUNCH, currentTime);
            // First Lunch and save time: It's free
            return true;
        }
        long differentTime = currentTime - timeFirstLunch;
        logTest(kMinimumFeaturesAndroid, differentTime, countSession);
        return countSession < 6 && differentTime < ONE_HOUR;
    }

    private void logTest(int kMinimumFeaturesAndroid, long differentTime, int countSession) {
        if (BuildConfig.DEBUG) {
            Log.d(TEST_LOGIC, "kMinimumFeaturesAndroid:  " + kMinimumFeaturesAndroid);
            Log.d(TEST_LOGIC, "differentTime: " + differentTime);
            Log.d(TEST_LOGIC, "ONE_HOUR:      " + ONE_HOUR);
            Log.d(TEST_LOGIC, "countSession:  " + countSession);
            Log.d(TEST_LOGIC, "=============================================");
        }
    }

    public boolean isFirstLunch() {
        return getSessionCount() == 1;
    }

    public static void saveSessionApp(Context context) {
        PrefsUtilsWtContext prefsUtilsWtContext = new PrefsUtilsWtContext(context);
        int countSession = prefsUtilsWtContext.getIntegerPreferenceProfile(KEY_COUNT_SESSION, 0);
        if (countSession == 0)
            prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_COUNT_SESSION, 0);
        countSession++;
        prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_COUNT_SESSION, countSession);
    }

    public int getSessionCount() {
        int countSession = prefsUtilsWtContext.getIntegerPreferenceProfile(KEY_COUNT_SESSION, 0);
        if (countSession == 0)
            prefsUtilsWtContext.setIntegerPreferenceProfile(KEY_COUNT_SESSION, 0);
        return countSession;
    }
}
