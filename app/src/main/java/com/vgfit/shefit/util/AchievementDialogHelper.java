package com.vgfit.shefit.util;

import static com.vgfit.shefit.util.VibrateHepatic.setVibrate;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.util.Log;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;

import com.bumptech.glide.Glide;
import com.vgfit.shefit.R;
import com.vgfit.shefit.realm.Achievement;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

public class AchievementDialogHelper {

    private static final String TAG = "AchievementDialog";

    public interface OnAchievementDialogListener {
        void onAchievementDialogClosed();

        void onShowCelebrationLayout(Achievement achievement);
    }

    /**
     * Check and show achievement dialog if new achievements are unlocked
     */
    public static void checkAndShowAchievementDialog(Context context, Fragment fragment,
                                                     OnAchievementDialogListener listener) {
        try {
            Log.d(TAG, "Checking for new achievements...");
            // Check for newly unlocked achievements
            List<Achievement> newAchievements = AchievementHelper.checkAndUnlockAchievements();
            Log.d(TAG, "Found " + newAchievements.size() + " new achievements from helper");

            // For testing - remove this line in production
/*
            Achievement achievementTest = AchievementHelper.getAchievementById(1);
            if (achievementTest != null) {
                newAchievements.add(achievementTest);
                Log.d(TAG, "Added test achievement, total: " + newAchievements.size());
            }
*/
            if (!newAchievements.isEmpty()) {
                Log.d(TAG, "New achievements unlocked: " + newAchievements.size());

                // Show celebration layout first, then achievement dialog
                Achievement firstAchievement = newAchievements.get(0);
                if (listener != null) {
                    listener.onShowCelebrationLayout(firstAchievement);
                }

                // Log all unlocked achievements
                for (Achievement achievement : newAchievements) {
                    Log.d(TAG, "Achievement unlocked: " + achievement.getBadgeNameKey() +
                            " (Required: " + achievement.getCalories() + " calories)");
                }
            } else {
                Log.d(TAG, "No new achievements unlocked");
                if (listener != null) {
                    listener.onAchievementDialogClosed();
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Error checking achievements: " + e.getMessage());
            if (listener != null) {
                listener.onAchievementDialogClosed();
            }
        }
    }

    /**
     * Show achievement dialog
     */
    public static void showAchievementDialog(Context context, Fragment fragment,
                                             Achievement achievement, OnAchievementDialogListener listener) {
        Log.d(TAG, "Showing achievement dialog for: " + achievement.getBadgeNameKey());

        try {
            // Create and show achievement dialog
            Dialog achievementDialog = new Dialog(context, R.style.ActivityDialog);
            achievementDialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
            achievementDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            achievementDialog.setContentView(R.layout.dialog_achievement_unlocked);
            achievementDialog.setCanceledOnTouchOutside(false);
            achievementDialog.setCancelable(false);

            // Set achievement data
            setupAchievementDialog(achievementDialog, achievement, context, fragment, listener);

            // Show the dialog
            achievementDialog.show();

        } catch (Exception e) {
            Log.e(TAG, "Error showing achievement dialog: " + e.getMessage());
            if (listener != null) {
                listener.onAchievementDialogClosed();
            }
        }
    }

    /**
     * Setup achievement dialog views and listeners
     */
    private static void setupAchievementDialog(Dialog dialog, Achievement achievement,
                                               Context context, Fragment fragment,
                                               OnAchievementDialogListener listener) {
        // Get views from dialog
        TextView titleView = dialog.findViewById(R.id.achievement_title);
        TextView btnShareText = dialog.findViewById(R.id.btn_share_achievement_text);
        ImageView badgeView = dialog.findViewById(R.id.achievement_badge);
        LinearLayout shareButton = dialog.findViewById(R.id.btn_share_achievement);
        TextView closeButton = dialog.findViewById(R.id.btn_close_achievement);
        btnShareText.setText(Translate.getValue("share"));

        // Set achievement data
        String badgeName = Translate.getValue(achievement.getBadgeNameKey());
        titleView.setText(badgeName);

        // Load achievement badge image
        loadAchievementBadge(fragment, badgeView, achievement.getId());

        // Set button listeners
        shareButton.setOnClickListener(v -> {
            setVibrate(v);
            Log.d(TAG, "Share achievement clicked");
            shareAchievement(context, fragment, achievement, badgeView, badgeName);
            dialog.dismiss();
            if (listener != null) {
                listener.onAchievementDialogClosed();
            }
        });

        closeButton.setOnClickListener(v -> {
            setVibrate(v);
            Log.d(TAG, "Close achievement clicked - closing dialog only");
            dialog.dismiss();
            // Just close dialog, celebration layout remains visible
        });

        closeButton.setPaintFlags(closeButton.getPaintFlags() | Paint.UNDERLINE_TEXT_FLAG);
        closeButton.setText(Translate.getValue("close"));
    }

    /**
     * Load achievement badge image using Glide
     */
    private static void loadAchievementBadge(Fragment fragment, ImageView badgeView, int achievementId) {
        // Build the asset path for the badge
        String badgeFileName = "badge_" + achievementId + ".webp";
        String assetPath = "file:///android_asset/achievements/" + badgeFileName;

        // Load image with Glide
        Glide.with(fragment)
                .load(assetPath)
                .placeholder(R.drawable.ic_fire)
                .error(R.drawable.ic_fire)
                .fitCenter()
                .into(badgeView);
    }

    /**
     * Share achievement with image and text
     */
    private static void shareAchievement(Context context, Fragment fragment, Achievement achievement,
                                         ImageView badgeView, String badgeName) {
        try {
            // Get the drawable from ImageView
            Drawable drawable = badgeView.getDrawable();
            if (drawable == null) {
                Log.e(TAG, "No drawable found in ImageView");
                return;
            }

            // Create bitmap from drawable
            Bitmap badgeBitmap = Bitmap.createBitmap(drawable.getIntrinsicWidth(),
                    drawable.getIntrinsicHeight(),
                    Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(badgeBitmap);
            drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
            drawable.draw(canvas);

            // Save badge bitmap directly (no text overlay)
            File shareFile = saveBitmapToTempFile(context, badgeBitmap);
            if (shareFile != null) {
                shareImageWithText(context, fragment, shareFile, achievement, badgeName);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error sharing achievement: " + e.getMessage());
            // Fallback to text-only share
            shareTextOnly(fragment, achievement, badgeName);
        }
    }

    /**
     * Save bitmap to temporary file
     */
    private static File saveBitmapToTempFile(Context context, Bitmap bitmap) {
        try {
            File cacheDir = new File(context.getCacheDir(), "achievements");
            if (!cacheDir.exists()) {
                cacheDir.mkdirs();
            }

            File tempFile = new File(cacheDir, "achievement_" + System.currentTimeMillis() + ".png");
            FileOutputStream fos = new FileOutputStream(tempFile);
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos);
            fos.close();

            return tempFile;
        } catch (IOException e) {
            Log.e(TAG, "Error saving bitmap: " + e.getMessage());
            return null;
        }
    }

    /**
     * Share image with text
     */
    private static void shareImageWithText(Context context, Fragment fragment, File imageFile,
                                           Achievement achievement, String badgeName) {
        try {
            Uri imageUri = FileProvider.getUriForFile(context,
                    context.getPackageName() + ".provider", imageFile);

            String shareText = "🏆 " + badgeName + "\n" +
                    Translate.getValue(achievement.getDescriptionKey()) + "\n" +
                    achievement.getCalories() + " kcal burned! 🔥\n\n" +
                    "Join me in my fitness journey!\n" +
                    "Download SheFit: https://play.google.com/store/apps/details?id=com.vgfit.shefit";

            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("image/png");
            shareIntent.putExtra(Intent.EXTRA_STREAM, imageUri);
            shareIntent.putExtra(Intent.EXTRA_TEXT, shareText);
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

            fragment.startActivity(Intent.createChooser(shareIntent, "Share Achievement"));

        } catch (Exception e) {
            Log.e(TAG, "Error sharing image: " + e.getMessage());
            shareTextOnly(fragment, achievement, badgeName);
        }
    }

    /**
     * Share text only (fallback)
     */
    private static void shareTextOnly(Fragment fragment, Achievement achievement, String badgeName) {
        String shareText = "🏆 " + badgeName + "\n" +
                Translate.getValue(achievement.getDescriptionKey()) + "\n" +
                achievement.getCalories() + " kcal burned! 🔥\n\n" +
                "Join me in my fitness journey!\n" +
                "Download SheFit: https://play.google.com/store/apps/details?id=com.vgfit.shefit";

        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, shareText);

        fragment.startActivity(Intent.createChooser(shareIntent, "Share Achievement"));
    }
}
