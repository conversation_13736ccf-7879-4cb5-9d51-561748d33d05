package com.vgfit.shefit.util.eventCollect.model;

public class GoalPurchaseUser {
    private int countStartWorkout;
    private int countEndWorkout;
    private int countSessionApp;
    private boolean isFirstTime;
    private int workoutLevel;
    private String idPurchase;

    public int getCountStartWorkout() {
        return countStartWorkout;
    }

    public void setCountStartWorkout(int countStartWorkout) {
        this.countStartWorkout = countStartWorkout;
    }

    public int getCountEndWorkout() {
        return countEndWorkout;
    }

    public void setCountEndWorkout(int countEndWorkout) {
        this.countEndWorkout = countEndWorkout;
    }

    public int getCountSessionApp() {
        return countSessionApp;
    }

    public void setCountSessionApp(int countSessionApp) {
        this.countSessionApp = countSessionApp;
    }

    public boolean isFirstTime() {
        return isFirstTime;
    }

    public void setFirstTime(boolean firstTime) {
        isFirstTime = firstTime;
    }

    public int getWorkoutLevel() {
        return workoutLevel;
    }

    public void setWorkoutLevel(int workoutLevel) {
        this.workoutLevel = workoutLevel;
    }

    public String getIdPurchase() {
        return idPurchase;
    }

    public void setIdPurchase(String idPurchase) {
        this.idPurchase = idPurchase;
    }
}
