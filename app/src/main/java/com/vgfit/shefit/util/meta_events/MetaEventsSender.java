package com.vgfit.shefit.util.meta_events;


import static com.vgfit.shefit.authorization.SecDevice.getUUID;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.vgfit.shefit.BaseApplication;
import com.vgfit.shefit.BuildConfig;
import com.vgfit.shefit.api.model.FacebookUtils;
import com.vgfit.shefit.api.model.MetaAppActivateInfo;
import com.vgfit.shefit.api.model.MetaAppInstallInfo;
import com.vgfit.shefit.api.model.MetaEventModel;
import com.vgfit.shefit.api.model.MetaInitiatedCheckoutInfo;
import com.vgfit.shefit.api.model.MetaTrialInfo;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.billing.BillingProcessor;
import com.vgfit.shefit.billing.model.SkuDetail;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.InfoDevice;

import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class MetaEventsSender {
    private static final String KEY_SEND_META_EVENT_INSTALL = "KEY_SEND_META_EVENT_INSTALL";
    private static final String KEY_IS_LIMITED_AD_TRACKING_ENABLED = "KEY_IS_LIMITED_AD_TRACKING_ENABLED";
    private static final String KEY_ADVERTISER_ID = "KEY_ADVERTISER_ID";
    private static final String DEFAULT_ADVERTISER_ID = "00000000-0000-0000-0000-000000000000";
    private static final String TAG_APP_USER_ID = "AppUserID-----------------------> ";
    private static final String TAG_ANON_ID = "AnonId -------------------------> ";
    private static final String TAG_ADVERTISER_ID = "AdvertiserId--------------------> ";
    private static final String TAG_APPLICATION_TRACKING_ENABLED = "ApplicationTrackingEnabled------> ";
    private static final String TAG_TRACKING_TRACKING_ENABLED = "AdvertiserTrackingEnabled-------> ";
    private static final String TAG_ENVIRONMENT = "Environment---------------------> ";
    private static final String TAG_COUNTRY_CODE = "CountryCode---------------------> ";
    private static final String TAG_EXIT_INFO = "ExtInfo-------------------------> ";
    private static final String TAG_PRODUCT_ID = "ProductID-----------------------> ";
    private static final String TAG_PRICE = "Price---------------------------> ";
    private static final String TAG_OPERATION_OS = "Operation OS--------------------> ";
    private static final String TAG_CURRENCY = "Currency------------------------> ";
    private static final String TAG_LOGGER_META = "TAG_LOGGER_META";
    private final Context context;
    private final PrefsUtilsWtContext prefsUtilsWtContext;
    private static final String SANDBOX = "SANDBOX";
    private static final String PRODUCTION = "PRODUCTION";

    public MetaEventsSender(Context context) {
        this.context = context;
        prefsUtilsWtContext = new PrefsUtilsWtContext(context);
    }

    public void sendAppInstall() {
        boolean isSentAppInstall = prefsUtilsWtContext.getBooleanPreference(KEY_SEND_META_EVENT_INSTALL, false);
        if (isSentAppInstall || Constant.isRequestActiveAppInstall) return;

        String appUserId = getUUID(prefsUtilsWtContext);
        MetaEventModel metaEventModel = getInfoDevice(context, appUserId);
        MetaAppInstallInfo metaAppInstallInfo = getInfoAppInstall(context, metaEventModel, appUserId);
        if (metaEventModel != null && metaAppInstallInfo != null) {
            sendAppInstallToServer(metaAppInstallInfo);
        }

    }

    public void sendTrialInfo(BillingProcessor bp, String productId, String transactionId) {
        String appUserId = getUUID(prefsUtilsWtContext);
        if (bp != null)
            bp.getSubscriptionListingDetails(productId, skuDetail -> {
                try {
                    MetaTrialInfo metaTrialInfo = getTrialInfo(skuDetail, appUserId, transactionId);
                    boolean isTrial = false;
                    if (skuDetail.subscriptionFreeTrialPeriod != null) {
                        isTrial = !skuDetail.subscriptionFreeTrialPeriod.isEmpty();
                    }
                    if (metaTrialInfo != null && isTrial)
                        sendTrialInfoToServer(metaTrialInfo);
                } catch (Exception ignored) {
                    //ignored exception
                }
            });
    }

    public void sendInitiatedCheckoutInfo(BillingProcessor bp, String productId) {
        String appUserId = getUUID(prefsUtilsWtContext);
        if (bp != null)
            bp.getSubscriptionListingDetails(productId, skuDetail -> {
                try {
                    boolean isTrial = false;
                    if (skuDetail.subscriptionFreeTrialPeriod != null) {
                        isTrial = !skuDetail.subscriptionFreeTrialPeriod.isEmpty();
                    }
                    MetaInitiatedCheckoutInfo metaInitiatedCheckoutInfo = getInitiatedCheckoutInfo(skuDetail, appUserId, isTrial);
                    if (metaInitiatedCheckoutInfo != null)
                        sendInitCheckoutInfoToServer(metaInitiatedCheckoutInfo);

                } catch (Exception ignored) {
                    //ignored exception
                }
            });
    }

    public void sendAppActivateInfo() {
        String appUserId = getUUID(prefsUtilsWtContext);
        MetaEventModel metaEventModel = getInfoDevice(context, appUserId);
        MetaAppActivateInfo metaAppActivateInfo = getAppActivateInfo(metaEventModel, appUserId);
        if (metaEventModel != null && metaAppActivateInfo != null) {
            sendAppActivateInfoToServer(metaAppActivateInfo);
        }
    }

    private void sendAppInstallToServer(MetaAppInstallInfo metaAppInstallInfo) {
        String logInfo = "TestMetaInfo";
        Constant.setRequestActiveAppInstall(true);
        BaseApplication.getApiMetaEvents().sendAppInstall(metaAppInstallInfo).enqueue(new Callback<Void>() {
            @Override
            public void onResponse(@NonNull Call<Void> call, @NonNull Response<Void> response) {
                if (response.isSuccessful()) {
                    try {
                        prefsUtilsWtContext.setBooleanPreference(KEY_SEND_META_EVENT_INSTALL, true);
                    } catch (Exception ignored) {
                        Log.d(logInfo, "Error save data send");
                    }
                    if (BuildConfig.DEBUG)
                        Log.d(logInfo, "SUCCESS send DATA TO SERVER <<APP INSTALL>>");
                } else {
                    if (BuildConfig.DEBUG)
                        Log.d(logInfo, "FAILURE send DATA TO SERVER <<APP INSTALL>> code-->" + response.code());
                }
                Constant.setRequestActiveAppInstall(false);
            }

            @Override
            public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                Constant.setRequestActiveAppInstall(false);
                if (BuildConfig.DEBUG)
                    Log.d(logInfo, "onFAILURE send DATA TO SERVER <<APP INSTALL>>-->" + call.request());
            }
        });
    }

    private void sendTrialInfoToServer(MetaTrialInfo metaTrialInfo) {
        String logInfo = "TestMetaTrial";
        BaseApplication.getApiMetaEvents().sendTrial(metaTrialInfo).enqueue(new Callback<Void>() {
            @Override
            public void onResponse(@NonNull Call<Void> call, @NonNull Response<Void> response) {
                if (response.isSuccessful() && BuildConfig.DEBUG) {
                    Log.d(logInfo, "SUCCESS send DATA TO SERVER <<TRIAL INFO>>");
                } else {
                    Log.e(logInfo, "FAILURE send DATA TO SERVER <<TRIAL INFO>>");
                }
            }

            @Override
            public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                Log.e(logInfo, "onFAILURE send DATA TO SERVER <<TRIAL INFO>>");
            }
        });
    }

    private void sendInitCheckoutInfoToServer(MetaInitiatedCheckoutInfo metaInitiatedCheckoutInfo) {
        String logInfo = "TestInitCheckout";
        BaseApplication.getApiMetaEvents().sendInitiatedCheckout(metaInitiatedCheckoutInfo).enqueue(new Callback<Void>() {
            @Override
            public void onResponse(@NonNull Call<Void> call, @NonNull Response<Void> response) {
                if (response.isSuccessful() && BuildConfig.DEBUG) {
                    Log.d(logInfo, "SUCCESS send DATA TO SERVER <<INIT CHECKOUT>>");
                } else {
                    Log.e(logInfo, "FAILURE send DATA TO SERVER <<INIT CHECKOUT>>");
                }
            }

            @Override
            public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                Log.e(logInfo, "onFAILURE send DATA TO SERVER <<INIT CHECKOUT>>");
            }
        });
    }

    private void sendAppActivateInfoToServer(MetaAppActivateInfo metaAppActivateInfo) {
        String logInfo = "TestAppActivate";
        BaseApplication.getApiMetaEvents().sendAppActivate(metaAppActivateInfo).enqueue(new Callback<Void>() {
            @Override
            public void onResponse(@NonNull Call<Void> call, @NonNull Response<Void> response) {
                if (response.isSuccessful() && BuildConfig.DEBUG) {
                    Log.d(logInfo, "SUCCESS send DATA TO SERVER <<APP ACTIVATE>>");
                } else {
                    Log.e(logInfo, "FAILURE send DATA TO SERVER <<APP ACTIVATE>>");
                }
            }

            @Override
            public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                Log.e(logInfo, "onFAILURE send DATA TO SERVER <<APP ACTIVATE>>");
            }
        });
    }

    private MetaEventModel getInfoDevice(Context context, String appUserId) {
        try {
            InfoDevice infoDevice = new InfoDevice(context);
            return infoDevice.getMetaInfo(appUserId);
        } catch (Exception e) {
            return null;
        }

    }

    private MetaAppInstallInfo getInfoAppInstall(Context context, MetaEventModel extInfo, String appUserId) {
        try {
            String advertiserID = prefsUtilsWtContext.getStringPreference(KEY_ADVERTISER_ID, DEFAULT_ADVERTISER_ID);
            boolean advertiserTrackingEnabled = !prefsUtilsWtContext.getBooleanPreference(KEY_IS_LIMITED_AD_TRACKING_ENABLED, true);
            MetaAppInstallInfo metaAppInstallInfo = new MetaAppInstallInfo();
            metaAppInstallInfo.setAppUserId(appUserId);
            metaAppInstallInfo.setAnonId(FacebookUtils.getAnonID(context));
            metaAppInstallInfo.setAccessToken(FacebookUtils.getAccessToken());
            metaAppInstallInfo.setAdvertiserId(advertiserID);
            metaAppInstallInfo.setAdvertiserIdCollectionEnabled(FacebookUtils.isAdvertiserIDCollectionEnabled() ? 1 : 0);
            metaAppInstallInfo.setApplicationTrackingEnabled(FacebookUtils.isApplicationTrackingEnabled(context) ? 1 : 0);
            metaAppInstallInfo.setAdvertiserTrackingEnabled(advertiserTrackingEnabled ? 1 : 0);
            metaAppInstallInfo.setExtInfo(extInfo);
            metaAppInstallInfo.setEnvironment(BuildConfig.DEBUG ? SANDBOX : PRODUCTION);
            metaAppInstallInfo.setCountryCode(Locale.getDefault().getCountry().toLowerCase());
            if (BuildConfig.DEBUG)
                loggerInfoInstall(metaAppInstallInfo);
            return metaAppInstallInfo;
        } catch (Exception e) {
            return null;
        }

    }

    private MetaTrialInfo getTrialInfo(SkuDetail skuDetail, String appUserId, String transactionId) {
        try {
            MetaTrialInfo metaTrialInfo = new MetaTrialInfo();
            metaTrialInfo.setTransactionId(transactionId);
            metaTrialInfo.setProductId(skuDetail.productId);
            metaTrialInfo.setAppUserId(appUserId);
            metaTrialInfo.setPrice(skuDetail.priceValue);
            metaTrialInfo.setCurrency(skuDetail.currency);
            metaTrialInfo.setEnvironment(BuildConfig.DEBUG ? SANDBOX : PRODUCTION);
            metaTrialInfo.setOperatingSystem("android");
            metaTrialInfo.setCountryCode(Locale.getDefault().getCountry().toLowerCase());
            if (BuildConfig.DEBUG)
                loggerInfoTrial(metaTrialInfo);
            return metaTrialInfo;
        } catch (Exception e) {
            return null;
        }
    }

    private MetaInitiatedCheckoutInfo getInitiatedCheckoutInfo(SkuDetail skuDetail, String appUserId, boolean isTrial) {
        try {
            String advertiserID = prefsUtilsWtContext.getStringPreference(KEY_ADVERTISER_ID, DEFAULT_ADVERTISER_ID);
            boolean advertiserTrackingEnabled = !prefsUtilsWtContext.getBooleanPreference(KEY_IS_LIMITED_AD_TRACKING_ENABLED, true);
            MetaInitiatedCheckoutInfo metaInitiatedCheckoutInfo = new MetaInitiatedCheckoutInfo();
            metaInitiatedCheckoutInfo.setProductId(skuDetail.productId);
            metaInitiatedCheckoutInfo.setAppUserId(appUserId);
            metaInitiatedCheckoutInfo.setPrice(skuDetail.priceValue);
            metaInitiatedCheckoutInfo.setCurrency(skuDetail.currency);
            metaInitiatedCheckoutInfo.setTrial(isTrial);
            metaInitiatedCheckoutInfo.setAnonId(FacebookUtils.getAnonID(context));
            metaInitiatedCheckoutInfo.setAdvertiserId(advertiserID);
            metaInitiatedCheckoutInfo.setAdvertiserTrackingEnabled(advertiserTrackingEnabled ? 1 : 0);
            metaInitiatedCheckoutInfo.setApplicationTrackingEnabled(FacebookUtils.isApplicationTrackingEnabled(context) ? 1 : 0);
            metaInitiatedCheckoutInfo.setEnvironment(BuildConfig.DEBUG ? SANDBOX : PRODUCTION);
            metaInitiatedCheckoutInfo.setOperatingSystem("android");
            metaInitiatedCheckoutInfo.setCountryCode(Locale.getDefault().getCountry().toLowerCase());
            if (BuildConfig.DEBUG)
                loggerInitiatedCheckoutInfo(metaInitiatedCheckoutInfo);
            return metaInitiatedCheckoutInfo;
        } catch (Exception e) {
            return null;
        }
    }

    private MetaAppActivateInfo getAppActivateInfo(MetaEventModel extInfo, String appUserId) {
        try {
            String advertiserID = prefsUtilsWtContext.getStringPreference(KEY_ADVERTISER_ID, DEFAULT_ADVERTISER_ID);
            boolean advertiserTrackingEnabled = !prefsUtilsWtContext.getBooleanPreference(KEY_IS_LIMITED_AD_TRACKING_ENABLED, true);
            MetaAppActivateInfo metaAppActivateInfo = new MetaAppActivateInfo();
            metaAppActivateInfo.setAppUserId(appUserId);
            metaAppActivateInfo.setAnonId(FacebookUtils.getAnonID(context));
            metaAppActivateInfo.setAdvertiserId(advertiserID);
            metaAppActivateInfo.setApplicationTrackingEnabled(FacebookUtils.isApplicationTrackingEnabled(context) ? 1 : 0);
            metaAppActivateInfo.setAdvertiserTrackingEnabled(advertiserTrackingEnabled ? 1 : 0);
            metaAppActivateInfo.setExtInfo(extInfo);
            metaAppActivateInfo.setEnvironment(BuildConfig.DEBUG ? SANDBOX : PRODUCTION);
            metaAppActivateInfo.setCountryCode(Locale.getDefault().getCountry().toLowerCase());
            if (BuildConfig.DEBUG)
                loggerAppActivateInfo(metaAppActivateInfo);
            return metaAppActivateInfo;
        } catch (Exception e) {
            return null;
        }
    }

    private void loggerInfoInstall(MetaAppInstallInfo metaAppInstallInfo) {
        Log.d(TAG_LOGGER_META, "===================MetaAppInstallInfo=======================");
        Log.d(TAG_LOGGER_META, TAG_APP_USER_ID + metaAppInstallInfo.getAppUserId());
        Log.d(TAG_LOGGER_META, TAG_ANON_ID + metaAppInstallInfo.getAnonId());
        Log.d(TAG_LOGGER_META, "AccessToken---------------------> " + metaAppInstallInfo.getAccessToken());
        Log.d(TAG_LOGGER_META, TAG_ADVERTISER_ID + metaAppInstallInfo.getAdvertiserId());
        Log.d(TAG_LOGGER_META, "AdvertiserIdCollectionEnabled---> " + metaAppInstallInfo.getAdvertiserIdCollectionEnabled());
        Log.d(TAG_LOGGER_META, TAG_APPLICATION_TRACKING_ENABLED + metaAppInstallInfo.getApplicationTrackingEnabled());
        Log.d(TAG_LOGGER_META, TAG_TRACKING_TRACKING_ENABLED + metaAppInstallInfo.getAdvertiserTrackingEnabled());
        Log.d(TAG_LOGGER_META, TAG_EXIT_INFO + metaAppInstallInfo.getExtInfo());
        Log.d(TAG_LOGGER_META, TAG_ENVIRONMENT + metaAppInstallInfo.getEnvironment());
        Log.d(TAG_LOGGER_META, TAG_COUNTRY_CODE + metaAppInstallInfo.getCountryCode());
    }

    private void loggerInfoTrial(MetaTrialInfo metaTrialInfo) {
        Log.d(TAG_LOGGER_META, "=====================MetaTrialInfo=====================");
        Log.d(TAG_LOGGER_META, "TransactionID-------------------> " + metaTrialInfo.getTransactionId());
        Log.d(TAG_LOGGER_META, TAG_PRODUCT_ID + metaTrialInfo.getProductId());
        Log.d(TAG_LOGGER_META, TAG_APP_USER_ID + metaTrialInfo.getAppUserId());
        Log.d(TAG_LOGGER_META, TAG_PRICE + metaTrialInfo.getPrice());
        Log.d(TAG_LOGGER_META, TAG_CURRENCY + metaTrialInfo.getCurrency());
        Log.d(TAG_LOGGER_META, TAG_ENVIRONMENT + metaTrialInfo.getEnvironment());
        Log.d(TAG_LOGGER_META, TAG_OPERATION_OS + metaTrialInfo.getOperatingSystem());
        Log.d(TAG_LOGGER_META, TAG_COUNTRY_CODE + metaTrialInfo.getCountryCode());
    }

    private void loggerInitiatedCheckoutInfo(MetaInitiatedCheckoutInfo metaInitiatedCheckoutInfo) {
        Log.d(TAG_LOGGER_META, "===================MetaInitiatedCheckoutInfo=======================");
        Log.d(TAG_LOGGER_META, TAG_PRODUCT_ID + metaInitiatedCheckoutInfo.getProductId());
        Log.d(TAG_LOGGER_META, TAG_APP_USER_ID + metaInitiatedCheckoutInfo.getAppUserId());
        Log.d(TAG_LOGGER_META, TAG_PRICE + metaInitiatedCheckoutInfo.getPrice());
        Log.d(TAG_LOGGER_META, TAG_CURRENCY + metaInitiatedCheckoutInfo.getCurrency());
        Log.d(TAG_LOGGER_META, "isTrial-------------------------> " + metaInitiatedCheckoutInfo.getTrial());
        Log.d(TAG_LOGGER_META, TAG_ANON_ID + metaInitiatedCheckoutInfo.getAnonId());
        Log.d(TAG_LOGGER_META, TAG_ADVERTISER_ID + metaInitiatedCheckoutInfo.getAdvertiserId());
        Log.d(TAG_LOGGER_META, TAG_APPLICATION_TRACKING_ENABLED + metaInitiatedCheckoutInfo.getApplicationTrackingEnabled());
        Log.d(TAG_LOGGER_META, TAG_TRACKING_TRACKING_ENABLED + metaInitiatedCheckoutInfo.getAdvertiserTrackingEnabled());
        Log.d(TAG_LOGGER_META, TAG_ENVIRONMENT + metaInitiatedCheckoutInfo.getEnvironment());
        Log.d(TAG_LOGGER_META, TAG_OPERATION_OS + metaInitiatedCheckoutInfo.getOperatingSystem());
        Log.d(TAG_LOGGER_META, TAG_COUNTRY_CODE + metaInitiatedCheckoutInfo.getCountryCode());
    }

    private void loggerAppActivateInfo(MetaAppActivateInfo metaAppActivateInfo) {
        Log.d(TAG_LOGGER_META, "===================MetaAppActivateInfo=======================");
        Log.d(TAG_LOGGER_META, TAG_APP_USER_ID + metaAppActivateInfo.getAppUserId());
        Log.d(TAG_LOGGER_META, TAG_ANON_ID + metaAppActivateInfo.getAnonId());
        Log.d(TAG_LOGGER_META, TAG_ADVERTISER_ID + metaAppActivateInfo.getAdvertiserId());
        Log.d(TAG_LOGGER_META, TAG_APPLICATION_TRACKING_ENABLED + metaAppActivateInfo.getApplicationTrackingEnabled());
        Log.d(TAG_LOGGER_META, TAG_TRACKING_TRACKING_ENABLED + metaAppActivateInfo.getAdvertiserTrackingEnabled());
        Log.d(TAG_LOGGER_META, TAG_EXIT_INFO + metaAppActivateInfo.getExtInfo());
        Log.d(TAG_LOGGER_META, TAG_ENVIRONMENT + metaAppActivateInfo.getEnvironment());
        Log.d(TAG_LOGGER_META, TAG_COUNTRY_CODE + metaAppActivateInfo.getCountryCode());
    }
}
