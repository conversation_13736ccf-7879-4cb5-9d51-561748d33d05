package com.vgfit.shefit.util;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;

import com.vgfit.shefit.BuildConfig;
import com.vgfit.shefit.R;
import com.vgfit.shefit.api.model.MetaEventModel;

import java.util.Locale;
import java.util.TimeZone;


/**
 * Created by Nemo on 7/14/17.
 */

public class InfoDevice {
    Context context;
    private static final String TAG_LOG = "TestMetaInfo";

    public InfoDevice(Context context) {
        this.context = context;
    }

    public String getFullInfoDevice() {
        return "\n\n\n" +
                "***********************************\n" +
                "Device model: " + getDeviceName() + "\n" +
                "System Version Android: " + Build.VERSION.RELEASE + "\n" +
                "App Version: " + BuildConfig.VERSION_NAME + "\n" +
                context.getResources().getString(R.string.app_name) + "\n" +
                "***********************************";
    }

    public String getFullInfoDeviceSlack(String appUserId) {
        return "\n" +
                "***********************************\n" +
                "Device model: " + getDeviceName() + "\n" +
                "System Version Android: " + Build.VERSION.RELEASE + "\n" +
                "App Version: " + BuildConfig.VERSION_NAME + "\n" +
                "Device Language: " + getLanguageName() + "\n" +
                "Device Time Zone: " + getCurrentTimeZone() + "\n" +
                "Connection Type: " + getConnectionType(context) + "\n" +
                "App USER ID: " + appUserId + "\n" +
                context.getResources().getString(R.string.app_name) + "\n" +
                "***********************************\n\n";


    }

    public MetaEventModel getMetaInfo(String appUserId) {
        DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();
        MetaEventModel metaEventModel = new MetaEventModel();
        metaEventModel.setAppUserId(appUserId);
        metaEventModel.setPackageApp(context.getPackageName());
        metaEventModel.setAppVersion(BuildConfig.VERSION_NAME);
        metaEventModel.setOsDevice("android");
        metaEventModel.setOsVersion(Build.VERSION.RELEASE);
        metaEventModel.setDeviceModel(getDeviceName());
        metaEventModel.setLocale(getCustomLocale());
        metaEventModel.setScreenWidth((int) (displayMetrics.widthPixels / displayMetrics.density));
        metaEventModel.setScreenHeight((int) (displayMetrics.heightPixels / displayMetrics.density));
        metaEventModel.setScreenDensity(displayMetrics.density);
        metaEventModel.setDeviceTimeZone(getCurrentTimeZone());
        metaEventModel.setTimezoneAbbreviation(getAbbreviationTimeZone());
        if (BuildConfig.DEBUG) {
            testInfo(metaEventModel);
        }
        return metaEventModel;
    }

    private void testInfo(MetaEventModel metaEventModel) {
        Log.d(TAG_LOG, "app_user_id --> " + metaEventModel.getAppUserId());
        Log.d(TAG_LOG, "package     --> " + metaEventModel.getPackageApp());
        Log.d(TAG_LOG, "app_version --> " + metaEventModel.getAppVersion());
        Log.d(TAG_LOG, "os          --> " + metaEventModel.getOsDevice());
        Log.d(TAG_LOG, "os_version  --> " + metaEventModel.getOsVersion());
        Log.d(TAG_LOG, "device_model--> " + metaEventModel.getDeviceModel());
        Log.d(TAG_LOG, "locale      --> " + metaEventModel.getLocale());
        Log.d(TAG_LOG, "carrier     --> " + metaEventModel.getCarrier());
        Log.d(TAG_LOG, "screen_width--> " + metaEventModel.getScreenWidth());
        Log.d(TAG_LOG, "screen_height--> " + metaEventModel.getScreenHeight());
        Log.d(TAG_LOG, "screen_density--> " + metaEventModel.getScreenDensity());
        Log.d(TAG_LOG, "device_timezone--> " + metaEventModel.getDeviceTimeZone());
        Log.d(TAG_LOG, "timezone_abbreviation--> " + metaEventModel.getTimezoneAbbreviation());
    }

    private String getAbbreviationTimeZone() {
        TimeZone tz = TimeZone.getDefault();
        return tz.getDisplayName(false, TimeZone.SHORT);
    }

    public String getConnectionType(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

        if (connectivityManager != null) {
            NetworkInfo activeNetwork = connectivityManager.getActiveNetworkInfo();

            if (activeNetwork != null && activeNetwork.isConnected()) {
                if (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI) {
                    return "Wi-Fi";
                } else if (activeNetwork.getType() == ConnectivityManager.TYPE_MOBILE) {
                    return "Mobile";
                }
            }
        }
        return "None";
    }

    private String getCustomLocale() {
        Locale locale = Locale.getDefault();
        String language = locale.getLanguage(); // ex. "en"
        String country = locale.getCountry(); // ex. "RO"
        return language + "_" + country;
    }

    public String getLanguageName() {
        Locale currentLocale = Locale.getDefault();
        return currentLocale.getDisplayLanguage();
    }

    public String getCurrentTimeZone() {
        TimeZone timeZone = TimeZone.getDefault();
        return timeZone.getID();
    }

    private String getDeviceName() {
        String manufacturer = Build.MANUFACTURER;
        String model = Build.MODEL;
        if (model.startsWith(manufacturer)) {
            return capitalize(model);
        }
        return capitalize(manufacturer) + " " + model;
    }

    private String capitalize(String str) {
        if (TextUtils.isEmpty(str)) {
            return str;
        }
        char[] arr = str.toCharArray();
        boolean capitalizeNext = true;

        StringBuilder phrase = new StringBuilder();
        for (char c : arr) {
            if (capitalizeNext && Character.isLetter(c)) {
                phrase.append(Character.toUpperCase(c));
                capitalizeNext = false;
                continue;
            } else if (Character.isWhitespace(c)) {
                capitalizeNext = true;
            }
            phrase.append(c);
        }

        return phrase.toString();
    }
}
