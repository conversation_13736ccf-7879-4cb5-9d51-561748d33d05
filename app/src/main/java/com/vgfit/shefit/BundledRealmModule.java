package com.vgfit.shefit;

import com.vgfit.shefit.realm.Achievement;
import com.vgfit.shefit.realm.Category;
import com.vgfit.shefit.realm.CoverDayPlan;
import com.vgfit.shefit.realm.CoverVideoPlan;
import com.vgfit.shefit.realm.DailyExercise;
import com.vgfit.shefit.realm.DailyWorkouts;
import com.vgfit.shefit.realm.DayPlan;
import com.vgfit.shefit.realm.Exercise;
import com.vgfit.shefit.realm.ExercisePlan;
import com.vgfit.shefit.realm.Languages;
import com.vgfit.shefit.realm.Localizations;
import com.vgfit.shefit.realm.Meal;
import com.vgfit.shefit.realm.MealByDay;
import com.vgfit.shefit.realm.NutritionDay;
import com.vgfit.shefit.realm.NutritionPlan;
import com.vgfit.shefit.realm.PhotoMeal;
import com.vgfit.shefit.realm.Superset;
import com.vgfit.shefit.realm.SupersetPlan;
import com.vgfit.shefit.realm.Workout;
import com.vgfit.shefit.realm.WorkoutExercise;
import com.vgfit.shefit.realm.WorkoutHistory;
import com.vgfit.shefit.realm.WorkoutLevel;

import io.realm.annotations.RealmModule;

@RealmModule(classes = {Achievement.class, Superset.class, Exercise.class, Localizations.class, Workout.class,
        Category.class, WorkoutLevel.class, Meal.class, Languages.class, MealByDay.class,
        NutritionDay.class, NutritionPlan.class, WorkoutExercise.class, DailyWorkouts.class,
        DailyExercise.class, DayPlan.class, ExercisePlan.class, SupersetPlan.class, PhotoMeal.class,
        CoverVideoPlan.class, CoverDayPlan.class, WorkoutHistory.class})
class BundledRealmModule {
}