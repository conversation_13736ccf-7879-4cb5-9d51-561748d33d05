package com.vgfit.shefit.api.loginweb;

import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getCurrentMetricHeight;
import static com.vgfit.shefit.fragment.userprofile.settings.ConverterMeasure.getCurrentMetricWeight;
import static com.vgfit.shefit.util.TextUtil.toSnakeCaseTranslate;

import androidx.annotation.NonNull;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.vgfit.shefit.BaseApplication;
import com.vgfit.shefit.api.loginweb.callback.DeleteUserResponse;
import com.vgfit.shefit.api.loginweb.callback.ForgotPasswordResponse;
import com.vgfit.shefit.api.loginweb.callback.LoginStatusResponse;
import com.vgfit.shefit.api.loginweb.callback.LoginUserInfoResponse;
import com.vgfit.shefit.api.loginweb.callback.LoginWebResponse;
import com.vgfit.shefit.api.loginweb.callback.UserExistResponse;
import com.vgfit.shefit.api.loginweb.model.SubscribeInfoWeb;
import com.vgfit.shefit.api.loginweb.model.UserExists;
import com.vgfit.shefit.api.loginweb.model.UserInfoWeb;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.authorization.Token;
import com.vgfit.shefit.util.Constant;

import java.io.IOException;
import java.util.Date;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class LoginWebService {
    private static final String DETAIL_FIELD = "detail";

    private LoginWebService() {
        //empty constructor
    }

    public static void getLoginWeb(PrefsUtilsWtContext prefsUtilsWtContext,
                                   String email, String password,
                                   LoginWebResponse loginWebResponse) {
        loginWebResponse.serverEndResponse(false);
        BaseApplication.getApiFemale().getUser(email, password, true).enqueue(new Callback<Token>() {
            @Override
            public void onResponse(@NonNull Call<Token> call, @NonNull Response<Token> response) {
                loginWebResponse.serverEndResponse(true);
                if (response.isSuccessful() && response.body() != null) {
                    saveUserToken(prefsUtilsWtContext, response.body(), email, password, loginWebResponse);
                } else {
                    loginWebResponse.responseLoginError(getErrorMessage(response));
                }
            }

            @Override
            public void onFailure(@NonNull Call<Token> call, @NonNull Throwable t) {
                loginWebResponse.responseLoginFailed();
                loginWebResponse.serverEndResponse(true);
            }
        });
    }

    public static void saveUserToken(PrefsUtilsWtContext prefsUtilsWtContext, Token token, String email, String password, LoginWebResponse loginWebResponse) {
        try {
            String tokenValid = "Bearer " + token.getAccess();
            prefsUtilsWtContext.setStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB, email);
            prefsUtilsWtContext.setStringPreferenceProfile(Constant.PASSWORD_LOGIN_WEB, password);
            prefsUtilsWtContext.setStringPreference(Constant.ACCESS_TOKEN, tokenValid);
            prefsUtilsWtContext.setStringPreference(Constant.REFRESH_TOKEN, token.getRefresh());
            loginWebResponse.responseLoginSuccess();
        } catch (Exception ignored) {
            //ignored
        }
    }

    public static void getStatusUser(PrefsUtilsWtContext prefsUtilsWtContext,
                                     LoginStatusResponse loginStatusResponse) {
        String token = prefsUtilsWtContext.getStringPreference(Constant.ACCESS_TOKEN);
        if (token != null && !token.isEmpty())
            BaseApplication.getApiFemale().getSubscribeInfo(token).enqueue(new Callback<SubscribeInfoWeb>() {
                @Override
                public void onResponse(@NonNull Call<SubscribeInfoWeb> call, @NonNull Response<SubscribeInfoWeb> response) {
                    if (response.isSuccessful()) {
                        loginResponse(response, prefsUtilsWtContext, loginStatusResponse);
                    } else if (response.code() == 401) {
                        saveLogout(prefsUtilsWtContext);
                    } else {
                        loginStatusResponse.responseStatusFailed();
                    }
                }

                @Override
                public void onFailure(@NonNull Call<SubscribeInfoWeb> call, @NonNull Throwable t) {
                    loginStatusResponse.responseStatusFailed();
                }
            });
    }

    private static void loginResponse(Response<SubscribeInfoWeb> response,
                                      PrefsUtilsWtContext prefsUtilsWtContext,
                                      LoginStatusResponse loginStatusResponse) {
        SubscribeInfoWeb subscribeInfoWeb = response.body();
        if (subscribeInfoWeb != null && response.body() != null) {
            boolean isValidSession = subscribeInfoWeb.getSessionValid();
            if (!Constant.premium && isValidSession) {
                Constant.setPremium(subscribeInfoWeb.getHasSubscription());
                long startDate = subscribeInfoWeb.getSubscriptionStartDate() != null ? subscribeInfoWeb.getSubscriptionStartDate() : 0L;
                Date date = startDate != 0L ? new Date(startDate * 1000L) : new Date();
                loginStatusResponse.statusPremium(date);
            }
            if (!isValidSession)
                saveLogout(prefsUtilsWtContext);
            loginStatusResponse.responseStatusSuccess();
        }
    }

    private static void saveLogout(PrefsUtilsWtContext prefsUtilsWtContext) {
        if (prefsUtilsWtContext != null) {
            try {
                prefsUtilsWtContext.setStringPreferenceProfile(Constant.EMAIL_LOGIN_WEB, "");
                prefsUtilsWtContext.setStringPreferenceProfile(Constant.PASSWORD_LOGIN_WEB, "");
            } catch (Exception ignored) {
                // ignore exception
            }
        }
    }

    public static void sendForgotPassword(String email, boolean openDialog, ForgotPasswordResponse forgotPasswordResponse) {
        forgotPasswordResponse.serverEndResponse(false);
        BaseApplication.getApiFemale().sendForgotPassword(email).enqueue(new Callback<Void>() {
            @Override
            public void onResponse(@NonNull Call<Void> call, @NonNull Response<Void> response) {
                if (!response.isSuccessful()) {
                    forgotPasswordResponse.responseForgotPasswordMessage(getErrorMessage(response), response.code() == 429);
                } else {
                    forgotPasswordResponse.successForgotPassword(openDialog);
                }
                forgotPasswordResponse.serverEndResponse(true);
            }

            @Override
            public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                // ignore failure
                forgotPasswordResponse.serverEndResponse(true);
            }
        });
    }

    public static void sendInstructionDelete(PrefsUtilsWtContext prefsUtilsWtContext, DeleteUserResponse deleteUserResponse) {
        deleteUserResponse.serverEndResponse(false);
        String token = prefsUtilsWtContext.getStringPreference(Constant.ACCESS_TOKEN);
        if (token != null && !token.isEmpty())
            BaseApplication.getApiFemale().sendDeleteInstruction(token).enqueue(new Callback<Void>() {
                @Override
                public void onResponse(@NonNull Call<Void> call, @NonNull Response<Void> response) {
                    deleteUserResponse.serverEndResponse(true);
                    if (response.isSuccessful()) {
                        deleteUserResponse.deleteSendSuccess();
                    } else if (response.code() == 401) {
                        saveLogout(prefsUtilsWtContext);
                    } else {
                        deleteUserResponse.responseDeleteUserMessage(getErrorMessage(response), response.code() == 429);
                        deleteUserResponse.deleteSendFailed();
                    }
                }

                @Override
                public void onFailure(@NonNull Call<Void> call, @NonNull Throwable t) {
                    deleteUserResponse.deleteSendFailed();
                    deleteUserResponse.serverEndResponse(true);
                }
            });
    }

    private static String getErrorMessage(Response<?> response) {
        String detailMessage = "Unknown error";
        try {
            if (response.errorBody() != null) {
                String errorResponse = response.errorBody().string();
                JsonObject jsonError = JsonParser.parseString(errorResponse).getAsJsonObject();
                if (jsonError.has(DETAIL_FIELD)) {
                    detailMessage = jsonError.get(DETAIL_FIELD).getAsString();
                    return toSnakeCaseTranslate(detailMessage);
                }
            }
        } catch (IOException e) {
            //ignored exception
        }
        return detailMessage;
    }

    public static void isExistUserId(PrefsUtilsWtContext prefsUtilsWtContext, UserExistResponse userExistResponse) {
        userExistResponse.serverEndResponse(false);
        final String KEY_USER_ID = "KEY_USER_ID";
        String token = prefsUtilsWtContext.getStringPreference(Constant.ACCESS_TOKEN);
        String userId = prefsUtilsWtContext.getStringPreferenceProfile(KEY_USER_ID, "");
        if (!userId.isEmpty() && token != null && !token.isEmpty()) {
            BaseApplication.getApiFemale().isExistUser(token, userId).enqueue(new Callback<UserExists>() {
                @Override
                public void onResponse(@NonNull Call<UserExists> call, @NonNull Response<UserExists> response) {
                    userExistResponse.serverEndResponse(true);
                    if (response.isSuccessful() && response.body() != null) {
                        UserExists userExists = response.body();
                        userExistResponse.userExist(userExists.isExists());
                    }
                }

                @Override
                public void onFailure(@NonNull Call<UserExists> call, @NonNull Throwable t) {
                    userExistResponse.serverEndResponse(true);
                }
            });
        } else {
            userExistResponse.serverEndResponse(true);
        }
    }

    public static void getUserInfo(PrefsUtilsWtContext prefsUtilsWtContext,
                                   LoginUserInfoResponse loginUserInfoResponse) {
        String token = prefsUtilsWtContext.getStringPreference(Constant.ACCESS_TOKEN);
        if (token != null && !token.isEmpty())
            BaseApplication.getApiFemale().getUserInfo(token).enqueue(new Callback<UserInfoWeb>() {
                @Override
                public void onResponse(@NonNull Call<UserInfoWeb> call, @NonNull Response<UserInfoWeb> response) {
                    if (response.isSuccessful()) {
                        saveUserInfo(response, prefsUtilsWtContext, loginUserInfoResponse);
                    } else if (response.code() == 401) {
                        saveLogout(prefsUtilsWtContext);
                    } else {
                        loginUserInfoResponse.responseUserInfoFailed();
                    }
                }

                @Override
                public void onFailure(@NonNull Call<UserInfoWeb> call, @NonNull Throwable t) {
                    loginUserInfoResponse.responseUserInfoFailed();
                }
            });
    }

    private static void saveUserInfo(Response<UserInfoWeb> response,
                                     PrefsUtilsWtContext prefsUtilsWtContext,
                                     LoginUserInfoResponse loginUserInfoResponse) {
        final String KEY_IS_LBS = "KEY_IS_LBS";
        final String KEY_IS_IN = "KEY_IS_IN";
        final String KEY_WORK_DAYS = "workout_days";
        final String KEY_AGE_VALUE = "KEY_AGE_VALUE_";
        final String KEY_EXPERIENCE = "KEY_EXPERIENCE";
        final String KEY_USER_ID = "KEY_USER_ID";
        UserInfoWeb userInfoWeb = response.body();
        if (userInfoWeb != null && response.body() != null) {
            if (!Constant.premium)
                Constant.premium = userInfoWeb.getSubscription().isHasSubscription();
            int experience = userInfoWeb.getExperience();
            int fitnessGoal = userInfoWeb.getFitnessGoal();

            String days = userInfoWeb.getWorkoutDays();
            boolean isChecked = userInfoWeb.getWeightMeasureUnit().contains("lbs");
            boolean isIn = userInfoWeb.getHeightMeasureUnit().contains("in");
            prefsUtilsWtContext.setStringPreferenceProfile(KEY_USER_ID, userInfoWeb.getId());
            prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_IS_LBS, isChecked);
            prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_IS_IN, isIn);
            prefsUtilsWtContext.setIntegerPreference(KEY_AGE_VALUE, userInfoWeb.getAge());
            prefsUtilsWtContext.setIntegerPreference(KEY_EXPERIENCE, experience);
            prefsUtilsWtContext.setStringPreferenceProfile(KEY_WORK_DAYS, days);
            saveHeightAndWeight(prefsUtilsWtContext, userInfoWeb);
            saveDays(prefsUtilsWtContext, days);
            saveFitnessGoal(prefsUtilsWtContext, fitnessGoal);
            saveSportLately(prefsUtilsWtContext, userInfoWeb.getSportLately() + 1);
            loginUserInfoResponse.responseUserInfoSuccess();
        }

    }

    private static void saveSportLately(PrefsUtilsWtContext pf, int value) {
        final String KEY_SPORT_LATELY = "chooseSport";
        for (int i = 1; i < 5; i++) {
            pf.setBooleanPreference(KEY_SPORT_LATELY + i, false);
        }
        pf.setBooleanPreference(KEY_SPORT_LATELY + value, true);
    }

    private static void saveFitnessGoal(PrefsUtilsWtContext prefsUtilsWtContext, int fitnessGoal) {
        final String KEY_FITNESS_GOAL = "active_daily_workout";
        prefsUtilsWtContext.setStringPreferenceProfile(KEY_FITNESS_GOAL, String.valueOf(fitnessGoal == 3 ? 1 : fitnessGoal + 2));
    }

    private static void saveHeightAndWeight(PrefsUtilsWtContext prefsUtilsWtContext, UserInfoWeb userInfoWeb) {
        final String WEIGHT_VALUE_SAVE = "KEY_WEIGHT_VALUE_F";
        final String KEY_HEIGHT_VALUE = "KEY_HEIGHT_VALUE_F";
        boolean isLbs = userInfoWeb.getWeightMeasureUnit().contains("lbs");
        boolean isIn = userInfoWeb.getHeightMeasureUnit().contains("in");
        float heightValueSave;
        float heightValue = userInfoWeb.getHeight();
        heightValueSave = getCurrentMetricHeight(heightValue, isIn);
        float weight = userInfoWeb.getWeight();
        float goalWeight = userInfoWeb.getGoalWeight();
        weight = getCurrentMetricWeight(weight, isLbs);
        goalWeight = getCurrentMetricWeight(goalWeight, isLbs);
        prefsUtilsWtContext.setFloatPreference(WEIGHT_VALUE_SAVE + "1", weight);
        prefsUtilsWtContext.setFloatPreference(WEIGHT_VALUE_SAVE + "2", goalWeight);
        prefsUtilsWtContext.setFloatPreference(KEY_HEIGHT_VALUE, heightValueSave);
    }

    private static void saveDays(PrefsUtilsWtContext prefsUtilsWtContext, String days) {
        prefsUtilsWtContext.setBooleanPreference("mondayDay", days.contains("0"));
        prefsUtilsWtContext.setBooleanPreference("tuesdayDay", days.contains("1"));
        prefsUtilsWtContext.setBooleanPreference("wednesdayDay", days.contains("2"));
        prefsUtilsWtContext.setBooleanPreference("thursdayDay", days.contains("3"));
        prefsUtilsWtContext.setBooleanPreference("fridayDay", days.contains("4"));
        prefsUtilsWtContext.setBooleanPreference("satDay", days.contains("5"));
        prefsUtilsWtContext.setBooleanPreference("sunDay", days.contains("6"));
    }
}
