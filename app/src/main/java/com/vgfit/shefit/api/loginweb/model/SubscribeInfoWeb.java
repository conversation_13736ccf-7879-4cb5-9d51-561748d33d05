package com.vgfit.shefit.api.loginweb.model;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class SubscribeInfoWeb implements Parcelable {
    @SerializedName("subscription_end_date")
    @Expose
    Long subscribeEndDate;
    @SerializedName("amount")
    @Expose
    Double amount;
    @SerializedName("next_amount")
    @Expose
    Double nextAmount;
    @SerializedName("has_subscription")
    @Expose
    Boolean hasSubscription;
    @SerializedName("exclusive_plan")
    @Expose
    int exclusivePlan;
    @SerializedName("subscription_plan")
    @Expose
    int subscriptionPlan;
    @SerializedName("cancel_at_period_end")
    @Expose
    Boolean cancelAtPeriodEnd;
    @SerializedName("used_first_discount")
    @Expose
    Boolean usedFirstDiscount;
    @SerializedName("used_cancel_discount")
    @Expose
    int usedCancelDiscount;
    @SerializedName("session_valid")
    @Expose
    Boolean isSessionValid;
    @SerializedName("trial_end")
    @Expose
    int trialEnd;
    @SerializedName("subscription_start_date")
    @Expose
    Long subscriptionStartDate;

    protected SubscribeInfoWeb(Parcel in) {
        if (in.readByte() == 0) {
            subscribeEndDate = null;
        } else {
            subscribeEndDate = in.readLong();
        }
        if (in.readByte() == 0) {
            amount = null;
        } else {
            amount = in.readDouble();
        }
        if (in.readByte() == 0) {
            nextAmount = null;
        } else {
            nextAmount = in.readDouble();
        }
        byte tmpHasSubscription = in.readByte();
        hasSubscription = tmpHasSubscription == 0 ? null : tmpHasSubscription == 1;
        exclusivePlan = in.readInt();
        subscriptionPlan = in.readInt();
        byte tmpCancelAtPeriodEnd = in.readByte();
        cancelAtPeriodEnd = tmpCancelAtPeriodEnd == 0 ? null : tmpCancelAtPeriodEnd == 1;
        byte tmpUsedFirstDiscount = in.readByte();
        usedFirstDiscount = tmpUsedFirstDiscount == 0 ? null : tmpUsedFirstDiscount == 1;
        usedCancelDiscount = in.readInt();
        byte tmpIsSessionValid = in.readByte();
        isSessionValid = tmpIsSessionValid == 0 ? null : tmpIsSessionValid == 1;
        trialEnd = in.readInt();
        if (in.readByte() == 0) {
            subscriptionStartDate = null;
        } else {
            subscriptionStartDate = in.readLong();
        }
    }
    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        if (subscribeEndDate == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(subscribeEndDate);
        }
        if (amount == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeDouble(amount);
        }
        if (nextAmount == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeDouble(nextAmount);
        }
        dest.writeByte((byte) (hasSubscription == null ? 0 : hasSubscription ? 1 : 2));
        dest.writeInt(exclusivePlan);
        dest.writeInt(subscriptionPlan);
        dest.writeByte((byte) (cancelAtPeriodEnd == null ? 0 : cancelAtPeriodEnd ? 1 : 2));
        dest.writeByte((byte) (usedFirstDiscount == null ? 0 : usedFirstDiscount ? 1 : 2));
        dest.writeInt(usedCancelDiscount);
        dest.writeByte((byte) (isSessionValid == null ? 0 : isSessionValid ? 1 : 2));
        dest.writeInt(trialEnd);
        if (subscriptionStartDate == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(subscriptionStartDate);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<SubscribeInfoWeb> CREATOR = new Creator<SubscribeInfoWeb>() {
        @Override
        public SubscribeInfoWeb createFromParcel(Parcel in) {
            return new SubscribeInfoWeb(in);
        }

        @Override
        public SubscribeInfoWeb[] newArray(int size) {
            return new SubscribeInfoWeb[size];
        }
    };

    public Long getSubscribeEndDate() {
        return subscribeEndDate;
    }

    public void setSubscribeEndDate(Long subscribeEndDate) {
        this.subscribeEndDate = subscribeEndDate;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getNextAmount() {
        return nextAmount;
    }

    public void setNextAmount(Double nextAmount) {
        this.nextAmount = nextAmount;
    }

    public Boolean getHasSubscription() {
        return hasSubscription;
    }

    public void setHasSubscription(Boolean hasSubscription) {
        this.hasSubscription = hasSubscription;
    }

    public int getExclusivePlan() {
        return exclusivePlan;
    }

    public void setExclusivePlan(int exclusivePlan) {
        this.exclusivePlan = exclusivePlan;
    }

    public int getSubscriptionPlan() {
        return subscriptionPlan;
    }

    public void setSubscriptionPlan(int subscriptionPlan) {
        this.subscriptionPlan = subscriptionPlan;
    }

    public Boolean getCancelAtPeriodEnd() {
        return cancelAtPeriodEnd;
    }

    public void setCancelAtPeriodEnd(Boolean cancelAtPeriodEnd) {
        this.cancelAtPeriodEnd = cancelAtPeriodEnd;
    }

    public Boolean getUsedFirstDiscount() {
        return usedFirstDiscount;
    }

    public void setUsedFirstDiscount(Boolean usedFirstDiscount) {
        this.usedFirstDiscount = usedFirstDiscount;
    }

    public int getUsedCancelDiscount() {
        return usedCancelDiscount;
    }

    public void setUsedCancelDiscount(int usedCancelDiscount) {
        this.usedCancelDiscount = usedCancelDiscount;
    }

    public Boolean getSessionValid() {
        return isSessionValid;
    }

    public void setSessionValid(Boolean sessionValid) {
        isSessionValid = sessionValid;
    }

    public int getTrialEnd() {
        return trialEnd;
    }

    public void setTrialEnd(int trialEnd) {
        this.trialEnd = trialEnd;
    }

    public Long getSubscriptionStartDate() {
        return subscriptionStartDate;
    }

    public void setSubscriptionStartDate(Long subscriptionStartDate) {
        this.subscriptionStartDate = subscriptionStartDate;
    }
}
