package com.vgfit.shefit;

import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.os.Bundle;
import android.util.Log;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;
import androidx.core.splashscreen.SplashScreen;
import androidx.fragment.app.FragmentManager;
import androidx.test.espresso.IdlingResource;

import com.nostra13.universalimageloader.core.ImageLoader;
import com.vgfit.shefit.api.FillLocalize;
import com.vgfit.shefit.api.callback.FilledWorkoutPlan;
import com.vgfit.shefit.api.callback.ResponseFilled;
import com.vgfit.shefit.api.callback.WantRunProfile;
import com.vgfit.shefit.api.service.FillDataService;
import com.vgfit.shefit.api.service.FillWorkoutPlan;
import com.vgfit.shefit.authorization.PrefsUtilsWtContext;
import com.vgfit.shefit.callbacks.TestSpeedIsCompleted;
import com.vgfit.shefit.databinding.SplashScreenLayoutBinding;
import com.vgfit.shefit.fragment.network.NetworkAlertFragment;
import com.vgfit.shefit.fragment.premium.BillingAndRemoteConfig;
import com.vgfit.shefit.fragment.premium.redesign.callback.OfferClosed;
import com.vgfit.shefit.fragment.userprofile.StartShapyRedesignFragment;
import com.vgfit.shefit.realm.DayPlan;
import com.vgfit.shefit.util.AdvertiseIdUtil;
import com.vgfit.shefit.util.Constant;
import com.vgfit.shefit.util.SpeedTest;
import com.vgfit.shefit.util.Translate;
import com.vgfit.shefit.util.idle.SimpleIdlingResource;
import com.vgfit.shefit.util.meta_events.MetaEventsSender;
import com.vgfit.shefit.util.screen.ImageUtils;

import java.util.Locale;

import io.realm.Realm;

public class StartScreen extends BillingAndRemoteConfig implements TestSpeedIsCompleted, WantRunProfile, ResponseFilled, FilledWorkoutPlan, OfferClosed {
    private static final String KEY_RUN_ONE_TIME = "KEY_RUN_ONE_TIME";
    private PrefsUtilsWtContext prefsUtilsWtContext;
    private boolean isSuccessfulFill;
    @Nullable
    private SimpleIdlingResource mIdlingResource;

    private static final String KEY_RUN_FIRST_TIME = "KEY_RUN_FIRST_TIME";

    @Override
    public void onCreate(Bundle icicle) {
        new MetaEventsSender(this).sendAppActivateInfo();
        SplashScreenLayoutBinding binding = SplashScreenLayoutBinding.inflate(getLayoutInflater());
        super.onCreate(icicle);
        setContentView(binding.getRoot());
        Window w = getWindow();
        new AdvertiseIdUtil().setAdvertiseIdAmplitude(this);
        w.setFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS, WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
        prefsUtilsWtContext = new PrefsUtilsWtContext(getApplicationContext());
        setTheme();

        if (isNetworkAvailable()) new SpeedTest(this).initSpeed();

        ImageLoader.getInstance().displayImage("assets://onboardingImage/10.webp", binding.ivSplash, ImageUtils.getDefaultDisplayImageOptions(), null);
        new FillLocalize(getApplicationContext(), this).startFillTranslate();
        // TODO: 13.03.2025 uncomment this line to send GAID to Amplitude
//        new AdvertiseIdUtil().setAdvertiseIdAmplitude(this);
    }

    private void setTheme() {
        prefsUtilsWtContext.setBooleanPreferenceProfile(Constant.THEME_DARK, false);
        getTheme().applyStyle(R.style.ThemeWhite, true);
    }

    public void closeFragment() {
        if (prefsUtilsWtContext != null) {
            prefsUtilsWtContext.setBooleanPreferenceProfile(Constant.KEY_ONBOARDING_FINISHED, true);
        }
        if (isNetworkAvailable()) {
            openOfferIfValid();
        } else {
            openNetworkProblem();
            isNetworkProblem = true;
        }
        new FillWorkoutPlan(getApplicationContext(), this, false).startFillData();

    }
    public void userLogged(){
        if (prefsUtilsWtContext != null) {
            prefsUtilsWtContext.setBooleanPreferenceProfile(Constant.KEY_ONBOARDING_FINISHED, true);
        }
        closeOfferFromOnBoarding();
        new FillWorkoutPlan(getApplicationContext(), this, false).startFillData();
    }

    public void closeOfferFromOnBoarding() {
        FragmentManager fm = StartScreen.this.getSupportFragmentManager();
        fm.popBackStack("general_profile", FragmentManager.POP_BACK_STACK_INCLUSIVE);
    }

    @Override
    public void isWorkoutFilled(boolean isFilled) {
        if (!isFilled) {
            failedInternet();
        } else {
            isSuccessfulFill = true;
            if (!isRunOffer && !isNetworkProblem) {
                openApp();
            }
        }
    }

    private void openNetworkProblem() {
        StartScreen.this.getSupportFragmentManager().beginTransaction().add(R.id.root_fragment, NetworkAlertFragment.newInstance()).addToBackStack("network_problem").commitAllowingStateLoss();
    }

    public void closeNetworkProblem() {
        FragmentManager fm = StartScreen.this.getSupportFragmentManager();
        fm.popBackStack("network_problem", FragmentManager.POP_BACK_STACK_INCLUSIVE);
        if (isNetworkAvailable() && !Constant.premium) {
            openOfferIfValid();
        } else {
            openApp();
        }
    }

    private void openOfferIfValid() {
        boolean isOneTime = prefsUtilsWtContext.getBooleanPreferenceProfile(KEY_RUN_ONE_TIME, false);
        if (!Constant.premium) {
            if (!isOneTime) {
                isRunFirstTime();
                prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_RUN_FIRST_TIME, true);
            } else {
                oneTime();
                prefsUtilsWtContext.setBooleanPreferenceProfile(KEY_RUN_FIRST_TIME, true);
            }
        } else {
            closeOfferFromOnBoarding();
        }
    }

    private void openApp() {
        Log.e("TestApp", "OpenApp");
        Intent i = new Intent();
        i.setClass(getApplicationContext(), MainActivity.class);
        startActivity(i);
        finish();
    }

    @Override
    public void testSpeedCompleted(boolean isBadInternet) {
        Constant.setIsBadInternet(isBadInternet);
    }

    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            Network network = connectivityManager.getActiveNetwork();
            if (network != null) {
                NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(network);
                return networkCapabilities != null &&
                        networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                        networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
            }
        }
        return false;
    }

    @Override
    public void filledDbResult(boolean valid) {
        if (!valid) {
            failedInternet();
        } else {
            new Translate().initTranslate(getLanguageValid());
            if (BuildConfig.DEBUG) {
                statusTest(true);
            }
            if (prefsUtilsWtContext.getBooleanPreferenceProfile(Constant.KEY_ONBOARDING_FINISHED, false) && getValidProfile()) {
                openApp();
            } else {
                StartScreen.this.getSupportFragmentManager().beginTransaction().add(R.id.root_fragment, StartShapyRedesignFragment.newInstance(true)).addToBackStack("general_profile").commitAllowingStateLoss();
            }
        }

    }

    public void statusTest(boolean isStartTest) {
        if (mIdlingResource != null) {
            mIdlingResource.setIdleState(isStartTest);
        }
    }

    private boolean getValidProfile() {
        boolean existDataPlan;
        Realm realm = Realm.getDefaultInstance();
        existDataPlan = (realm.where(DayPlan.class).findFirst() != null);
        realm.close();
        return existDataPlan;
    }

    private String getLanguageValid() {
        String lang = Locale.getDefault().getLanguage();
        String langUser = prefsUtilsWtContext.getStringPreferenceProfile("langDevice");
        if (langUser != null) {
            lang = langUser;
        }
        return lang;
    }

    @Override
    public void validRunProfile(boolean valid) {
        if (!valid) {
            failedInternet();
        } else
            new FillDataService(getApplicationContext(), this).startFillData();
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    private void failedInternet() {
        Toast.makeText(getApplicationContext(), getResources().getString(R.string.conn), Toast.LENGTH_LONG).show();
        new java.util.Timer().schedule(
                new java.util.TimerTask() {
                    @Override
                    public void run() {
                        runOnUiThread(() -> finish());
                    }
                },
                2000);
    }

    @Override
    public void offerFinished() {
        if (isSuccessfulFill) {
            openApp();
        } else {
            if (!isNetworkAvailable())
                failedInternet();
        }
    }

    @VisibleForTesting
    @NonNull
    public IdlingResource getIdlingResource() {
        Log.d("TestTest", "getIdlingResource");
        if (mIdlingResource == null) {
            mIdlingResource = new SimpleIdlingResource();
            statusTest(false);
            Log.d("TestTest", "IdlingResource initialize");
        }
        return mIdlingResource;
    }

}