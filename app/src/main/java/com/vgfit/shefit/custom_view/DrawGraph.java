package com.vgfit.shefit.custom_view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;

public class DrawGraph extends View {

    Paint paint;
    Path path;

    public DrawGraph(Context context) {
        super(context);
        init();
    }

    public DrawGraph(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public DrawGraph(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    private void init() {
        paint = new Paint();

        paint.setStyle(Paint.Style.STROKE);

    }

    @Override
    protected void onDraw(@NonNull Canvas canvas) {
        super.onDraw(canvas);
        path = new Path();
        paint.setColor(Color.RED);
        paint.setStrokeWidth(20);
        path.cubicTo(0, 0, 286, 350, 336, 0);
        path.cubicTo(450, 100, 500, 50, 550, 150);
        canvas.drawPath(path, paint);

    }
}