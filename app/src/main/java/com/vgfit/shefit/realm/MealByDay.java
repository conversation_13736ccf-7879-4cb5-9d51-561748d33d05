package com.vgfit.shefit.realm;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class MealByDay extends RealmObject {
    @SerializedName("id")
    @Expose
    @PrimaryKey
    @Required
    private String id;
    @SerializedName("order")
    @Expose
    private int order_;
    @SerializedName("meal")
    @Expose
    private Meal meal;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getOrder() {
        return order_;
    }

    public void setOrder(int order) {
        this.order_ = order;
    }

    public Meal getMeal() {
        return meal;
    }

    public void setMeal(Meal meal) {
        this.meal = meal;
    }
}