package com.vgfit.shefit.realm;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class Achievement extends RealmObject {

    @SerializedName("id")
    @Expose
    @PrimaryKey
    private int id;

    @SerializedName("calories")
    @Expose
    private int calories;

    @SerializedName("badge_name_key")
    @Expose
    @Required
    private String badgeNameKey;

    @SerializedName("description_key")
    @Expose
    @Required
    private String descriptionKey;

    @SerializedName("is_unlocked")
    @Expose
    private boolean isUnlocked;

    public Achievement() {
    }

    public Achievement(int id, int calories, String badgeNameKey, String descriptionKey) {
        this.id = id;
        this.calories = calories;
        this.badgeNameKey = badgeNameKey;
        this.descriptionKey = descriptionKey;
        this.isUnlocked = false;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getCalories() {
        return calories;
    }

    public void setCalories(int calories) {
        this.calories = calories;
    }

    public String getBadgeNameKey() {
        return badgeNameKey;
    }

    public void setBadgeNameKey(String badgeNameKey) {
        this.badgeNameKey = badgeNameKey;
    }

    public String getDescriptionKey() {
        return descriptionKey;
    }

    public void setDescriptionKey(String descriptionKey) {
        this.descriptionKey = descriptionKey;
    }

    public boolean isUnlocked() {
        return isUnlocked;
    }

    public void setUnlocked(boolean unlocked) {
        isUnlocked = unlocked;
    }
}
