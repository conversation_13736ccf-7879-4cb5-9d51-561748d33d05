package com.vgfit.shefit.realm;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class NutritionPlan extends RealmObject {
    @SerializedName("id")
    @Expose
    @PrimaryKey
    @Required
    private String id;

    @SerializedName("name")
    @Expose
    private String name;

    @SerializedName("image")
    @Expose
    private String image;

    @SerializedName("description")
    @Expose
    private String description_;

    @SerializedName("order")
    @Expose
    private int order_;

    @SerializedName("days")
    @Expose
    private RealmList<NutritionDay> days;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getDescription_() {
        return description_;
    }

    public void setDescription_(String description_) {
        this.description_ = description_;
    }

    public int getOrder() {
        return order_;
    }

    public void setOrder(int order) {
        this.order_ = order;
    }

    public RealmList<NutritionDay> getDays() {
        return days;
    }

    public void setDays(RealmList<NutritionDay> days) {
        this.days = days;
    }
}
