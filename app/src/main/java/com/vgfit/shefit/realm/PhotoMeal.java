package com.vgfit.shefit.realm;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class PhotoMeal extends RealmObject {
    public PhotoMeal(){}
    @PrimaryKey
    @SerializedName("id")
    private int id;

    @SerializedName("order")
    @Expose
    private int order;

    @SerializedName("video")
    @Expose
    private String linkPhoto;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public String getLinkPhoto() {
        return linkPhoto;
    }

    public void setLinkPhoto(String linkPhoto) {
        this.linkPhoto = linkPhoto;
    }
}
