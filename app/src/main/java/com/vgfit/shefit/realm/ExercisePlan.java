package com.vgfit.shefit.realm;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class ExercisePlan extends RealmObject {
    public ExercisePlan() {
    }

    @PrimaryKey
    @SerializedName("id")
    private int id;

    @SerializedName("superset")
    @Expose
    private int idSupersetPlan;

    @SerializedName("time")
    @Expose
    private int time;

    @SerializedName("order")
    @Expose
    private int order;

    @SerializedName("exercise")
    @Expose
    private Exercise exercise;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getIdSupersetPlan() {
        return idSupersetPlan;
    }

    public void setIdSupersetPlan(int idSupersetPlan) {
        this.idSupersetPlan = idSupersetPlan;
    }

    public int getTime() {
        return time;
    }

    public void setTime(int time) {
        this.time = time;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public Exercise getExercise() {
        return exercise;
    }

    public void setExercise(Exercise exercise) {
        this.exercise = exercise;
    }

}
