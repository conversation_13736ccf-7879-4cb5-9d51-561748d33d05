package com.vgfit.shefit.realm;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class CoverDayPlan extends RealmObject {
    public CoverDayPlan() {
    }
    @PrimaryKey
    @SerializedName("id")
    private int id;

    @SerializedName("order")
    @Expose
    private int order;

    @SerializedName("image")
    @Expose
    private String imageCover;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public String getImageCover() {
        return imageCover;
    }

    public void setImageCover(String imageCover) {
        this.imageCover = imageCover;
    }
}
