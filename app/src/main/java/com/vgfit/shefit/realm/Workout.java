package com.vgfit.shefit.realm;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class Workout extends RealmObject implements Parcelable {
    @SerializedName("id")
    @Expose
    @PrimaryKey
    @Required
    private String id;

    @SerializedName("name")
    @Expose
    private String name;

    @SerializedName("description")
    @Expose
    private String description_;

    @SerializedName("duration")
    @Expose
    private int duration;

    @SerializedName("order")
    @Expose
    private int order;

    @SerializedName("kcal")
    @Expose
    private int kcal;

    @SerializedName("items")
    @Expose
    private RealmList<WorkoutExercise> workoutExercises;

    @SerializedName("level")
    @Expose
    private WorkoutLevel level;

    public Workout() {
    }


    protected Workout(Parcel in) {
        id = in.readString();
        name = in.readString();
        description_ = in.readString();
        duration = in.readInt();
        order = in.readInt();
        kcal = in.readInt();
    }

    public static final Creator<Workout> CREATOR = new Creator<Workout>() {
        @Override
        public Workout createFromParcel(Parcel in) {
            return new Workout(in);
        }

        @Override
        public Workout[] newArray(int size) {
            return new Workout[size];
        }
    };

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public String getDescription_() {
        return description_;
    }

    public void setDescription_(String description_) {
        this.description_ = description_;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public RealmList<WorkoutExercise> getWorkoutExercises() {
        return workoutExercises;
    }

    public void setWorkoutExercises(RealmList<WorkoutExercise> workoutExercises) {
        this.workoutExercises = workoutExercises;
    }

    public WorkoutLevel getLevel() {
        return level;
    }

    public void setLevel(WorkoutLevel level) {
        this.level = level;
    }

    public int getKcal() {
        return kcal;
    }

    public void setKcal(int kcal) {
        this.kcal = kcal;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(id);
        dest.writeString(name);
        dest.writeString(description_);
        dest.writeInt(duration);
        dest.writeInt(order);
        dest.writeInt(kcal);
    }
}