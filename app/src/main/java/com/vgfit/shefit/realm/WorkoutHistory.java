package com.vgfit.shefit.realm;

import java.util.Date;
import java.util.UUID;

import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class WorkoutHistory extends RealmObject {
    public static final String TYPE_SUPERSET = "SUPERSET";
    public static final String TYPE_DAYPLAN = "DAYPLAN";
    
    @PrimaryKey
    @Required
    private String id;
    
    @Required
    private String workoutId;

    private int workoutIntId;
    
    private String workoutName;

    private String workoutLevel;
    
    @Required
    private String workoutType;
    
    @Required
    private Date completedDate;
    
    private int duration;

    private int kcalBurned;
    
    private int progressPercentage;
    
    private RealmList<String> completedExerciseIds;
    
    public WorkoutHistory() {
        this.id = UUID.randomUUID().toString();
        this.completedDate = new Date();
        this.completedExerciseIds = new RealmList<>();
        this.workoutLevel = "";
    }
    
    // Getters și Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getWorkoutId() {
        return workoutId;
    }
    
    public void setWorkoutId(String workoutId) {
        this.workoutId = workoutId;
    }
    
    public int getWorkoutIntId() {
        return workoutIntId;
    }
    
    public void setWorkoutIntId(int workoutIntId) {
        this.workoutIntId = workoutIntId;
    }
    
    public String getWorkoutName() {
        return workoutName;
    }
    
    public void setWorkoutName(String workoutName) {
        this.workoutName = workoutName;
    }
    
    public String getWorkoutType() {
        return workoutType;
    }
    
    public void setWorkoutType(String workoutType) {
        this.workoutType = workoutType;
    }
    
    public Date getCompletedDate() {
        return completedDate;
    }
    
    public void setCompletedDate(Date completedDate) {
        this.completedDate = completedDate;
    }
    
    public int getDuration() {
        return duration;
    }
    
    public void setDuration(int duration) {
        this.duration = duration;
    }
    
    public int getKcalBurned() {
        return kcalBurned;
    }
    
    public void setKcalBurned(int kcalBurned) {
        this.kcalBurned = kcalBurned;
    }
    
    public int getProgressPercentage() {
        return progressPercentage;
    }
    
    public void setProgressPercentage(int progressPercentage) {
        this.progressPercentage = progressPercentage;
    }
    
    public RealmList<String> getCompletedExerciseIds() {
        return completedExerciseIds;
    }
    
    public void setCompletedExerciseIds(RealmList<String> completedExerciseIds) {
        this.completedExerciseIds.clear();
        this.completedExerciseIds.addAll(completedExerciseIds);
    }
    
    public void addCompletedExerciseId(String exerciseId) {
        if (!completedExerciseIds.contains(exerciseId)) {
            completedExerciseIds.add(exerciseId);
        }
    }
    public String getWorkoutLevel() {
        return workoutLevel;
    }

    public void setWorkoutLevel(String workoutLevel) {
        this.workoutLevel = workoutLevel;
    }
}