package com.vgfit.shefit.realm;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class NutritionDay extends RealmObject {
    @SerializedName("id")
    @Expose
    @PrimaryKey
    @Required
    private String id;

    @SerializedName("number")
    @Expose
    private int number;

    @SerializedName("order")
    @Expose
    private int order;

    @SerializedName("meals")
    @Expose
    private RealmList<MealByDay> meals;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public RealmList<MealByDay> getMeals() {
        return meals;
    }

    public void setMeals(RealmList<MealByDay> meals) {
        this.meals = meals;
    }
}
