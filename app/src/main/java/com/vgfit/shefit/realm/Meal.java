package com.vgfit.shefit.realm;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class Meal extends RealmObject implements Parcelable {
    public Meal() {
    }

    @SerializedName("id")
    @Expose
    @PrimaryKey
    @Required
    private String id;

    @SerializedName("name")
    @Expose
    private String name;

    @SerializedName("ingredients")
    @Expose
    private String ingredients;

    @SerializedName("steps")
    @Expose
    private String steps;

    @SerializedName("image")
    @Expose
    private String image;

    @SerializedName("vertical_image")
    @Expose
    private String verticalImage;

    @SerializedName("calories")
    @Expose
    private int calories;

    @SerializedName("proteins")
    @Expose
    private int proteins;

    @SerializedName("fats")
    @Expose
    private int fats;

    @SerializedName("carbs")
    @Expose
    private int carbs;

    @SerializedName("time_to_cook")
    @Expose
    private int timeToCook;

    protected Meal(Parcel in) {
        id = in.readString();
        name = in.readString();
        ingredients = in.readString();
        steps = in.readString();
        image = in.readString();
        verticalImage = in.readString();
        calories = in.readInt();
        proteins = in.readInt();
        fats = in.readInt();
        carbs = in.readInt();
        timeToCook = in.readInt();
    }

    public static final Creator<Meal> CREATOR = new Creator<Meal>() {
        @Override
        public Meal createFromParcel(Parcel in) {
            return new Meal(in);
        }

        @Override
        public Meal[] newArray(int size) {
            return new Meal[size];
        }
    };

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIngredients() {
        return ingredients;
    }

    public void setIngredients(String ingredients) {
        this.ingredients = ingredients;
    }

    public String getSteps() {
        return steps;
    }

    public void setSteps(String steps) {
        this.steps = steps;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getVerticalImage() {
        return verticalImage;
    }

    public void setVerticalImage(String verticalImage) {
        this.verticalImage = verticalImage;
    }

    public int getCalories() {
        return calories;
    }

    public void setCalories(int calories) {
        this.calories = calories;
    }

    public int getProteins() {
        return proteins;
    }

    public void setProteins(int proteins) {
        this.proteins = proteins;
    }

    public int getFats() {
        return fats;
    }

    public void setFats(int fats) {
        this.fats = fats;
    }

    public int getCarbs() {
        return carbs;
    }

    public void setCarbs(int carbs) {
        this.carbs = carbs;
    }

    public int getTimeToCook() {
        return timeToCook;
    }

    public void setTimeToCook(int timeToCook) {
        this.timeToCook = timeToCook;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        dest.writeString(id);
        dest.writeString(name);
        dest.writeString(ingredients);
        dest.writeString(steps);
        dest.writeString(image);
        dest.writeString(verticalImage);
        dest.writeInt(calories);
        dest.writeInt(proteins);
        dest.writeInt(fats);
        dest.writeInt(carbs);
        dest.writeInt(timeToCook);
    }
}
