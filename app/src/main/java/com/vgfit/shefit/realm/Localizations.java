package com.vgfit.shefit.realm;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class Localizations extends RealmObject {
    @Required
    private String key;
    @Required
    private String value;
    @Required
    private String languageName;

    public Localizations(String key) {
        this.key = key;
    }

    public Localizations() {
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLanguageName() {
        return languageName;
    }

    public void setLanguageName(String languageName) {
        this.languageName = languageName;
    }
}