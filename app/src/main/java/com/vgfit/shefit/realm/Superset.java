package com.vgfit.shefit.realm;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class Superset extends RealmObject implements Parcelable {
    public static final Creator<Superset> CREATOR = new Creator<Superset>() {
        @Override
        public Superset createFromParcel(Parcel in) {
            return new Superset(in);
        }

        @Override
        public Superset[] newArray(int size) {
            return new Superset[size];
        }
    };
    @PrimaryKey
    @Required
    @SerializedName("id")
    @Expose
    private String id;

    @SerializedName("name")
    @Expose
    private String name;


    @SerializedName("description")
    @Expose
    private String description_;

    @SerializedName("image")
    @Expose
    private String image;

    @SerializedName("order")
    @Expose
    private int order_;

    @SerializedName("workouts")
    @Expose
    private RealmList<Workout> workouts;

    public Superset() {
    }

    protected Superset(Parcel in) {
        id = in.readString();
        name = in.readString();
        description_ = in.readString();
        image = in.readString();
        order_ = in.readInt();
    }

    public int getOrder() {
        return order_;
    }

    public void setOrder(int order) {
        this.order_ = order;
    }

    public RealmList<Workout> getWorkouts() {
        return workouts;
    }

    public void setWorkouts(RealmList<Workout> workouts) {
        this.workouts = workouts;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription_() {
        return description_;
    }

    public void setDescription_(String description_) {
        this.description_ = description_;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(id);
        dest.writeString(name);
        dest.writeString(description_);
        dest.writeString(image);
        dest.writeInt(order_);
    }
}