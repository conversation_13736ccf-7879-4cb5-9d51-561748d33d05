package com.vgfit.shefit.realm;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class WorkoutExercise extends RealmObject implements Parcelable {
    public WorkoutExercise() {
    }

    @SerializedName("id")
    @Expose
    @PrimaryKey
    @Required
    private String id;

    @SerializedName("duration")
    @Expose
    private int duration;

    @SerializedName("rest_time")
    @Expose
    private int restTime;

    @SerializedName("order")
    @Expose
    private int order;

    @SerializedName("exercise")
    @Expose
    private Exercise exercise;

    protected WorkoutExercise(Parcel in) {
        id = in.readString();
        duration = in.readInt();
        restTime = in.readInt();
        order = in.readInt();
        exercise = in.readParcelable(Exercise.class.getClassLoader());
    }

    public static final Creator<WorkoutExercise> CREATOR = new Creator<WorkoutExercise>() {
        @Override
        public WorkoutExercise createFromParcel(Parcel in) {
            return new WorkoutExercise(in);
        }

        @Override
        public WorkoutExercise[] newArray(int size) {
            return new WorkoutExercise[size];
        }
    };

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getRestTime() {
        return restTime;
    }

    public void setRestTime(int restTime) {
        this.restTime = restTime;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public Exercise getExercise() {
        return exercise;
    }

    public void setExercise(Exercise exercise) {
        this.exercise = exercise;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(id);
        dest.writeInt(duration);
        dest.writeInt(restTime);
        dest.writeInt(order);
        dest.writeParcelable(exercise, flags);
    }
}