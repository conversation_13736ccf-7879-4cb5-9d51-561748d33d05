package com.vgfit.shefit.realm;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class SupersetPlan extends RealmObject implements Parcelable {
    @PrimaryKey
    @SerializedName("id")
    private int id;

    @SerializedName("rest_time")
    @Expose
    private int restTime;

    @SerializedName("order")
    @Expose
    private int order;

    @SerializedName("exercises")
    @Expose
    private RealmList<ExercisePlan> listExercisesPlan;

    public SupersetPlan() {
    }

    protected SupersetPlan(Parcel in) {
        id = in.readInt();
        restTime = in.readInt();
        order = in.readInt();
    }

    public static final Creator<SupersetPlan> CREATOR = new Creator<SupersetPlan>() {
        @Override
        public SupersetPlan createFromParcel(Parcel in) {
            return new SupersetPlan(in);
        }

        @Override
        public SupersetPlan[] newArray(int size) {
            return new SupersetPlan[size];
        }
    };

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getRestTime() {
        return restTime;
    }

    public void setRestTime(int restTime) {
        this.restTime = restTime;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public RealmList<ExercisePlan> getListExercisesPlan() {
        return listExercisesPlan;
    }

    public void setListExercisesPlan(RealmList<ExercisePlan> listExercisesPlan) {
        this.listExercisesPlan = listExercisesPlan;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        dest.writeInt(restTime);
        dest.writeInt(order);
    }
}
