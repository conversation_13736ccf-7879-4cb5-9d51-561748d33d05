package com.vgfit.shefit.realm;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class DayPlan extends RealmObject implements Parcelable  {
    public DayPlan(){}
    @PrimaryKey
    @SerializedName("id")
    @Expose
    private int id;

    @SerializedName("description")
    @Expose
    private String description;

    @SerializedName("warm_up_description")
    @Expose
    private String warmUpDescription;


    @SerializedName("recommendations")
    @Expose
    private String recomandations;

    @SerializedName("order")
    @Expose
    private int order;

    @SerializedName("workout")
    @Expose
    private int workout;

    protected DayPlan(Parcel in) {
        id = in.readInt();
        description = in.readString();
        warmUpDescription = in.readString();
        recomandations = in.readString();
        order = in.readInt();
        workout = in.readInt();
    }

    public static final Creator<DayPlan> CREATOR = new Creator<DayPlan>() {
        @Override
        public DayPlan createFromParcel(Parcel in) {
            return new DayPlan(in);
        }

        @Override
        public DayPlan[] newArray(int size) {
            return new DayPlan[size];
        }
    };

    public RealmList<SupersetPlan> getSupersetPlans() {
        return supersetPlans;
    }

    public void setSupersetPlans(RealmList<SupersetPlan> supersetPlans) {
        this.supersetPlans = supersetPlans;
    }

    @SerializedName("supersets")
    private RealmList<SupersetPlan> supersetPlans;


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getWarmUpDescription() {
        return warmUpDescription;
    }

    public void setWarmUpDescription(String warmUpDescription) {
        this.warmUpDescription = warmUpDescription;
    }

    public String getRecomandations() {
        return recomandations;
    }

    public void setRecomandations(String recomandations) {
        this.recomandations = recomandations;
    }

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public int getWorkout() {
        return workout;
    }

    public void setWorkout(int workout) {
        this.workout = workout;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        dest.writeString(description);
        dest.writeString(warmUpDescription);
        dest.writeString(recomandations);
        dest.writeInt(order);
        dest.writeInt(workout);
    }
}
