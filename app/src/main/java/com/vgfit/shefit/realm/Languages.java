package com.vgfit.shefit.realm;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class Languages extends RealmObject {
    @SerializedName("id")
    @Expose
    @PrimaryKey
    private int id;

    @SerializedName("name")
    @Expose
    @Required
    private String name;

    @SerializedName("iso_639_1_code")
    @Expose
    private String isoOneCode;

    @SerializedName("iso_639_2_code")
    @Expose
    private String isoTwoCode;

    @SerializedName("script_language_id")
    @Expose
    private String scriptLanguageID;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIsoOneCode() {
        return isoOneCode;
    }

    public void setIsoOneCode(String isoOneCode) {
        this.isoOneCode = isoOneCode;
    }

    public String getIsoTwoCode() {
        return isoTwoCode;
    }

    public void setIsoTwoCode(String isoTwoCode) {
        this.isoTwoCode = isoTwoCode;
    }

    public String getScriptLanguageID() {
        return scriptLanguageID;
    }

    public void setScriptLanguageID(String scriptLanguageID) {
        this.scriptLanguageID = scriptLanguageID;
    }
}
