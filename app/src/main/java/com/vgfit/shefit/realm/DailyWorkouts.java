package com.vgfit.shefit.realm;

import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class DailyWorkouts extends RealmObject {
    @PrimaryKey
    @Required
    private String uuid;
    @Required
    private String description;
    @Required
    private String warm_up_description;
    @Required
    private String recommendations;
    private RealmList<DailyExercise> exercises;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getWarm_up_description() {
        return warm_up_description;
    }

    public void setWarm_up_description(String warm_up_description) {
        this.warm_up_description = warm_up_description;
    }

    public String getRecommendations() {
        return recommendations;
    }

    public void setRecommendations(String recommendations) {
        this.recommendations = recommendations;
    }

    public RealmList<DailyExercise> getExercises() {
        return exercises;
    }

    public void setExercises(RealmList<DailyExercise> exercises) {
        this.exercises = exercises;
    }
}