package com.vgfit.shefit.realm;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;
import io.realm.annotations.Required;

public class Exercise extends RealmObject implements Parcelable {

    @SerializedName("id")
    @Expose
    @PrimaryKey
    @Required
    private String id;

    @SerializedName("name")
    @Expose
    private String name;

    @SerializedName("thumbnail")
    @Expose
    private String thumbnail;

    @SerializedName("image")
    @Expose
    private String image;

    @SerializedName("video")
    @Expose
    private String video;

    @SerializedName("video_fullhd")
    @Expose
    private String videoHD;

    @SerializedName("video_low_quality")
    @Expose
    private String videoLowQ;

    @SerializedName("body_parts_involved")
    @Expose
    private String bodyPartsInvolved;

    @SerializedName("equipment")
    @Expose
    private String equipment;

    @SerializedName("description")
    @Expose
    private String description_;

    @SerializedName("category")
    @Expose
//    @Required
    private String category;
    @SerializedName("playlist")
    @Expose
    private String playlist;

    public Exercise() {
    }


    protected Exercise(Parcel in) {
        id = in.readString();
        name = in.readString();
        thumbnail = in.readString();
        image = in.readString();
        video = in.readString();
        videoHD = in.readString();
        videoLowQ = in.readString();
        bodyPartsInvolved = in.readString();
        equipment = in.readString();
        description_ = in.readString();
        category = in.readString();
        playlist = in.readString();
    }

    public static final Creator<Exercise> CREATOR = new Creator<Exercise>() {
        @Override
        public Exercise createFromParcel(Parcel in) {
            return new Exercise(in);
        }

        @Override
        public Exercise[] newArray(int size) {
            return new Exercise[size];
        }
    };

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription_() {
        return description_;
    }

    public void setDescription_(String description_) {
        this.description_ = description_;
    }

    public String getBodyPartsInvolved() {
        return bodyPartsInvolved;
    }

    public void setBodyPartsInvolved(String bodyPartsInvolved) {
        this.bodyPartsInvolved = bodyPartsInvolved;
    }

    public String getVideoHD() {
        return videoHD;
    }

    public void setVideoHD(String videoHD) {
        this.videoHD = videoHD;
    }

    public String getVideoLowQ() {
        return videoLowQ;
    }

    public void setVideoLowQ(String videoLowQ) {
        this.videoLowQ = videoLowQ;
    }

    public String getEquipment() {
        return equipment;
    }

    public void setEquipment(String equipment) {
        this.equipment = equipment;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getVideo() {
        return video;
    }

    public void setVideo(String video) {
        this.video = video;
    }

    public String getPlaylist() {
        return playlist;
    }

    public void setPlaylist(String playlist) {
        this.playlist = playlist;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        dest.writeString(id);
        dest.writeString(name);
        dest.writeString(thumbnail);
        dest.writeString(image);
        dest.writeString(video);
        dest.writeString(videoHD);
        dest.writeString(videoLowQ);
        dest.writeString(bodyPartsInvolved);
        dest.writeString(equipment);
        dest.writeString(description_);
        dest.writeString(category);
        dest.writeString(playlist);
    }
}
