package com.vgfit.shefit;

import androidx.annotation.NonNull;
import android.util.Log;

import java.util.Date;

import io.realm.DynamicRealm;
import io.realm.FieldAttribute;
import io.realm.RealmMigration;
import io.realm.RealmObjectSchema;
import io.realm.RealmSchema;

class Migration implements RealmMigration {
    @Override
    public void migrate(@NonNull DynamicRealm realm, long oldVersion, long newVersion) {
        Log.e("realmVersion", "oldVersion --->" + oldVersion);
        Log.e("realmVersion", "newVersion --->" + newVersion);

        Log.e("testMigration", "migration");

        RealmSchema schema = realm.getSchema();
        if (oldVersion <= 13) {
            Log.e("testMigration", "==12");
            RealmObjectSchema exerciseSchema = schema.get("DailyExercise");
            if (exerciseSchema == null) {
                Log.e("testMigration", "exerciseSchema == null");

                exerciseSchema = schema.create("DailyExercise");
            }

            if (exerciseSchema != null) {
                if (exerciseSchema.hasField("uuid"))
                    exerciseSchema.removeField("uuid");
                if (exerciseSchema.hasField("description"))
                    exerciseSchema.removeField("description");
                if (exerciseSchema.hasField("sets"))
                    exerciseSchema.removeField("sets");
                if (exerciseSchema.hasField("repetitions"))
                    exerciseSchema.removeField("repetitions");
                if (exerciseSchema.hasField("rest_time"))
                    exerciseSchema.removeField("rest_time");
                if (exerciseSchema.hasField("order"))
                    exerciseSchema.removeField("order");
                if (exerciseSchema.hasField("exercise"))
                    exerciseSchema.removeField("exercise");

                exerciseSchema.addField("uuid", String.class, FieldAttribute.REQUIRED);
                exerciseSchema.addPrimaryKey("uuid");
                exerciseSchema.addField("description", String.class, FieldAttribute.REQUIRED);
                exerciseSchema.addField("sets", String.class, FieldAttribute.REQUIRED);
                exerciseSchema.addField("repetitions", String.class, FieldAttribute.REQUIRED);
                exerciseSchema.addField("rest_time", Integer.class, FieldAttribute.REQUIRED);
                exerciseSchema.addField("order", Integer.class, FieldAttribute.REQUIRED);
                exerciseSchema.addField("exercise", Integer.class, FieldAttribute.REQUIRED);
            }

            RealmObjectSchema workoutSchema = schema.get("DailyWorkouts");
            if (workoutSchema == null) {
                workoutSchema = schema.create("DailyWorkouts");

                Log.e("testMigration", "workoutSchema == null");
            }

            if (workoutSchema != null) {
                if (workoutSchema.hasField("uuid"))
                    workoutSchema.removeField("uuid");
                if (workoutSchema.hasField("description"))
                    workoutSchema.removeField("description");
                if (workoutSchema.hasField("warm_up_description"))
                    workoutSchema.removeField("warm_up_description");
                if (workoutSchema.hasField("recommendations"))
                    workoutSchema.removeField("recommendations");
                if (workoutSchema.hasField("exercises"))
                    workoutSchema.removeField("exercises");

                workoutSchema.addField("uuid", String.class, FieldAttribute.REQUIRED);
                workoutSchema.addPrimaryKey("uuid");
                workoutSchema.addField("description", String.class, FieldAttribute.REQUIRED);
                workoutSchema.addField("warm_up_description", String.class, FieldAttribute.REQUIRED);
                workoutSchema.addField("recommendations", String.class, FieldAttribute.REQUIRED);
                if (exerciseSchema != null)
                    workoutSchema.addRealmListField("exercises", exerciseSchema);
            }
        }

        if (oldVersion <= 14) {
            RealmObjectSchema workoutHistorySchema = schema.get("WorkoutHistory");
            if (workoutHistorySchema == null) {
                workoutHistorySchema = schema.create("WorkoutHistory");

                workoutHistorySchema.addField("id", String.class, FieldAttribute.PRIMARY_KEY, FieldAttribute.REQUIRED);
                workoutHistorySchema.addField("workoutId", String.class, FieldAttribute.REQUIRED);
                workoutHistorySchema.addField("workoutIntId", int.class);
                workoutHistorySchema.addField("workoutName", String.class);
                workoutHistorySchema.addField("workoutLevel", String.class);
                workoutHistorySchema.addField("workoutType", String.class, FieldAttribute.REQUIRED);
                workoutHistorySchema.addField("completedDate", Date.class, FieldAttribute.REQUIRED);
                workoutHistorySchema.addField("duration", int.class);
                workoutHistorySchema.addField("kcalBurned", int.class);
                workoutHistorySchema.addField("progressPercentage", int.class);
                workoutHistorySchema.addRealmListField("completedExerciseIds", String.class);

                Log.e("Migration", "Created WorkoutHistory schema");
            }
        }

        if (oldVersion <= 15) {
            RealmObjectSchema achievementSchema = schema.get("Achievement");
            if (achievementSchema == null) {
                achievementSchema = schema.create("Achievement");

                achievementSchema.addField("id", int.class, FieldAttribute.PRIMARY_KEY);
                achievementSchema.addField("calories", int.class);
                achievementSchema.addField("badgeNameKey", String.class, FieldAttribute.REQUIRED);
                achievementSchema.addField("descriptionKey", String.class, FieldAttribute.REQUIRED);
                achievementSchema.addField("isUnlocked", boolean.class);

                Log.e("Migration", "Created Achievement schema");
            }
        }
    }
}
