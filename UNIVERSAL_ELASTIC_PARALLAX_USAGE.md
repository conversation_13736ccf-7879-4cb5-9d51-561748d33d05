# Universal Elastic Parallax - Ghid de Utilizare

## Descriere

`UniversalElasticParallax` este o clasă universală care poate fi aplicată pe **orice 2 View-uri/containere** pentru efectul elastic parallax, similar cu `ParallaxListView_new` dar complet configurabil și reutilizabil.

## Caracteristici

### ✅ **Universal**
- Funcționează cu orice tip de View: `ImageView`, `TextView`, `LinearLayout`, `CardView`, `FrameLayout`, etc.
- Nu este limitat la imagine + text - poate fi aplicat pe orice 2 containere
- **Funcționează CU sau FĂRĂ ScrollView** - poate folosi orice View ca container

### ✅ **Configurabil**
- Factor elastic ajustabil
- Limite de stretch/compress personalizabile
- Durată animații configurabilă
- Factori de încetinire progresivă ajustabili
- **Dezactivare click-uri** în secondary view în timpul parallax

### ✅ **Fără Scroll Real**
- ScrollView-ul revine întotdeauna la poziția inițială (dacă se folosește ScrollView)
- Efectul este pur vizual, fără scroll real
- Funcționează perfect și fără ScrollView

## Utilizare Simplă

### 1. **Setup de Bază cu ScrollView**

```java
// În Fragment sau Activity
ScrollView scrollView = findViewById(R.id.scrollView);
View primaryView = findViewById(R.id.primary_container);    // Se întinde când gesture în jos
View secondaryView = findViewById(R.id.secondary_container); // Se comprimă când gesture în jos

// Creează instanța Universal Elastic Parallax
UniversalElasticParallax elasticParallax = new UniversalElasticParallax(
    scrollView,
    primaryView,
    secondaryView
);
```

### 1b. **Setup de Bază FĂRĂ ScrollView**

```java
// În Fragment sau Activity - cu orice View container
LinearLayout containerLayout = findViewById(R.id.container_layout);
View primaryView = findViewById(R.id.primary_container);    // Se întinde când gesture în jos
View secondaryView = findViewById(R.id.secondary_container); // Se comprimă când gesture în jos

// Creează instanța Universal Elastic Parallax
UniversalElasticParallax elasticParallax = new UniversalElasticParallax(
    containerLayout,  // Orice View poate fi container!
    primaryView,
    secondaryView
);
```

### 2. **Setup cu Configurări Personalizate**

```java
UniversalElasticParallax elasticParallax = new UniversalElasticParallax(scrollView, primaryView, secondaryView)
    .setElasticFactor(4.0f)                    // Factor de smoothness (mai mare = mai puțin elastic)
    .setPrimaryStretchLimits(1.5f, 0.5f)       // Primary: Max 150%, Min 50%
    .setSecondaryStretchLimits(1.33f, 0.5f)    // Secondary: Max 133%, Min 50%
    .setAnimationDurations(300, 280)           // Durată animații (primary, secondary)
    .setDecelerationFactors(3.0f, 2.5f)        // Factori de încetinire progresivă
    .setDisableSecondaryClicks(true);          // Dezactivează click-urile în secondary view în timpul parallax
```

## Exemple de Utilizare

### **Exemplul 1: ImageView + TextView (HomeFragment)**

```java
public class HomeFragment extends Fragment {
    private UniversalElasticParallax elasticParallax;

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        ScrollView scrollView = view.findViewById(R.id.scrollView);
        ImageView imageView = view.findViewById(R.id.parallax_image);
        TextView textView = view.findViewById(R.id.content_text);

        elasticParallax = new UniversalElasticParallax(scrollView, imageView, textView);
    }
}
```

### **Exemplul 2: CardView + LinearLayout**

```java
public class ProfileFragment extends Fragment {
    private UniversalElasticParallax elasticParallax;

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        ScrollView scrollView = view.findViewById(R.id.profile_scroll);
        CardView profileCard = view.findViewById(R.id.profile_card);
        LinearLayout contentLayout = view.findViewById(R.id.content_layout);

        elasticParallax = new UniversalElasticParallax(scrollView, profileCard, contentLayout)
            .setElasticFactor(3.0f)                    // Mai elastic
            .setPrimaryStretchLimits(1.8f, 0.3f)       // Stretch mai mare pentru CardView
            .setSecondaryStretchLimits(1.2f, 0.7f);    // Compress mai puțin pentru LinearLayout
    }
}
```

### **Exemplul 3: FrameLayout + RecyclerView**

```java
public class WorkoutFragment extends Fragment {
    private UniversalElasticParallax elasticParallax;

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        ScrollView scrollView = view.findViewById(R.id.workout_scroll);
        FrameLayout headerFrame = view.findViewById(R.id.header_frame);
        RecyclerView exercisesList = view.findViewById(R.id.exercises_recycler);

        elasticParallax = new UniversalElasticParallax(scrollView, headerFrame, exercisesList)
            .setElasticFactor(5.0f)                    // Foarte smooth
            .setAnimationDurations(400, 350)           // Animații mai lungi
            .setDecelerationFactors(4.0f, 3.0f);       // Încetinire foarte progresivă
    }
}
```

### **Exemplul 4: FĂRĂ ScrollView - LinearLayout Container**

```java
public class ExampleNoScrollFragment extends Fragment {
    private UniversalElasticParallax elasticParallax;

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Container View - LinearLayout în loc de ScrollView
        LinearLayout containerLayout = view.findViewById(R.id.container_layout);
        ImageView primaryView = view.findViewById(R.id.primary_image);
        LinearLayout secondaryView = view.findViewById(R.id.secondary_container);

        elasticParallax = new UniversalElasticParallax(containerLayout, primaryView, secondaryView)
            .setElasticFactor(3.0f)                    // Mai elastic pentru demo
            .setPrimaryStretchLimits(2.0f, 0.3f)       // Primary: Max 200%, Min 30%
            .setSecondaryStretchLimits(1.5f, 0.4f)     // Secondary: Max 150%, Min 40%
            .setAnimationDurations(350, 300)           // Animații ușor mai lungi
            .setDecelerationFactors(2.5f, 2.0f)        // Încetinire mai blândă
            .setDisableSecondaryClicks(true);          // Dezactivează click-urile în secondary
    }
}
```

### **Exemplul 5: ProfileFr - Cu Interceptare Manuală pentru Butoane**

```java
public class ProfileFr extends Fragment {
    private UniversalElasticParallax elasticParallax;

    private void setupUniversalElasticParallax(View view) {
        ScrollView scrollView = view.findViewById(R.id.scrollView);
        RelativeLayout containerView = view.findViewById(R.id.parallax_container);
        RelativeLayout secondaryView = view.findViewById(R.id.rl_profile_menu);  // Conține butoane clickable

        if (scrollView != null && containerView != null && secondaryView != null) {
            elasticParallax = new UniversalElasticParallax(scrollView, containerView, secondaryView)
                .setElasticFactor(2.0f)                    // Factor elastic
                .setPrimaryStretchLimits(2f, 0.0f)         // Primary: Max 200%, Min 0%
                .setSecondaryStretchLimits(2f, 0.0f)       // Secondary: Max 200%, Min 0%
                .setAnimationDurations(1000, 980)          // Animații lungi
                .setDecelerationFactors(3.0f, 2.0f)        // Încetinire progresivă
                .setDisableSecondaryClicks(true);          // Dezactivează click-urile în timpul parallax

            // IMPORTANT: Adaugă interceptarea manuală pentru butoane clickable
            setupManualTouchInterception(scrollView, secondaryView);
        }
    }

    /**
     * Interceptare manuală pentru a funcționa peste butoane clickable
     */
    private void setupManualTouchInterception(ScrollView scrollView, RelativeLayout secondaryView) {
        scrollView.setOnTouchListener(new View.OnTouchListener() {
            private boolean isParallaxGesture = false;
            private float startY = 0, startX = 0;
            private static final float PARALLAX_THRESHOLD = 15; // Threshold sensibil

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (isEventInSecondaryView(event, secondaryView)) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            startY = event.getY();
                            startX = event.getX();
                            isParallaxGesture = false;
                            return false; // Permite click-urile

                        case MotionEvent.ACTION_MOVE:
                            float deltaY = Math.abs(event.getY() - startY);
                            float deltaX = Math.abs(event.getX() - startX);

                            // Detectează gesture vertical (parallax)
                            if (deltaY > PARALLAX_THRESHOLD && deltaY > deltaX * 1.5) {
                                isParallaxGesture = true;
                                return true; // Consumă pentru parallax
                            }

                            if (isParallaxGesture) return true;
                            break;

                        case MotionEvent.ACTION_UP:
                            if (isParallaxGesture) {
                                isParallaxGesture = false;
                                return true;
                            }
                            break;
                    }
                }
                return false;
            }
        });
    }

    private boolean isEventInSecondaryView(MotionEvent event, View secondaryView) {
        int[] location = new int[2];
        secondaryView.getLocationOnScreen(location);
        float touchX = event.getRawX();
        float touchY = event.getRawY();

        return touchX >= location[0] && touchX <= location[0] + secondaryView.getWidth() &&
               touchY >= location[1] && touchY <= location[1] + secondaryView.getHeight();
    }
}
```

## Configurări Avansate

### **Parametri Configurabili**

| Parametru | Descriere | Valoare Implicită |
|-----------|-----------|-------------------|
| `elasticFactor` | Factor de reducere pentru smoothness (mai mare = mai puțin elastic) | `4.0f` |
| `primaryMaxStretch` | Stretch maxim pentru primary view (1.5f = 150%) | `1.5f` |
| `primaryMinCompress` | Compress minim pentru primary view (0.5f = 50%) | `0.5f` |
| `secondaryMaxStretch` | Stretch maxim pentru secondary view | `1.33f` |
| `secondaryMinCompress` | Compress minim pentru secondary view | `0.5f` |
| `primaryAnimationDuration` | Durată animație primary view (ms) | `300` |
| `secondaryAnimationDuration` | Durată animație secondary view (ms) | `280` |
| `primaryDecelerationFactor` | Factor încetinire progresivă primary | `3.0f` |
| `secondaryDecelerationFactor` | Factor încetinire progresivă secondary | `2.5f` |
| `disableSecondaryClicks` | Dezactivează click-urile în secondary view în timpul parallax | `true` |

### **Comportament Gesture**

#### **Gesture în Jos** 👇:
- **Primary View**: Se întinde (stretch) până la `primaryMaxStretch`
- **Secondary View**: Se comprimă până la `secondaryMinCompress`

#### **Gesture în Sus** 👆:
- **Primary View**: Se comprimă până la `primaryMinCompress`
- **Secondary View**: Se întinde până la `secondaryMaxStretch`

#### **La Ridicarea Degetului**:
- **Animație de revenire** cu încetinire progresivă
- **ScrollView forțat** să rămână la poziția 0
- **Reactivare click-uri** în secondary view după finalizarea animației

## Dezactivare Click-uri în Secondary View

### 🚫 **Problema Rezolvată**

Când utilizatorul face gesture-uri de parallax, există două probleme principale:

#### **1. Click-uri Accidentale**:
- **Click-uri nedorite** în timpul gesture-urilor
- **Activare accidentală** a butoanelor în timpul animației
- **Experiență utilizator confuză** când view-urile se mișcă

#### **2. Interceptarea Touch Events-urilor**:
- **View-urile clickable** (butoane, meniuri) interceptează touch events-urile
- **Gesture-urile parallax nu funcționează** când atingi zone clickable
- **Conflicte între click-uri și swipe-uri** în secondary view
- **Butoanele au prioritate** - OnTouchListener-ul lor se execută primul

### ✅ **Soluția Implementată**

`UniversalElasticParallax` + **Interceptare Manuală** rezolvă ambele probleme:

#### **1. Interceptarea Automată (UniversalElasticParallax)**:
- **Detectare gesture parallax**: Threshold de 15-20px pentru diferențiere click/swipe
- **Funcționează pentru zone non-clickable**: Spații libere, text-uri, imagini
- **Preservare click-uri**: Click-urile mici rămân funcționale

#### **2. Interceptarea Manuală (Pentru Butoane Clickable)**:
- **Setată pe ScrollView**: Prioritate mai mare decât butoanele
- **Detectare gesture vertical**: deltaY > 15px și deltaY > deltaX * 1.5
- **Consumă events pentru parallax**: Când detectează swipe vertical

#### **3. Logica Combinată**:
- **ACTION_DOWN**: Nu consumă event-ul, permite click-urile
- **ACTION_MOVE**: Dacă gesture vertical → consumă și activează parallax
- **ACTION_UP**: Dacă a fost parallax → consumă, altfel → permite click-ul
- **Dezactivare click-uri**: Opțional, pentru siguranță extra

### 🔧 **Cum Funcționează Interceptarea**

#### **Scenario 1: Click Normal** 👆
```
1. User atinge butonul → ACTION_DOWN
2. Threshold nu este depășit (< 20px)
3. Event-ul trece către buton → Click funcționează normal
```

#### **Scenario 2: Gesture Parallax** 👆👇
```
1. User atinge zona → ACTION_DOWN (nu se consumă)
2. User mișcă degetul > 20px → ACTION_MOVE
3. Event-ul este consumat și redirecționat către parallax
4. Parallax se activează, click-urile sunt ignorate
```

#### **Scenario 3: Gesture Mic** 👆
```
1. User atinge și mișcă < 20px → Nu se activează parallax
2. Event-ul trece către copii → Click-urile funcționează normal
```

### 🔧 **Configurare**

```java
// Interceptare automată + dezactivare click-uri (recomandat)
.setDisableSecondaryClicks(true)   // Dezactivează click-urile în timpul parallax

// Doar interceptare automată (fără dezactivare)
.setDisableSecondaryClicks(false)  // Permite click-urile, dar interceptează gesture-urile
```

## 🚨 Cazuri Speciale: Butoane Clickable în Secondary View

### **Problema cu Butoanele**

Când secondary view conține **butoane clickable** (RelativeLayout cu `android:background="@drawable/selector_item_profile"`), acestea interceptează touch events-urile înainte ca `UniversalElasticParallax` să le proceseze.

### **Soluția: Interceptare Manuală**

Pentru cazuri ca **ProfileFr** cu multe butoane clickable, adaugă interceptarea manuală:

```java
// 1. Setup normal UniversalElasticParallax
elasticParallax = new UniversalElasticParallax(scrollView, containerView, secondaryView)
    .setElasticFactor(2.0f)
    .setDisableSecondaryClicks(true);

// 2. IMPORTANT: Adaugă interceptarea manuală pentru butoane
setupManualTouchInterception(scrollView, secondaryView);
```

### **Implementarea Interceptării Manuale**

```java
private void setupManualTouchInterception(ScrollView scrollView, RelativeLayout secondaryView) {
    scrollView.setOnTouchListener(new View.OnTouchListener() {
        private boolean isParallaxGesture = false;
        private float startY = 0, startX = 0;
        private static final float PARALLAX_THRESHOLD = 15; // Sensibilitate

        @Override
        public boolean onTouch(View v, MotionEvent event) {
            if (isEventInSecondaryView(event, secondaryView)) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        startY = event.getY();
                        startX = event.getX();
                        isParallaxGesture = false;
                        return false; // Permite click-urile

                    case MotionEvent.ACTION_MOVE:
                        float deltaY = Math.abs(event.getY() - startY);
                        float deltaX = Math.abs(event.getX() - startX);

                        // Detectează gesture vertical (nu diagonal)
                        if (deltaY > PARALLAX_THRESHOLD && deltaY > deltaX * 1.5) {
                            isParallaxGesture = true;
                            return true; // Consumă pentru parallax
                        }

                        if (isParallaxGesture) return true;
                        break;

                    case MotionEvent.ACTION_UP:
                        if (isParallaxGesture) {
                            isParallaxGesture = false;
                            return true;
                        }
                        break;
                }
            }
            return false;
        }
    });
}
```

## Layout Requirements

### **XML Layout Minim**

```xml
<ScrollView
    android:id="@+id/scrollView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Primary View - se întinde când gesture în jos -->
        <ImageView
            android:id="@+id/primary_view"
            android:layout_width="match_parent"
            android:layout_height="300dp" />

        <!-- Secondary View - se comprimă când gesture în jos -->
        <TextView
            android:id="@+id/secondary_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>
</ScrollView>
```

## Avantaje

### ✅ **Reutilizabil**
- O singură clasă pentru toate fragmentele/activitățile
- Nu mai trebuie să rescrii logica pentru fiecare fragment

### ✅ **Flexibil**
- Funcționează cu orice tip de View-uri
- Configurări personalizabile pentru fiecare caz de utilizare

### ✅ **Performant**
- Animații optimizate cu `ValueAnimator`
- Încetinire progresivă naturală cu `DecelerateInterpolator`

### ✅ **Ușor de Integrat**
- Setup în 2-3 linii de cod
- Nu necesită modificări în layout-uri existente

### ✅ **Avantaje FĂRĂ ScrollView**
- **Nu există probleme cu scroll-ul** - nu trebuie să blochezi scroll-ul
- **Mai simplu de implementat** - nu ai nevoie de ScrollView în layout
- **Funcționează cu orice layout** - LinearLayout, FrameLayout, ConstraintLayout, etc.
- **Performance mai bun** - fără scroll listener și fără verificări de scroll
- **Mai flexibil pentru design-uri complexe** - nu ești limitat de ScrollView

### ✅ **Avantaje Interceptare Touch Events**
- **Rezolvă conflictele** între click-uri și gesture-uri parallax
- **Detectare inteligentă** - diferențiază între click și swipe
- **Preservă funcționalitatea** - click-urile mici rămân funcționale
- **Threshold configurabil** - 20px implicit pentru detectarea gesture-urilor
- **Compatibil cu orice layout** - funcționează cu butoane, meniuri, liste

### ✅ **Avantaje Dezactivare Click-uri**
- **Previne click-uri accidentale** în timpul gesture-urilor
- **Experiență utilizator mai bună** - nu se activează butoane nedorit
- **Funcționează recursiv** - dezactivează toate view-urile copil
- **Reactivare automată** - click-urile revin după animație
- **Configurabil** - poți dezactiva funcționalitatea dacă nu e necesară

## Concluzie

`UniversalElasticParallax` oferă o soluție completă și universală pentru efectul elastic parallax în aplicațiile Android, fiind aplicabil pe orice 2 View-uri/containere **cu sau fără ScrollView**, cu configurări personalizabile și animații smooth cu încetinire progresivă.

### 🎯 **Recomandări de Utilizare**

- **Cu ScrollView**: Când ai nevoie de scroll real în alte părți ale layout-ului
- **Fără ScrollView**: Pentru efecte pure de parallax fără complicații de scroll
- **LinearLayout/FrameLayout**: Pentru layout-uri simple și performance maxim
- **ConstraintLayout**: Pentru design-uri complexe cu multe relații între view-uri
